#!/usr/bin/env python
"""
Test script to check imports.
"""
import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_imports")

def main():
    """Main function to test imports."""
    logger.info("Testing imports...")
    
    # Add the project root directory to the Python path
    project_root = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, project_root)
    
    # Test importing modules
    try:
        logger.info("Importing flet...")
        import flet
        logger.info(f"Flet version: {flet.__version__}")
    except ImportError as e:
        logger.error(f"Error importing flet: {str(e)}")
    
    try:
        logger.info("Importing src.utils.logger...")
        from src.utils.logger import setup_logger
        logger.info("Successfully imported setup_logger")
    except ImportError as e:
        logger.error(f"Error importing setup_logger: {str(e)}")
    
    try:
        logger.info("Importing src.database.db_manager...")
        from src.database.db_manager import DatabaseManager
        logger.info("Successfully imported DatabaseManager")
    except ImportError as e:
        logger.error(f"Error importing DatabaseManager: {str(e)}")
    
    try:
        logger.info("Importing src.auth.auth_manager...")
        from src.auth.auth_manager import AuthManager
        logger.info("Successfully imported AuthManager")
    except ImportError as e:
        logger.error(f"Error importing AuthManager: {str(e)}")
    
    logger.info("Import tests completed.")

if __name__ == "__main__":
    main()