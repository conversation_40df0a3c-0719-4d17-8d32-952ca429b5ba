# Science Laboratory Management System
# Installation and Deployment Guide

## Table of Contents
1. [Introduction](#introduction)
2. [System Requirements](#system-requirements)
3. [Pre-Installation Steps](#pre-installation-steps)
4. [Installation Methods](#installation-methods)
5. [Configuration](#configuration)
6. [Database Setup](#database-setup)
7. [Web Server Configuration](#web-server-configuration)
8. [Initial System Setup](#initial-system-setup)
9. [Upgrading](#upgrading)
10. [Deployment Scenarios](#deployment-scenarios)
11. [Troubleshooting](#troubleshooting)

---

## Introduction

This guide provides detailed instructions for installing and deploying the Science Laboratory Management System. It covers various installation methods, configuration options, and deployment scenarios to help you set up the system according to your specific requirements.

### About the System

The Science Laboratory Management System is a comprehensive solution designed to streamline laboratory operations, inventory management, equipment tracking, and experiment documentation. The system consists of several integrated modules that work together to provide a complete laboratory management solution.

### Supported Platforms

The system can be installed on:
- Windows Server 2016/2019/2022
- Linux (Ubuntu 18.04+, CentOS 7+, Debian 10+)
- macOS (for development purposes)

---

## System Requirements

### Hardware Requirements

#### Minimum Requirements
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 50GB
- **Network**: 100Mbps Ethernet

#### Recommended Requirements
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 100GB+ SSD
- **Network**: 1Gbps Ethernet

### Software Requirements

#### Operating System
- **Windows**: Windows Server 2016/2019/2022 or Windows 10/11
- **Linux**: Ubuntu 18.04+, CentOS 7+, Debian 10+
- **macOS**: Catalina (10.15)+ (development only)

#### Required Software
- **Python**: 3.8+
- **Database**: SQLite (included), MySQL 5.7+, PostgreSQL 10+
- **Web Server**: Nginx or Apache (for production)
- **Additional Libraries**:
  - OpenSSL
  - libffi
  - zlib
  - libjpeg
  - libpng

#### Optional Software
- **Redis**: For caching and background tasks
- **Docker**: For containerized deployment
- **Git**: For version control and updates

---

## Pre-Installation Steps

### Preparing the Operating System

#### Windows
1. Install the latest updates:
   ```
   wuauclt /detectnow /updatenow
   ```
2. Install required software:
   - [Python](https://www.python.org/downloads/)
   - [Git](https://git-scm.com/download/win)
   - [Visual C++ Build Tools](https://visualstudio.microsoft.com/visual-cpp-build-tools/)

#### Linux (Ubuntu/Debian)
1. Update the system:
   ```bash
   sudo apt update
   sudo apt upgrade -y
   ```
2. Install required packages:
   ```bash
   sudo apt install -y python3 python3-pip python3-venv python3-dev \
   build-essential libssl-dev libffi-dev \
   libjpeg-dev libpng-dev zlib1g-dev \
   git curl
   ```

#### Linux (CentOS/RHEL)
1. Update the system:
   ```bash
   sudo yum update -y
   ```
2. Install required packages:
   ```bash
   sudo yum install -y python3 python3-pip python3-devel \
   gcc openssl-devel libffi-devel \
   libjpeg-devel libpng-devel zlib-devel \
   git curl
   ```

### Creating a Service Account

It's recommended to create a dedicated service account for running the application:

#### Windows
1. Open Computer Management
2. Navigate to Local Users and Groups > Users
3. Create a new user (e.g., `slms-service`)
4. Add the user to the appropriate groups

#### Linux
```bash
sudo useradd -m -s /bin/bash slms-service
sudo passwd slms-service
```

### Setting Up Python Virtual Environment

Create a virtual environment to isolate the application dependencies:

#### Windows
```
mkdir C:\slms
cd C:\slms
python -m venv venv
venv\Scripts\activate
```

#### Linux
```bash
mkdir -p /opt/slms
cd /opt/slms
python3 -m venv venv
source venv/bin/activate
```

---

## Installation Methods

### Standard Installation

#### Downloading the Package
1. Download the latest release from the official website or repository
2. Extract the package to your installation directory:
   - Windows: `C:\slms`
   - Linux: `/opt/slms`

#### Installing Dependencies
1. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - Linux: `source venv/bin/activate`
2. Install the required packages:
   ```
   pip install -r requirements.txt
   ```

#### Running the Installation Script
1. Run the installation script:
   ```
   python install.py
   ```
2. Follow the on-screen instructions to complete the installation

### Docker Installation

#### Prerequisites
1. Install Docker and Docker Compose:
   - [Docker Installation Guide](https://docs.docker.com/get-docker/)
   - [Docker Compose Installation Guide](https://docs.docker.com/compose/install/)

#### Setting Up Docker Deployment
1. Download the Docker configuration files:
   ```bash
   git clone https://github.com/example/slms-docker.git
   cd slms-docker
   ```
2. Configure the environment variables:
   ```bash
   cp .env.example .env
   # Edit .env file with your settings
   ```
3. Start the containers:
   ```bash
   docker-compose up -d
   ```

### Installation from Source

#### Cloning the Repository
1. Clone the repository:
   ```bash
   git clone https://github.com/example/slms.git
   cd slms
   ```
2. Check out the desired version:
   ```bash
   git checkout v1.0.0
   ```

#### Installing Dependencies
1. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - Linux: `source venv/bin/activate`
2. Install the required packages:
   ```
   pip install -r requirements.txt
   ```

#### Building the Application
1. Build the frontend assets:
   ```bash
   cd frontend
   npm install
   npm run build
   cd ..
   ```
2. Run the installation script:
   ```
   python install.py --dev
   ```

---

## Configuration

### Configuration Files

The system uses the following configuration files:

- `config.json`: Main configuration file
- `database.json`: Database connection settings
- `integrations.json`: Integration settings
- `logging.json`: Logging configuration
- `security.json`: Security settings

These files are located in:
- Windows: `C:\slms\config`
- Linux: `/opt/slms/config`

### Environment Variables

The system supports the following environment variables:

- `SLMS_CONFIG_PATH`: Path to configuration directory
- `SLMS_DB_TYPE`: Database type (sqlite, mysql, postgresql)
- `SLMS_DB_HOST`: Database host
- `SLMS_DB_PORT`: Database port
- `SLMS_DB_NAME`: Database name
- `SLMS_DB_USER`: Database username
- `SLMS_DB_PASSWORD`: Database password
- `SLMS_SECRET_KEY`: Application secret key
- `SLMS_DEBUG`: Enable debug mode (true/false)

### Basic Configuration

#### Setting Up the Main Configuration

1. Copy the example configuration:
   ```bash
   cp config.json.example config.json
   ```
2. Edit the configuration file:
   ```bash
   nano config.json
   ```
3. Configure the basic settings:
   ```json
   {
     "system": {
       "name": "Science Laboratory Management System",
       "debug": false,
       "timezone": "UTC",
       "language": "en",
       "secret_key": "generate-a-secure-random-key"
     },
     "server": {
       "host": "0.0.0.0",
       "port": 8000,
       "workers": 4,
       "max_upload_size": 50000000
     }
   }
   ```

#### Configuring Email Settings

1. Edit the configuration file:
   ```bash
   nano config.json
   ```
2. Add email settings:
   ```json
   {
     "email": {
       "enabled": true,
       "smtp_server": "smtp.example.com",
       "smtp_port": 587,
       "smtp_use_tls": true,
       "smtp_username": "<EMAIL>",
       "smtp_password": "your-password",
       "default_sender": "<EMAIL>",
       "default_sender_name": "Science Lab Management System"
     }
   }
   ```

#### Configuring File Storage

1. Edit the configuration file:
   ```bash
   nano config.json
   ```
2. Add file storage settings:
   ```json
   {
     "file_storage": {
       "type": "local",
       "local": {
         "path": "data/files"
       },
       "s3": {
         "bucket": "your-bucket-name",
         "access_key": "your-access-key",
         "secret_key": "your-secret-key",
         "region": "us-east-1"
       }
     }
   }
   ```

---

## Database Setup

### Supported Databases

The system supports the following databases:
- SQLite (included, suitable for small deployments)
- MySQL 5.7+
- PostgreSQL 10+

### SQLite Setup

1. Edit the database configuration:
   ```bash
   nano database.json
   ```
2. Configure SQLite:
   ```json
   {
     "type": "sqlite",
     "sqlite": {
       "database": "data/lab_db.db"
     }
   }
   ```
3. The database file will be created automatically during initialization

### MySQL Setup

#### Creating the Database
1. Log in to MySQL:
   ```bash
   mysql -u root -p
   ```
2. Create the database and user:
   ```sql
   CREATE DATABASE slms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'slms_user'@'localhost' IDENTIFIED BY 'your-password';
   GRANT ALL PRIVILEGES ON slms.* TO 'slms_user'@'localhost';
   FLUSH PRIVILEGES;
   EXIT;
   ```

#### Configuring the Application
1. Edit the database configuration:
   ```bash
   nano database.json
   ```
2. Configure MySQL:
   ```json
   {
     "type": "mysql",
     "mysql": {
       "host": "localhost",
       "port": 3306,
       "database": "slms",
       "user": "slms_user",
       "password": "your-password",
       "charset": "utf8mb4"
     }
   }
   ```

### PostgreSQL Setup

#### Creating the Database
1. Log in to PostgreSQL:
   ```bash
   sudo -u postgres psql
   ```
2. Create the database and user:
   ```sql
   CREATE USER slms_user WITH PASSWORD 'your-password';
   CREATE DATABASE slms OWNER slms_user;
   \q
   ```

#### Configuring the Application
1. Edit the database configuration:
   ```bash
   nano database.json
   ```
2. Configure PostgreSQL:
   ```json
   {
     "type": "postgresql",
     "postgresql": {
       "host": "localhost",
       "port": 5432,
       "database": "slms",
       "user": "slms_user",
       "password": "your-password"
     }
   }
   ```

### Database Initialization

1. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - Linux: `source venv/bin/activate`
2. Run the database initialization script:
   ```
   python manage.py init_db
   ```
3. Create the initial admin user:
   ```
   python manage.py create_admin
   ```

---

## Web Server Configuration

### Development Server

For development or testing purposes, you can use the built-in development server:

```bash
python manage.py runserver
```

The server will be available at http://localhost:8000

### Production Deployment with Gunicorn and Nginx

#### Installing Gunicorn
1. Activate the virtual environment:
   ```bash
   source venv/bin/activate
   ```
2. Install Gunicorn:
   ```bash
   pip install gunicorn
   ```

#### Creating a Gunicorn Service (Linux)
1. Create a systemd service file:
   ```bash
   sudo nano /etc/systemd/system/slms.service
   ```
2. Add the following content:
   ```
   [Unit]
   Description=Science Laboratory Management System
   After=network.target

   [Service]
   User=slms-service
   Group=slms-service
   WorkingDirectory=/opt/slms
   Environment="PATH=/opt/slms/venv/bin"
   ExecStart=/opt/slms/venv/bin/gunicorn app:app -w 4 -k uvicorn.workers.UvicornWorker -b 127.0.0.1:8000

   [Install]
   WantedBy=multi-user.target
   ```
3. Enable and start the service:
   ```bash
   sudo systemctl enable slms
   sudo systemctl start slms
   ```

#### Configuring Nginx

1. Install Nginx:
   ```bash
   sudo apt install nginx
   ```
2. Create an Nginx configuration file:
   ```bash
   sudo nano /etc/nginx/sites-available/slms
   ```
3. Add the following content:
   ```
   server {
       listen 80;
       server_name your-domain.com;

       location / {
           proxy_pass http://127.0.0.1:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }

       location /static/ {
           alias /opt/slms/static/;
           expires 30d;
       }

       location /media/ {
           alias /opt/slms/media/;
           expires 30d;
       }
   }
   ```
4. Enable the site:
   ```bash
   sudo ln -s /etc/nginx/sites-available/slms /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl restart nginx
   ```

### Production Deployment with Apache

#### Installing Apache and mod_wsgi
1. Install Apache and mod_wsgi:
   ```bash
   sudo apt install apache2 libapache2-mod-wsgi-py3
   ```

#### Configuring Apache
1. Create an Apache configuration file:
   ```bash
   sudo nano /etc/apache2/sites-available/slms.conf
   ```
2. Add the following content:
   ```
   <VirtualHost *:80>
       ServerName your-domain.com
       ServerAdmin <EMAIL>

       WSGIDaemonProcess slms python-home=/opt/slms/venv python-path=/opt/slms
       WSGIProcessGroup slms
       WSGIScriptAlias / /opt/slms/wsgi.py

       <Directory /opt/slms>
           Require all granted
       </Directory>

       Alias /static/ /opt/slms/static/
       <Directory /opt/slms/static>
           Require all granted
       </Directory>

       Alias /media/ /opt/slms/media/
       <Directory /opt/slms/media>
           Require all granted
       </Directory>

       ErrorLog ${APACHE_LOG_DIR}/slms-error.log
       CustomLog ${APACHE_LOG_DIR}/slms-access.log combined
   </VirtualHost>
   ```
3. Enable the site:
   ```bash
   sudo a2ensite slms.conf
   sudo systemctl restart apache2
   ```

---

## Initial System Setup

### First-Time Login

1. Access the system using the default URL:
   - Development: http://localhost:8000
   - Production: http://your-domain.com
2. Log in with the default administrator credentials:
   - Username: admin
   - Password: admin123
3. You will be prompted to change the default password

### Running the Setup Wizard

1. After logging in, the setup wizard will start automatically
2. Follow the steps to configure:
   - System name and branding
   - Email settings
   - User roles and permissions
   - Default categories and settings
3. Complete the wizard to finalize the setup

### Creating Initial Data

#### Setting Up Lab Rooms
1. Navigate to "Administration" > "Lab Management" > "Rooms"
2. Click "Add Room"
3. Enter room details:
   - Name
   - Number
   - Location
   - Capacity
   - Equipment
4. Click "Save Room"

#### Setting Up Inventory Categories
1. Navigate to "Administration" > "Inventory" > "Categories"
2. Click "Add Category"
3. Enter category details:
   - Name
   - Description
   - Parent category (if applicable)
4. Click "Save Category"

#### Setting Up Equipment Types
1. Navigate to "Administration" > "Equipment" > "Types"
2. Click "Add Type"
3. Enter type details:
   - Name
   - Category
   - Maintenance requirements
   - Calibration requirements
4. Click "Save Type"

---

## Upgrading

### Backup Before Upgrading

Always create a backup before upgrading:

1. Back up the database:
   - SQLite: Copy the database file
   - MySQL: `mysqldump -u slms_user -p slms > slms_backup.sql`
   - PostgreSQL: `pg_dump -U slms_user slms > slms_backup.sql`
2. Back up the configuration files:
   ```bash
   cp -r /opt/slms/config /opt/slms/config_backup
   ```
3. Back up uploaded files:
   ```bash
   cp -r /opt/slms/media /opt/slms/media_backup
   ```

### Standard Upgrade

1. Download the new version
2. Stop the application:
   ```bash
   sudo systemctl stop slms
   ```
3. Extract the new version over the existing installation
4. Activate the virtual environment:
   ```bash
   source venv/bin/activate
   ```
5. Install or update dependencies:
   ```bash
   pip install -r requirements.txt
   ```
6. Run the database migration:
   ```bash
   python manage.py migrate
   ```
7. Restart the application:
   ```bash
   sudo systemctl start slms
   ```

### Docker Upgrade

1. Pull the latest image:
   ```bash
   docker-compose pull
   ```
2. Stop and remove the existing containers:
   ```bash
   docker-compose down
   ```
3. Start the new containers:
   ```bash
   docker-compose up -d
   ```

---

## Deployment Scenarios

### Single-Server Deployment

This is the simplest deployment scenario, suitable for small to medium-sized organizations:

1. Install all components on a single server:
   - Web application
   - Database
   - File storage
2. Configure the web server (Nginx or Apache)
3. Set up SSL/TLS for secure access

### Multi-Server Deployment

For larger organizations or higher availability requirements:

1. **Web Server(s)**:
   - Install the web application
   - Configure load balancing if using multiple web servers
2. **Database Server**:
   - Install and configure the database
   - Set up replication for high availability
3. **File Storage Server**:
   - Set up a dedicated server for file storage
   - Configure the application to use this server
4. **Cache Server** (optional):
   - Install Redis or Memcached
   - Configure the application to use the cache server

### Cloud Deployment

#### AWS Deployment

1. **EC2 Instances**:
   - Launch EC2 instances for the web application
   - Configure security groups
2. **RDS**:
   - Create an RDS instance for the database
3. **S3**:
   - Create an S3 bucket for file storage
4. **Load Balancer**:
   - Set up an Application Load Balancer
5. **Route 53**:
   - Configure DNS settings

#### Azure Deployment

1. **Virtual Machines**:
   - Create VMs for the web application
   - Configure network security groups
2. **Azure Database**:
   - Set up Azure Database for MySQL or PostgreSQL
3. **Blob Storage**:
   - Create a storage account for file storage
4. **Application Gateway**:
   - Set up an Application Gateway for load balancing
5. **DNS**:
   - Configure DNS settings

#### Google Cloud Deployment

1. **Compute Engine**:
   - Create VM instances for the web application
   - Configure firewall rules
2. **Cloud SQL**:
   - Set up Cloud SQL for the database
3. **Cloud Storage**:
   - Create a bucket for file storage
4. **Load Balancing**:
   - Set up HTTP(S) Load Balancing
5. **Cloud DNS**:
   - Configure DNS settings

---

## Troubleshooting

### Installation Issues

#### Python Dependency Errors

**Issue**: Errors when installing Python dependencies

**Solution**:
1. Ensure you have the required development libraries:
   ```bash
   sudo apt install python3-dev build-essential libssl-dev libffi-dev
   ```
2. Upgrade pip:
   ```bash
   pip install --upgrade pip
   ```
3. Try installing dependencies one by one to identify the problematic package

#### Database Connection Errors

**Issue**: Unable to connect to the database

**Solution**:
1. Verify database credentials in the configuration
2. Check if the database server is running:
   ```bash
   sudo systemctl status mysql
   ```
3. Ensure the database and user exist:
   ```bash
   mysql -u root -p
   SHOW DATABASES;
   SELECT User FROM mysql.user;
   ```
4. Check network connectivity and firewall settings

### Web Server Issues

#### Nginx/Apache Configuration Errors

**Issue**: Web server returns 502 Bad Gateway or 500 Internal Server Error

**Solution**:
1. Check the web server error logs:
   - Nginx: `/var/log/nginx/error.log`
   - Apache: `/var/log/apache2/error.log`
2. Verify the application is running:
   ```bash
   sudo systemctl status slms
   ```
3. Check the application logs:
   ```bash
   tail -f /opt/slms/logs/app.log
   ```
4. Test the application directly:
   ```bash
   curl http://localhost:8000
   ```

#### Permission Issues

**Issue**: File permission errors

**Solution**:
1. Check ownership of application files:
   ```bash
   ls -la /opt/slms
   ```
2. Set correct permissions:
   ```bash
   sudo chown -R slms-service:slms-service /opt/slms
   sudo chmod -R 755 /opt/slms
   ```
3. Ensure the web server has access to the application files

### Application Issues

#### Application Won't Start

**Issue**: The application service fails to start

**Solution**:
1. Check the service status:
   ```bash
   sudo systemctl status slms
   ```
2. View the service logs:
   ```bash
   sudo journalctl -u slms
   ```
3. Try running the application manually:
   ```bash
   cd /opt/slms
   source venv/bin/activate
   python manage.py runserver
   ```
4. Check for syntax errors in the configuration files

#### Database Migration Errors

**Issue**: Database migration fails

**Solution**:
1. Check the migration logs:
   ```bash
   tail -f /opt/slms/logs/app.log
   ```
2. Try running migrations manually:
   ```bash
   cd /opt/slms
   source venv/bin/activate
   python manage.py migrate --fake-initial
   ```
3. If necessary, restore from backup and try again

### Getting Help

If you encounter issues not covered in this guide:

1. Check the [official documentation](https://example.com/docs)
2. Visit the [community forum](https://example.com/forum)
3. Submit a support ticket through the [support portal](https://example.com/support)
4. Contact the development <NAME_EMAIL>