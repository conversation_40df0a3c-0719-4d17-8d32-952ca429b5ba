"""
FastAPI application for Science Laboratory Management System REST API.
"""
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os
import sys
from typing import Optional, List
import logging

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from src.database.db_manager import DatabaseManager
from src.auth.auth_manager import AuthManager
from src.models.user_model import UserModel
from src.models.inventory_model import InventoryModel
from src.models.experiment_model import ExperimentModel
from src.api.schemas import *
from src.api.auth import get_current_user, create_access_token
from src.utils.logger import setup_logger

# Setup logging
logger = setup_logger("api")

# Initialize FastAPI app
app = FastAPI(
    title="Science Laboratory Management System API",
    description="REST API for managing laboratory operations, inventory, and experiments",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database and models
db_manager = DatabaseManager()
auth_manager = AuthManager(db_manager)
user_model = UserModel(db_manager)
inventory_model = InventoryModel(db_manager)
experiment_model = ExperimentModel(db_manager)

# Security
security = HTTPBearer()


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


@app.get("/api/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "version": "1.0.0"}


# Authentication endpoints
@app.post("/api/auth/login", response_model=TokenResponse)
async def login(credentials: LoginRequest):
    """Authenticate user and return access token."""
    try:
        success, user = auth_manager.login(credentials.username, credentials.password)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )

        access_token = create_access_token(data={"sub": user["username"]})
        return TokenResponse(
            access_token=access_token,
            token_type="bearer",
            user=UserResponse(**user)
        )
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@app.post("/api/auth/logout")
async def logout(current_user: dict = Depends(get_current_user)):
    """Logout current user."""
    auth_manager.logout()
    return {"message": "Successfully logged out"}


# User management endpoints
@app.get("/api/users", response_model=List[UserResponse])
async def get_users(current_user: dict = Depends(get_current_user)):
    """Get all users (admin only)."""
    if current_user["role"] != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )

    users = user_model.get_all_users()
    return [UserResponse(**user) for user in users]


@app.post("/api/users", response_model=UserResponse)
async def create_user(
    user_data: CreateUserRequest,
    current_user: dict = Depends(get_current_user)
):
    """Create a new user (admin only)."""
    if current_user["role"] != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )

    user_id, error = user_model.create_user(
        username=user_data.username,
        password=user_data.password,
        email=user_data.email,
        full_name=user_data.full_name,
        role=user_data.role,
        created_by=current_user["id"]
    )

    if error:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error
        )

    user = user_model.get_user_by_id(user_id)
    return UserResponse(**user)


@app.get("/api/users/me", response_model=UserResponse)
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """Get current user information."""
    return UserResponse(**current_user)


# Inventory endpoints
@app.get("/api/inventory", response_model=List[InventoryItemResponse])
async def get_inventory(current_user: dict = Depends(get_current_user)):
    """Get all inventory items."""
    items = inventory_model.get_all_items()
    return [InventoryItemResponse(**item) for item in items]


@app.post("/api/inventory", response_model=InventoryItemResponse)
async def create_inventory_item(
    item_data: CreateInventoryItemRequest,
    current_user: dict = Depends(get_current_user)
):
    """Create a new inventory item."""
    item_id = inventory_model.create_item(
        item_data.dict(exclude_unset=True),
        current_user["id"]
    )

    item = inventory_model.get_item_by_id(item_id)
    return InventoryItemResponse(**item)


@app.get("/api/inventory/{item_id}", response_model=InventoryItemResponse)
async def get_inventory_item(
    item_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Get a specific inventory item."""
    item = inventory_model.get_item_by_id(item_id)
    if not item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Item not found"
        )
    return InventoryItemResponse(**item)


@app.put("/api/inventory/{item_id}", response_model=InventoryItemResponse)
async def update_inventory_item(
    item_id: int,
    item_data: UpdateInventoryItemRequest,
    current_user: dict = Depends(get_current_user)
):
    """Update an inventory item."""
    success = inventory_model.update_item(
        item_id,
        item_data.dict(exclude_unset=True),
        current_user["id"]
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Item not found"
        )

    item = inventory_model.get_item_by_id(item_id)
    return InventoryItemResponse(**item)


@app.delete("/api/inventory/{item_id}")
async def delete_inventory_item(
    item_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Delete an inventory item."""
    success = inventory_model.delete_item(item_id, current_user["id"])
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Item not found"
        )
    return {"message": "Item deleted successfully"}


# Experiment endpoints
@app.get("/api/experiments", response_model=List[ExperimentResponse])
async def get_experiments(current_user: dict = Depends(get_current_user)):
    """Get all experiments."""
    experiments = experiment_model.get_all_experiments()
    return [ExperimentResponse(**exp) for exp in experiments]


@app.post("/api/experiments", response_model=ExperimentResponse)
async def create_experiment(
    experiment_data: CreateExperimentRequest,
    current_user: dict = Depends(get_current_user)
):
    """Create a new experiment."""
    experiment_id = experiment_model.create_experiment(
        experiment_data.dict(exclude_unset=True),
        current_user["id"]
    )

    experiment = experiment_model.get_experiment_by_id(experiment_id)
    return ExperimentResponse(**experiment)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
