import os
import sys
import traceback

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def main():
    """Main function to run the application."""
    try:
        print("Starting Science Laboratory Management System...")
        
        # Import the application
        print("Importing lab_management_app_with_auth...")
        import lab_management_app_with_auth
        
        # Run the application
        print("Running the application...")
        lab_management_app_with_auth.main()
        
        print("Application exited successfully.")
    except Exception as e:
        print(f"Error running application: {str(e)}")
        print("Traceback:")
        traceback.print_exc()

if __name__ == "__main__":
    main()