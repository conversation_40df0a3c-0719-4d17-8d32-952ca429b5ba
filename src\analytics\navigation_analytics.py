"""
Navigation Analytics System
Tracks and analyzes navigation patterns and user behavior
"""
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import sqlite3


@dataclass
class NavigationEvent:
    """Represents a navigation event."""
    user_id: str
    session_id: str
    timestamp: str
    from_route: Optional[str]
    to_route: str
    method: str  # 'click', 'keyboard', 'search', 'direct'
    device_type: str  # 'mobile', 'tablet', 'desktop'
    duration_on_previous: Optional[int]  # seconds spent on previous page


class NavigationAnalytics:
    """
    Tracks and analyzes navigation patterns for optimization.
    """
    
    def __init__(self, db_path: str = "data/navigation_analytics.db"):
        """
        Initialize navigation analytics.
        
        Args:
            db_path: Path to SQLite database for storing analytics
        """
        self.db_path = db_path
        self.session_data = {}  # Track current session data
        self.current_sessions = {}  # Track active sessions
        
        # Ensure data directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # Initialize database
        self._init_database()
    
    def _init_database(self):
        """Initialize the analytics database."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Navigation events table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS navigation_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    session_id TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    from_route TEXT,
                    to_route TEXT NOT NULL,
                    method TEXT NOT NULL,
                    device_type TEXT NOT NULL,
                    duration_on_previous INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # User sessions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_sessions (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    device_type TEXT NOT NULL,
                    total_navigation_events INTEGER DEFAULT 0,
                    unique_pages_visited INTEGER DEFAULT 0,
                    session_duration INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Page performance table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS page_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    route TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    session_id TEXT NOT NULL,
                    load_time INTEGER,
                    time_spent INTEGER,
                    interactions INTEGER DEFAULT 0,
                    timestamp TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes for better performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_nav_user_time ON navigation_events(user_id, timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_nav_route ON navigation_events(to_route)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_session_user ON user_sessions(user_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_perf_route ON page_performance(route)")
            
            conn.commit()
    
    def start_session(self, user_id: str, session_id: str, device_type: str):
        """Start tracking a user session."""
        self.current_sessions[session_id] = {
            'user_id': user_id,
            'start_time': datetime.now().isoformat(),
            'device_type': device_type,
            'current_route': None,
            'route_start_time': None,
            'pages_visited': set(),
        }
        
        # Store in database
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO user_sessions 
                (session_id, user_id, start_time, device_type)
                VALUES (?, ?, ?, ?)
            """, (session_id, user_id, self.current_sessions[session_id]['start_time'], device_type))
            conn.commit()
    
    def track_navigation(self, user_id: str, session_id: str, to_route: str, 
                        method: str = 'click', from_route: str = None):
        """Track a navigation event."""
        timestamp = datetime.now().isoformat()
        
        # Get session data
        session = self.current_sessions.get(session_id, {})
        device_type = session.get('device_type', 'desktop')
        
        # Calculate time spent on previous page
        duration_on_previous = None
        if session.get('route_start_time') and from_route:
            start_time = datetime.fromisoformat(session['route_start_time'])
            duration_on_previous = int((datetime.now() - start_time).total_seconds())
        
        # Create navigation event
        event = NavigationEvent(
            user_id=user_id,
            session_id=session_id,
            timestamp=timestamp,
            from_route=from_route,
            to_route=to_route,
            method=method,
            device_type=device_type,
            duration_on_previous=duration_on_previous
        )
        
        # Store in database
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO navigation_events 
                (user_id, session_id, timestamp, from_route, to_route, method, device_type, duration_on_previous)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (event.user_id, event.session_id, event.timestamp, event.from_route, 
                  event.to_route, event.method, event.device_type, event.duration_on_previous))
            conn.commit()
        
        # Update session data
        if session_id in self.current_sessions:
            self.current_sessions[session_id]['current_route'] = to_route
            self.current_sessions[session_id]['route_start_time'] = timestamp
            self.current_sessions[session_id]['pages_visited'].add(to_route)
    
    def end_session(self, session_id: str):
        """End a user session."""
        if session_id not in self.current_sessions:
            return
        
        session = self.current_sessions[session_id]
        end_time = datetime.now().isoformat()
        start_time = datetime.fromisoformat(session['start_time'])
        session_duration = int((datetime.now() - start_time).total_seconds())
        
        # Update session in database
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE user_sessions 
                SET end_time = ?, session_duration = ?, unique_pages_visited = ?
                WHERE session_id = ?
            """, (end_time, session_duration, len(session['pages_visited']), session_id))
            conn.commit()
        
        # Remove from active sessions
        del self.current_sessions[session_id]
    
    def track_page_performance(self, user_id: str, session_id: str, route: str, 
                             load_time: int, time_spent: int, interactions: int = 0):
        """Track page performance metrics."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO page_performance 
                (route, user_id, session_id, load_time, time_spent, interactions, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (route, user_id, session_id, load_time, time_spent, interactions, 
                  datetime.now().isoformat()))
            conn.commit()
    
    def get_popular_routes(self, days: int = 30, limit: int = 10) -> List[Dict[str, Any]]:
        """Get most popular routes."""
        since_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT to_route, COUNT(*) as visits, 
                       COUNT(DISTINCT user_id) as unique_users,
                       AVG(duration_on_previous) as avg_time_spent
                FROM navigation_events 
                WHERE timestamp >= ? AND to_route IS NOT NULL
                GROUP BY to_route 
                ORDER BY visits DESC 
                LIMIT ?
            """, (since_date, limit))
            
            results = []
            for row in cursor.fetchall():
                results.append({
                    'route': row[0],
                    'visits': row[1],
                    'unique_users': row[2],
                    'avg_time_spent': round(row[3] or 0, 2)
                })
            
            return results
    
    def get_navigation_flow(self, days: int = 30) -> Dict[str, Dict[str, int]]:
        """Get navigation flow between pages."""
        since_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT from_route, to_route, COUNT(*) as count
                FROM navigation_events 
                WHERE timestamp >= ? AND from_route IS NOT NULL
                GROUP BY from_route, to_route
                ORDER BY count DESC
            """, (since_date,))
            
            flow = defaultdict(dict)
            for row in cursor.fetchall():
                flow[row[0]][row[1]] = row[2]
            
            return dict(flow)
    
    def get_user_journey(self, user_id: str, session_id: str = None) -> List[Dict[str, Any]]:
        """Get user's navigation journey."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            query = """
                SELECT timestamp, from_route, to_route, method, duration_on_previous
                FROM navigation_events 
                WHERE user_id = ?
            """
            params = [user_id]
            
            if session_id:
                query += " AND session_id = ?"
                params.append(session_id)
            
            query += " ORDER BY timestamp"
            
            cursor.execute(query, params)
            
            journey = []
            for row in cursor.fetchall():
                journey.append({
                    'timestamp': row[0],
                    'from_route': row[1],
                    'to_route': row[2],
                    'method': row[3],
                    'duration_on_previous': row[4]
                })
            
            return journey
    
    def get_device_analytics(self, days: int = 30) -> Dict[str, Any]:
        """Get device usage analytics."""
        since_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Device distribution
            cursor.execute("""
                SELECT device_type, COUNT(DISTINCT session_id) as sessions,
                       COUNT(*) as total_navigations
                FROM navigation_events 
                WHERE timestamp >= ?
                GROUP BY device_type
            """, (since_date,))
            
            device_stats = {}
            for row in cursor.fetchall():
                device_stats[row[0]] = {
                    'sessions': row[1],
                    'total_navigations': row[2]
                }
            
            return device_stats
    
    def get_navigation_methods(self, days: int = 30) -> Dict[str, int]:
        """Get navigation method usage."""
        since_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT method, COUNT(*) as count
                FROM navigation_events 
                WHERE timestamp >= ?
                GROUP BY method
                ORDER BY count DESC
            """, (since_date,))
            
            methods = {}
            for row in cursor.fetchall():
                methods[row[0]] = row[1]
            
            return methods
    
    def get_page_performance_stats(self, route: str = None, days: int = 30) -> Dict[str, Any]:
        """Get page performance statistics."""
        since_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            query = """
                SELECT AVG(load_time) as avg_load_time,
                       AVG(time_spent) as avg_time_spent,
                       AVG(interactions) as avg_interactions,
                       COUNT(*) as total_views
                FROM page_performance 
                WHERE timestamp >= ?
            """
            params = [since_date]
            
            if route:
                query += " AND route = ?"
                params.append(route)
            
            cursor.execute(query, params)
            row = cursor.fetchone()
            
            return {
                'avg_load_time': round(row[0] or 0, 2),
                'avg_time_spent': round(row[1] or 0, 2),
                'avg_interactions': round(row[2] or 0, 2),
                'total_views': row[3]
            }
    
    def get_bounce_rate(self, days: int = 30) -> Dict[str, float]:
        """Calculate bounce rate for each page."""
        since_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Get sessions with only one page view
            cursor.execute("""
                SELECT s.session_id, COUNT(DISTINCT n.to_route) as unique_pages
                FROM user_sessions s
                LEFT JOIN navigation_events n ON s.session_id = n.session_id
                WHERE s.start_time >= ?
                GROUP BY s.session_id
            """, (since_date,))
            
            session_stats = cursor.fetchall()
            total_sessions = len(session_stats)
            bounce_sessions = len([s for s in session_stats if s[1] <= 1])
            
            # Get bounce rate by entry page
            cursor.execute("""
                SELECT n1.to_route as entry_page,
                       COUNT(DISTINCT n1.session_id) as total_entries,
                       COUNT(DISTINCT CASE WHEN session_pages.page_count = 1 THEN n1.session_id END) as bounces
                FROM navigation_events n1
                JOIN (
                    SELECT session_id, COUNT(DISTINCT to_route) as page_count
                    FROM navigation_events
                    WHERE timestamp >= ?
                    GROUP BY session_id
                ) session_pages ON n1.session_id = session_pages.session_id
                WHERE n1.timestamp >= ? 
                AND n1.from_route IS NULL  -- Entry page
                GROUP BY n1.to_route
            """, (since_date, since_date))
            
            page_bounce_rates = {}
            for row in cursor.fetchall():
                entry_page, total_entries, bounces = row
                bounce_rate = (bounces / total_entries * 100) if total_entries > 0 else 0
                page_bounce_rates[entry_page] = round(bounce_rate, 2)
            
            return {
                'overall_bounce_rate': round((bounce_sessions / total_sessions * 100) if total_sessions > 0 else 0, 2),
                'page_bounce_rates': page_bounce_rates
            }
    
    def generate_analytics_report(self, days: int = 30) -> Dict[str, Any]:
        """Generate comprehensive analytics report."""
        return {
            'period_days': days,
            'popular_routes': self.get_popular_routes(days),
            'navigation_flow': self.get_navigation_flow(days),
            'device_analytics': self.get_device_analytics(days),
            'navigation_methods': self.get_navigation_methods(days),
            'bounce_rates': self.get_bounce_rate(days),
            'performance_stats': self.get_page_performance_stats(days=days),
            'generated_at': datetime.now().isoformat()
        }
    
    def export_analytics(self, filepath: str, days: int = 30):
        """Export analytics to JSON file."""
        report = self.generate_analytics_report(days)
        
        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2)
    
    def cleanup_old_data(self, days_to_keep: int = 90):
        """Clean up old analytics data."""
        cutoff_date = (datetime.now() - timedelta(days=days_to_keep)).isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Delete old navigation events
            cursor.execute("DELETE FROM navigation_events WHERE timestamp < ?", (cutoff_date,))
            
            # Delete old sessions
            cursor.execute("DELETE FROM user_sessions WHERE start_time < ?", (cutoff_date,))
            
            # Delete old performance data
            cursor.execute("DELETE FROM page_performance WHERE timestamp < ?", (cutoff_date,))
            
            conn.commit()
            
            # Vacuum database to reclaim space
            cursor.execute("VACUUM")
            conn.commit()
