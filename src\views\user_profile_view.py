import flet as ft
from datetime import datetime
import re

class UserProfileView:
    """
    User profile view for the Science Laboratory Management System.
    Allows users to view and update their profile information.
    """
    
    def __init__(self, controller):
        """
        Initialize the user profile view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        
        # Create profile form controls
        self.full_name_field = ft.TextField(
            label="Full Name",
            width=300,
        )
        
        self.email_field = ft.TextField(
            label="Email",
            width=300,
            keyboard_type=ft.KeyboardType.EMAIL,
        )
        
        self.department_field = ft.TextField(
            label="Department",
            width=300,
        )
        
        self.profile_error_text = ft.Text(
            color=ft.colors.RED_500,
            size=12,
            visible=False,
        )
        
        self.update_profile_button = ft.ElevatedButton(
            text="Update Profile",
            on_click=self.update_profile_clicked,
        )
        
        # Create password change form controls
        self.current_password_field = ft.TextField(
            label="Current Password",
            password=True,
            can_reveal_password=True,
            width=300,
        )
        
        self.new_password_field = ft.TextField(
            label="New Password",
            password=True,
            can_reveal_password=True,
            width=300,
            helper_text="Minimum 8 characters with letters and numbers",
        )
        
        self.confirm_password_field = ft.TextField(
            label="Confirm New Password",
            password=True,
            can_reveal_password=True,
            width=300,
        )
        
        self.password_error_text = ft.Text(
            color=ft.colors.RED_500,
            size=12,
            visible=False,
        )
        
        self.change_password_button = ft.ElevatedButton(
            text="Change Password",
            on_click=self.change_password_clicked,
        )
        
        # Create activity history section
        self.activity_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("Action")),
                ft.DataColumn(ft.Text("Entity")),
                ft.DataColumn(ft.Text("Details")),
                ft.DataColumn(ft.Text("Date/Time")),
            ],
            rows=[],
        )
    
    def build(self):
        """
        Build and return the user profile view.
        
        Returns:
            ft.Container: The user profile view container
        """
        # Load user data
        self.load_user_data()
        
        # Create profile information card
        profile_card = ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Text("Profile Information", size=20, weight=ft.FontWeight.BOLD),
                        ft.Divider(),
                        self.full_name_field,
                        self.email_field,
                        self.department_field,
                        self.profile_error_text,
                        ft.Container(height=10),
                        self.update_profile_button,
                    ],
                    spacing=10,
                ),
                padding=20,
                width=350,
            ),
        )
        
        # Create password change card
        password_card = ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Text("Change Password", size=20, weight=ft.FontWeight.BOLD),
                        ft.Divider(),
                        self.current_password_field,
                        self.new_password_field,
                        self.confirm_password_field,
                        self.password_error_text,
                        ft.Container(height=10),
                        self.change_password_button,
                    ],
                    spacing=10,
                ),
                padding=20,
                width=350,
            ),
        )
        
        # Create activity history card
        activity_card = ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Text("Activity History", size=20, weight=ft.FontWeight.BOLD),
                        ft.Divider(),
                        ft.Container(
                            content=self.activity_table,
                            height=300,
                        ),
                    ],
                    spacing=10,
                ),
                padding=20,
                width=800,
            ),
        )
        
        # Return the complete view
        return ft.Container(
            content=ft.Column(
                [
                    ft.Text("User Profile", size=28, weight=ft.FontWeight.BOLD),
                    ft.Container(height=20),
                    ft.Row(
                        [profile_card, password_card],
                        alignment=ft.MainAxisAlignment.CENTER,
                        spacing=20,
                    ),
                    ft.Container(height=20),
                    activity_card,
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            ),
            padding=20,
            expand=True,
        )
    
    def load_user_data(self):
        """Load user data and activity history."""
        # Get current user
        user = self.controller.current_user
        
        # Set form values
        self.full_name_field.value = user["full_name"]
        self.email_field.value = user["email"]
        self.department_field.value = user["department"] or ""
        
        # Load activity history
        self.load_activity_history()
    
    def load_activity_history(self):
        """Load user activity history."""
        # Clear existing rows
        self.activity_table.rows.clear()
        
        # Query recent audit logs for this user
        logs = self.controller.db.execute_query(
            "SELECT * FROM audit_logs WHERE user_id = ? ORDER BY timestamp DESC LIMIT 20",
            (self.controller.current_user["id"],)
        )
        
        # Add rows for logs
        for log in logs:
            formatted_time = self.format_timestamp(log["timestamp"])
            entity_type = log["entity_type"].replace("_", " ").capitalize()
            
            self.activity_table.rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(log["action"].capitalize())),
                        ft.DataCell(ft.Text(entity_type)),
                        ft.DataCell(ft.Text(log["details"])),
                        ft.DataCell(ft.Text(formatted_time)),
                    ]
                )
            )
    
    def format_timestamp(self, timestamp):
        """
        Format a timestamp for display.
        
        Args:
            timestamp (str): The timestamp to format
            
        Returns:
            str: Formatted timestamp
        """
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime("%m/%d/%Y %I:%M %p")
        except:
            return timestamp
    
    def update_profile_clicked(self, e):
        """
        Handle update profile button click.
        
        Args:
            e: The click event
        """
        # Validate email format
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, self.email_field.value):
            self.profile_error_text.value = "Please enter a valid email address."
            self.profile_error_text.visible = True
            self.controller.page.update()
            return
        
        # Update user profile
        self.controller.db.execute_query(
            "UPDATE users SET full_name = ?, email = ?, department = ? WHERE id = ?",
            (
                self.full_name_field.value,
                self.email_field.value,
                self.department_field.value,
                self.controller.current_user["id"]
            )
        )
        
        # Update current user in memory
        self.controller.current_user["full_name"] = self.full_name_field.value
        self.controller.current_user["email"] = self.email_field.value
        self.controller.current_user["department"] = self.department_field.value
        
        # Show success message
        self.controller.page.snack_bar = ft.SnackBar(
            content=ft.Text("Profile updated successfully"),
            bgcolor=ft.colors.GREEN_400,
        )
        self.controller.page.snack_bar.open = True
        
        # Hide error message
        self.profile_error_text.visible = False
        
        # Update the page
        self.controller.page.update()
    
    def change_password_clicked(self, e):
        """
        Handle change password button click.
        
        Args:
            e: The click event
        """
        # Verify current password
        if not self.controller.security.verify_password(
            self.current_password_field.value, 
            self.controller.current_user["password"]
        ):
            self.password_error_text.value = "Current password is incorrect."
            self.password_error_text.visible = True
            self.controller.page.update()
            return
        
        # Validate new password strength
        if len(self.new_password_field.value) < 8:
            self.password_error_text.value = "New password must be at least 8 characters long."
            self.password_error_text.visible = True
            self.controller.page.update()
            return
        
        # Check if new password contains both letters and numbers
        if not (any(c.isalpha() for c in self.new_password_field.value) and 
                any(c.isdigit() for c in self.new_password_field.value)):
            self.password_error_text.value = "New password must contain both letters and numbers."
            self.password_error_text.visible = True
            self.controller.page.update()
            return
        
        # Check if passwords match
        if self.new_password_field.value != self.confirm_password_field.value:
            self.password_error_text.value = "New passwords do not match."
            self.password_error_text.visible = True
            self.controller.page.update()
            return
        
        # Update password
        hashed_password = self.controller.security.hash_password(self.new_password_field.value)
        self.controller.db.execute_query(
            "UPDATE users SET password = ? WHERE id = ?",
            (hashed_password, self.controller.current_user["id"])
        )
        
        # Update current user in memory
        self.controller.current_user["password"] = hashed_password
        
        # Show success message
        self.controller.page.snack_bar = ft.SnackBar(
            content=ft.Text("Password changed successfully"),
            bgcolor=ft.colors.GREEN_400,
        )
        self.controller.page.snack_bar.open = True
        
        # Clear password fields
        self.current_password_field.value = ""
        self.new_password_field.value = ""
        self.confirm_password_field.value = ""
        
        # Hide error message
        self.password_error_text.visible = False
        
        # Update the page
        self.controller.page.update()
    
    def __init__(self, controller):
        """
        Initialize the user profile view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        
        # Create profile form controls
        self.full_name_field = ft.TextField(
            label="Full Name",
            width=300,
        )
        
        self.email_field = ft.TextField(
            label="Email",
            width=300,
            keyboard_type=ft.KeyboardType.EMAIL,
        )
        
        self.department_field = ft.TextField(
            label="Department",
            width=300,
        )
        
        self.profile_error_text = ft.Text(
            color=ft.colors.RED_500,
            size=12,
            visible=False,
        )
        
        self.update_profile_button = ft.ElevatedButton(
            text="Update Profile",
            on_click=self.update_profile_clicked,
        )
        
        # Create password change form controls
        self.current_password_field = ft.TextField(
            label="Current Password",
            password=True,
            can_reveal_password=True,
            width=300,
        )
        
        self.new_password_field = ft.TextField(
            label="New Password",
            password=True,
            can_reveal_password=True,
            width=300,
            helper_text="Minimum 8 characters with letters and numbers",
        )
        
        self.confirm_password_field = ft.TextField(
            label="Confirm New Password",
            password=True,
            can_reveal_password=True,
            width=300,
        )
        
        self.password_error_text = ft.Text(
            color=ft.colors.RED_500,
            size=12,
            visible=False,
        )
        
        self.change_password_button = ft.ElevatedButton(
            text="Change Password",
            on_click=self.change_password_clicked,
        )
        
        # Create activity history section
        self.activity_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("Action")),
                ft.DataColumn(ft.Text("Entity")),
                ft.DataColumn(ft.Text("Details")),
                ft.DataColumn(ft.Text("Date/Time")),
            ],
            rows=[],
        )
    
    def build(self):
        """
        Build and return the user profile view.
        
        Returns:
            ft.Container: The user profile view container
        """
        # Load user data
        self.load_user_data()
        
        # Create profile information card
        profile_card = ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Text("Profile Information", size=20, weight=ft.FontWeight.BOLD),
                        ft.Divider(),
                        self.full_name_field,
                        self.email_field,
                        self.department_field,
                        self.profile_error_text,
                        ft.Container(height=10),
                        self.update_profile_button,
                    ],
                    spacing=10,
                ),
                padding=20,
                width=350,
            ),
        )
        
        # Create password change card
        password_card = ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Text("Change Password", size=20, weight=ft.FontWeight.BOLD),
                        ft.Divider(),
                        self.current_password_field,
                        self.new_password_field,
                        self.confirm_password_field,
                        self.password_error_text,
                        ft.Container(height=10),
                        self.change_password_button,
                    ],
                    spacing=10,
                ),
                padding=20,
                width=350,
            ),
        )
        
        # Create activity history card
        activity_card = ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Text("Activity History", size=20, weight=ft.FontWeight.BOLD),
                        ft.Divider(),
                        ft.Container(
                            content=self.activity_table,
                            height=300,
                        ),
                    ],
                    spacing=10,
                ),
                padding=20,
                width=800,
            ),
        )
        
        # Return the complete view
        return ft.Container(
            content=ft.Column(
                [
                    ft.Text("User Profile", size=28, weight=ft.FontWeight.BOLD),
                    ft.Container(height=20),
                    ft.Row(
                        [profile_card, password_card],
                        alignment=ft.MainAxisAlignment.CENTER,
                        spacing=20,
                    ),
                    ft.Container(height=20),
                    activity_card,
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            ),
            padding=20,
            expand=True,
        )
    
    def load_user_data(self):
        """Load user data and activity history."""
        # Get current user
        user = self.controller.current_user
        
        # Set form values
        self.full_name_field.value = user["full_name"]
        self.email_field.value = user["email"]
        self.department_field.value = user["department"] or ""
        
        # Load activity history
        self.load_activity_history()
    
    def load_activity_history(self):
        """Load user activity history."""
        # Clear existing rows
        self.activity_table.rows.clear()
        
        # Query recent audit logs for this user
        logs = self.controller.db.execute_query(
            "SELECT * FROM audit_logs WHERE user_id = ? ORDER BY timestamp DESC LIMIT 20",
            (self.controller.current_user["id"],)
        )
        
        # Add rows for logs
        for log in logs:
            formatted_time = self.format_timestamp(log["timestamp"])
            entity_type = log["entity_type"].replace("_", " ").capitalize()
            
            self.activity_table.rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(log["action"].capitalize())),
                        ft.DataCell(ft.Text(entity_type)),
                        ft.DataCell(ft.Text(log["details"])),
                        ft.DataCell(ft.Text(formatted_time)),
                    ]
                )
            )
    
    def format_timestamp(self, timestamp):
        """
        Format a timestamp for display.
        
        Args:
            timestamp (str): The timestamp to format
            
        Returns:
            str: Formatted timestamp
        """
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime("%m/%d/%Y %I:%M %p")
        except:
            return timestamp
    
    def update_profile_clicked(self, e):
        """
        Handle update profile button click.
        
        Args:
            e: The click event
        """
        # Validate email format
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, self.email_field.value):
            self.profile_error_text.value = "Please enter a valid email address."
            self.profile_error_text.visible = True
            self.controller.page.update()
            return
        
        # Update user profile
        self.controller.db.execute_query(
            "UPDATE users SET full_name = ?, email = ?, department = ? WHERE id = ?",
            (
                self.full_name_field.value,
                self.email_field.value,
                self.department_field.value,
                self.controller.current_user["id"]
            )
        )
        
        # Update current user in memory
        self.controller.current_user["full_name"] = self.full_name_field.value
        self.controller.current_user["email"] = self.email_field.value
        self.controller.current_user["department"] = self.department_field.value
        
        # Show success message
        self.controller.page.snack_bar = ft.SnackBar(
            content=ft.Text("Profile updated successfully"),
            bgcolor=ft.colors.GREEN_400,
        )
        self.controller.page.snack_bar.open = True
        
        # Hide error message
        self.profile_error_text.visible = False
        
        # Update the page
        self.controller.page.update()
    
    def change_password_clicked(self, e):
        """
        Handle change password button click.
        
        Args:
            e: The click event
        """
        # Verify current password
        if not self.controller.security.verify_password(
            self.current_password_field.value, 
            self.controller.current_user["password"]
        ):
            self.password_error_text.value = "Current password is incorrect."
            self.password_error_text.visible = True
            self.controller.page.update()
            return
        
        # Validate new password strength
        if len(self.new_password_field.value) < 8:
            self.password_error_text.value = "New password must be at least 8 characters long."
            self.password_error_text.visible = True
            self.controller.page.update()
            return
        
        # Check if new password contains both letters and numbers
        if not (any(c.isalpha() for c in self.new_password_field.value) and 
                any(c.isdigit() for c in self.new_password_field.value)):
            self.password_error_text.value = "New password must contain both letters and numbers."
            self.password_error_text.visible = True
            self.controller.page.update()
            return
        
        # Check if passwords match
        if self.new_password_field.value != self.confirm_password_field.value:
            self.password_error_text.value = "New passwords do not match."
            self.password_error_text.visible = True
            self.controller.page.update()
            return
        
        # Update password
        hashed_password = self.controller.security.hash_password(self.new_password_field.value)
        self.controller.db.execute_query(
            "UPDATE users SET password = ? WHERE id = ?",
            (hashed_password, self.controller.current_user["id"])
        )
        
        # Update current user in memory
        self.controller.current_user["password"] = hashed_password
        
        # Show success message
        self.controller.page.snack_bar = ft.SnackBar(
            content=ft.Text("Password changed successfully"),
            bgcolor=ft.colors.GREEN_400,
        )
        self.controller.page.snack_bar.open = True
        
        # Clear password fields
        self.current_password_field.value = ""
        self.new_password_field.value = ""
        self.confirm_password_field.value = ""
        
        # Hide error message
        self.password_error_text.visible = False
        
        # Update the page
        self.controller.page.update()