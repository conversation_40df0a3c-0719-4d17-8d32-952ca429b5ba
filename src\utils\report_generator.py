import os
import json
import csv
import io
from datetime import datetime
import matplotlib.pyplot as plt
import numpy as np
from .experiment_analyzer import ExperimentAnalyzer

class ReportGenerator:
    """
    Utility class for generating reports from experiment data.
    """
    
    def __init__(self, experiment_model, output_dir="reports"):
        """
        Initialize the report generator.
        
        Args:
            experiment_model: The experiment model instance
            output_dir (str): Directory to save generated reports
        """
        self.experiment_model = experiment_model
        self.analyzer = ExperimentAnalyzer(experiment_model)
        self.output_dir = output_dir
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
    
    def generate_experiment_report(self, experiment_id, format="html"):
        """
        Generate a report for an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            format (str): The report format (html, pdf, csv, json)
            
        Returns:
            str: Path to the generated report file
        """
        # Get the experiment
        experiment = self.experiment_model.get_experiment_by_id(experiment_id)
        if not experiment:
            return None
        
        # Generate the report based on format
        if format == "html":
            return self._generate_html_report(experiment_id)
        elif format == "csv":
            return self._generate_csv_report(experiment_id)
        elif format == "json":
            return self._generate_json_report(experiment_id)
        else:
            # Default to HTML
            return self._generate_html_report(experiment_id)
    
    def _generate_html_report(self, experiment_id):
        """
        Generate an HTML report for an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            
        Returns:
            str: Path to the generated HTML file
        """
        # Get the experiment
        experiment = self.experiment_model.get_experiment_by_id(experiment_id)
        if not experiment:
            return None
        
        # Generate the report content
        report_content = self.analyzer.generate_experiment_report(experiment_id, format="html")
        
        # Create the output file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"experiment_{experiment_id}_{timestamp}.html"
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(report_content)
        
        return filepath
    
    def _generate_csv_report(self, experiment_id):
        """
        Generate a CSV report for an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            
        Returns:
            str: Path to the generated CSV file
        """
        # Get the experiment
        experiment = self.experiment_model.get_experiment_by_id(experiment_id)
        if not experiment:
            return None
        
        # Get measurements
        measurements = self.experiment_model.get_measurements(experiment_id)
        
        # Create the output file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"experiment_{experiment_id}_{timestamp}.csv"
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, "w", newline="", encoding="utf-8") as f:
            writer = csv.writer(f)
            
            # Write header
            writer.writerow(["Experiment ID", experiment_id])
            writer.writerow(["Title", experiment["title"]])
            writer.writerow(["Status", experiment["status"]])
            writer.writerow(["Created By", experiment["creator_name"]])
            writer.writerow(["Created At", experiment["created_at"]])
            writer.writerow(["Last Updated", experiment["last_updated"]])
            writer.writerow([])
            
            # Write measurements header
            writer.writerow(["Measurement ID", "Name", "Value", "Unit", "Timestamp", "Notes", "Created By"])
            
            # Write measurements
            for measurement in measurements:
                writer.writerow([
                    measurement["id"],
                    measurement["name"],
                    measurement["value"],
                    measurement["unit"],
                    measurement["timestamp"],
                    measurement["notes"],
                    measurement["creator_name"]
                ])
        
        return filepath
    
    def _generate_json_report(self, experiment_id):
        """
        Generate a JSON report for an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            
        Returns:
            str: Path to the generated JSON file
        """
        # Get the experiment data
        export_data = self.experiment_model.export_experiment_data(experiment_id)
        if not export_data:
            return None
        
        # Add analysis data
        analysis = self.analyzer.analyze_experiment(experiment_id)
        export_data["analysis"] = analysis
        
        # Create the output file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"experiment_{experiment_id}_{timestamp}.json"
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(export_data, f, indent=2)
        
        return filepath
    
    def generate_measurement_charts(self, experiment_id):
        """
        Generate charts for experiment measurements.
        
        Args:
            experiment_id (int): The experiment ID
            
        Returns:
            list: Paths to the generated chart files
        """
        # Get the experiment
        experiment = self.experiment_model.get_experiment_by_id(experiment_id)
        if not experiment:
            return []
        
        # Get measurements
        measurements = self.experiment_model.get_measurements(experiment_id)
        
        # Group measurements by name
        measurement_groups = {}
        for measurement in measurements:
            name = measurement["name"]
            if name not in measurement_groups:
                measurement_groups[name] = []
            measurement_groups[name].append(measurement)
        
        # Generate charts for each measurement group
        chart_files = []
        for name, group in measurement_groups.items():
            if len(group) >= 2:  # Need at least 2 points for a chart
                chart_path = self._generate_measurement_chart(experiment_id, name, group)
                if chart_path:
                    chart_files.append(chart_path)
        
        return chart_files
    
    def _generate_measurement_chart(self, experiment_id, measurement_name, measurements):
        """
        Generate a chart for a group of measurements.
        
        Args:
            experiment_id (int): The experiment ID
            measurement_name (str): The measurement name
            measurements (list): List of measurements
            
        Returns:
            str: Path to the generated chart file
        """
        # Extract data
        timestamps = []
        values = []
        
        for measurement in measurements:
            try:
                timestamp = datetime.fromisoformat(measurement["timestamp"])
                timestamps.append(timestamp)
                values.append(measurement["value"])
            except:
                continue
        
        if not timestamps or not values:
            return None
        
        # Sort by timestamp
        sorted_data = sorted(zip(timestamps, values), key=lambda x: x[0])
        timestamps = [t for t, v in sorted_data]
        values = [v for t, v in sorted_data]
        
        # Create the chart
        plt.figure(figsize=(10, 6))
        plt.plot(timestamps, values, marker='o')
        plt.title(f"{measurement_name} over Time")
        plt.xlabel("Time")
        plt.ylabel(f"{measurement_name} ({measurements[0]['unit'] or 'units'})")
        plt.grid(True)
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # Save the chart
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"experiment_{experiment_id}_{measurement_name}_{timestamp}.png"
        filepath = os.path.join(self.output_dir, filename)
        
        plt.savefig(filepath)
        plt.close()
        
        return filepath
    
    def generate_summary_report(self, experiment_ids=None):
        """
        Generate a summary report for multiple experiments.
        
        Args:
            experiment_ids (list): List of experiment IDs, or None for all experiments
            
        Returns:
            str: Path to the generated report file
        """
        # Get experiments
        if experiment_ids:
            experiments = [self.experiment_model.get_experiment_by_id(exp_id) for exp_id in experiment_ids]
            experiments = [exp for exp in experiments if exp]
        else:
            experiments = self.experiment_model.get_all_experiments()
        
        if not experiments:
            return None
        
        # Create the output file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"experiment_summary_{timestamp}.html"
        filepath = os.path.join(self.output_dir, filename)
        
        # Generate HTML content
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Experiment Summary Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1, h2, h3 {{ color: #333; }}
                table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .status-draft {{ color: #888; }}
                .status-active {{ color: #00c; }}
                .status-completed {{ color: #080; }}
                .status-archived {{ color: #c00; }}
            </style>
        </head>
        <body>
            <h1>Experiment Summary Report</h1>
            <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            
            <h2>Experiments ({len(experiments)})</h2>
            <table>
                <tr>
                    <th>ID</th>
                    <th>Title</th>
                    <th>Status</th>
                    <th>Created By</th>
                    <th>Created At</th>
                    <th>Last Updated</th>
                    <th>Measurements</th>
                </tr>
        """
        
        # Add experiment rows
        for experiment in experiments:
            # Get measurement count
            measurements = self.experiment_model.get_measurements(experiment["id"])
            
            # Format dates
            created_at = self._format_timestamp(experiment["created_at"])
            last_updated = self._format_timestamp(experiment["last_updated"])
            
            # Determine status class
            status_class = f"status-{experiment['status'].lower()}"
            
            html += f"""
                <tr>
                    <td>{experiment['id']}</td>
                    <td>{experiment['title']}</td>
                    <td class="{status_class}">{experiment['status']}</td>
                    <td>{experiment['creator_name']}</td>
                    <td>{created_at}</td>
                    <td>{last_updated}</td>
                    <td>{len(measurements)}</td>
                </tr>
            """
        
        html += """
            </table>
        </body>
        </html>
        """
        
        # Write the file
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(html)
        
        return filepath
    
    def _format_timestamp(self, timestamp):
        """Format a timestamp for display."""
        if not timestamp:
            return "N/A"
        
        try:
            dt = datetime.fromisoformat(timestamp)
            return dt.strftime("%Y-%m-%d %H:%M")
        except:
            return timestamp
    
    def schedule_automatic_reports(self, interval_hours=24):
        """
        Schedule automatic report generation.
        
        Args:
            interval_hours (int): Interval in hours between report generation
            
        Returns:
            bool: True if scheduled successfully, False otherwise
        """
        # In a real application, this would set up a scheduled task
        # For this example, we'll just return True
        return True