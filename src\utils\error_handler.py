"""
Enhanced error handling and monitoring for the Science Laboratory Management System.
"""
import logging
import traceback
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional, Callable
from functools import wraps
import json
from enum import Enum

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from src.utils.logger import setup_logger


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories."""
    DATABASE = "database"
    AUTHENTICATION = "authentication"
    VALIDATION = "validation"
    NETWORK = "network"
    FILE_SYSTEM = "file_system"
    BUSINESS_LOGIC = "business_logic"
    EXTERNAL_API = "external_api"
    SYSTEM = "system"


class LabManagementError(Exception):
    """Base exception class for Lab Management System."""
    
    def __init__(
        self, 
        message: str, 
        error_code: str = None,
        category: ErrorCategory = ErrorCategory.SYSTEM,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        details: Dict[str, Any] = None,
        user_message: str = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.category = category
        self.severity = severity
        self.details = details or {}
        self.user_message = user_message or "An error occurred. Please try again."
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary."""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "user_message": self.user_message,
            "category": self.category.value,
            "severity": self.severity.value,
            "details": self.details,
            "timestamp": self.timestamp.isoformat()
        }


class DatabaseError(LabManagementError):
    """Database-related errors."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message, 
            category=ErrorCategory.DATABASE,
            severity=ErrorSeverity.HIGH,
            **kwargs
        )


class AuthenticationError(LabManagementError):
    """Authentication-related errors."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.AUTHENTICATION,
            severity=ErrorSeverity.HIGH,
            user_message="Authentication failed. Please check your credentials.",
            **kwargs
        )


class ValidationError(LabManagementError):
    """Validation-related errors."""
    
    def __init__(self, message: str, field: str = None, **kwargs):
        details = kwargs.get('details', {})
        if field:
            details['field'] = field
        
        super().__init__(
            message,
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.LOW,
            details=details,
            user_message="Please check your input and try again.",
            **kwargs
        )


class BusinessLogicError(LabManagementError):
    """Business logic-related errors."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.BUSINESS_LOGIC,
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )


class ErrorHandler:
    """
    Centralized error handling and monitoring system.
    """
    
    def __init__(self, log_file: str = None):
        """
        Initialize the error handler.
        
        Args:
            log_file: Path to error log file
        """
        self.logger = setup_logger("error_handler")
        self.error_log_file = log_file or os.path.join(
            project_root, "logs", "errors.json"
        )
        self.error_stats = {
            "total_errors": 0,
            "errors_by_category": {},
            "errors_by_severity": {},
            "recent_errors": []
        }
        
        # Ensure log directory exists
        os.makedirs(os.path.dirname(self.error_log_file), exist_ok=True)
    
    def handle_error(
        self, 
        error: Exception, 
        context: Dict[str, Any] = None,
        user_id: int = None,
        request_id: str = None
    ) -> Dict[str, Any]:
        """
        Handle and log an error.
        
        Args:
            error: The exception that occurred
            context: Additional context information
            user_id: ID of the user when error occurred
            request_id: Request ID for tracking
            
        Returns:
            Dict containing error information
        """
        # Convert to LabManagementError if needed
        if not isinstance(error, LabManagementError):
            lab_error = LabManagementError(
                message=str(error),
                error_code=error.__class__.__name__,
                details={"original_error": str(error)}
            )
        else:
            lab_error = error
        
        # Add context information
        if context:
            lab_error.details.update(context)
        
        if user_id:
            lab_error.details["user_id"] = user_id
        
        if request_id:
            lab_error.details["request_id"] = request_id
        
        # Add traceback for debugging
        lab_error.details["traceback"] = traceback.format_exc()
        
        # Log the error
        self._log_error(lab_error)
        
        # Update statistics
        self._update_stats(lab_error)
        
        # Save to file
        self._save_error_to_file(lab_error)
        
        return lab_error.to_dict()
    
    def _log_error(self, error: LabManagementError):
        """Log error to the logging system."""
        log_message = f"[{error.error_code}] {error.message}"
        
        if error.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message, extra=error.details)
        elif error.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message, extra=error.details)
        elif error.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message, extra=error.details)
        else:
            self.logger.info(log_message, extra=error.details)
    
    def _update_stats(self, error: LabManagementError):
        """Update error statistics."""
        self.error_stats["total_errors"] += 1
        
        # Update category stats
        category = error.category.value
        if category not in self.error_stats["errors_by_category"]:
            self.error_stats["errors_by_category"][category] = 0
        self.error_stats["errors_by_category"][category] += 1
        
        # Update severity stats
        severity = error.severity.value
        if severity not in self.error_stats["errors_by_severity"]:
            self.error_stats["errors_by_severity"][severity] = 0
        self.error_stats["errors_by_severity"][severity] += 1
        
        # Add to recent errors (keep last 100)
        self.error_stats["recent_errors"].append({
            "timestamp": error.timestamp.isoformat(),
            "error_code": error.error_code,
            "category": category,
            "severity": severity,
            "message": error.message
        })
        
        if len(self.error_stats["recent_errors"]) > 100:
            self.error_stats["recent_errors"] = self.error_stats["recent_errors"][-100:]
    
    def _save_error_to_file(self, error: LabManagementError):
        """Save error to JSON file for analysis."""
        try:
            error_data = error.to_dict()
            
            # Read existing errors
            errors = []
            if os.path.exists(self.error_log_file):
                try:
                    with open(self.error_log_file, 'r') as f:
                        errors = json.load(f)
                except (json.JSONDecodeError, IOError):
                    errors = []
            
            # Add new error
            errors.append(error_data)
            
            # Keep only last 1000 errors
            if len(errors) > 1000:
                errors = errors[-1000:]
            
            # Save back to file
            with open(self.error_log_file, 'w') as f:
                json.dump(errors, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Failed to save error to file: {str(e)}")
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics."""
        return self.error_stats.copy()
    
    def get_recent_errors(self, limit: int = 50) -> list:
        """Get recent errors."""
        return self.error_stats["recent_errors"][-limit:]
    
    def clear_stats(self):
        """Clear error statistics."""
        self.error_stats = {
            "total_errors": 0,
            "errors_by_category": {},
            "errors_by_severity": {},
            "recent_errors": []
        }


# Global error handler instance
error_handler = ErrorHandler()


def handle_exceptions(
    reraise: bool = False,
    default_return: Any = None,
    error_category: ErrorCategory = ErrorCategory.SYSTEM
):
    """
    Decorator for handling exceptions in functions.
    
    Args:
        reraise: Whether to reraise the exception after handling
        default_return: Default value to return if exception occurs
        error_category: Category of errors for this function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except LabManagementError:
                # Already a lab management error, just handle it
                error_handler.handle_error(
                    sys.exc_info()[1],
                    context={"function": func.__name__, "args": str(args)[:200]}
                )
                if reraise:
                    raise
                return default_return
            except Exception as e:
                # Convert to lab management error
                lab_error = LabManagementError(
                    message=f"Error in {func.__name__}: {str(e)}",
                    category=error_category,
                    details={"function": func.__name__, "args": str(args)[:200]}
                )
                error_handler.handle_error(lab_error)
                if reraise:
                    raise lab_error
                return default_return
        
        return wrapper
    return decorator


def validate_input(validation_func: Callable, error_message: str = None):
    """
    Decorator for input validation.
    
    Args:
        validation_func: Function that validates input and returns (is_valid, error_message)
        error_message: Custom error message
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            is_valid, validation_error = validation_func(*args, **kwargs)
            if not is_valid:
                raise ValidationError(
                    error_message or validation_error,
                    details={"function": func.__name__}
                )
            return func(*args, **kwargs)
        
        return wrapper
    return decorator
