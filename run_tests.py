#!/usr/bin/env python
"""
Test runner script for the Science Laboratory Management System.
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def run_tests(test_type="all", coverage=True, verbose=True, parallel=False):
    """
    Run tests with specified options.
    
    Args:
        test_type: Type of tests to run (all, unit, integration, api, ui)
        coverage: Whether to generate coverage report
        verbose: Whether to run in verbose mode
        parallel: Whether to run tests in parallel
    """
    cmd = ["python", "-m", "pytest"]
    
    # Add test type marker
    if test_type != "all":
        cmd.extend(["-m", test_type])
    
    # Add coverage options
    if coverage:
        cmd.extend([
            "--cov=src",
            "--cov-report=html",
            "--cov-report=term-missing",
            "--cov-fail-under=80"
        ])
    
    # Add verbosity
    if verbose:
        cmd.append("-v")
    
    # Add parallel execution
    if parallel:
        cmd.extend(["-n", "auto"])
    
    # Add test directory
    cmd.append("tests/")
    
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, cwd=project_root, check=False)
        return result.returncode == 0
    except Exception as e:
        print(f"Error running tests: {e}")
        return False


def run_linting():
    """Run code linting."""
    print("Running code linting...")
    
    # Run flake8
    flake8_cmd = ["python", "-m", "flake8", "src/", "tests/"]
    try:
        subprocess.run(flake8_cmd, cwd=project_root, check=True)
        print("✓ Flake8 linting passed")
    except subprocess.CalledProcessError:
        print("✗ Flake8 linting failed")
        return False
    
    # Run black (code formatting check)
    black_cmd = ["python", "-m", "black", "--check", "src/", "tests/"]
    try:
        subprocess.run(black_cmd, cwd=project_root, check=True)
        print("✓ Black formatting check passed")
    except subprocess.CalledProcessError:
        print("✗ Black formatting check failed")
        return False
    
    return True


def run_security_scan():
    """Run security scanning."""
    print("Running security scan...")
    
    # Run bandit
    bandit_cmd = ["python", "-m", "bandit", "-r", "src/"]
    try:
        subprocess.run(bandit_cmd, cwd=project_root, check=True)
        print("✓ Security scan passed")
        return True
    except subprocess.CalledProcessError:
        print("✗ Security scan failed")
        return False


def setup_test_environment():
    """Set up test environment."""
    print("Setting up test environment...")
    
    # Create test directories
    test_dirs = ["tests/data", "tests/logs", "tests/uploads"]
    for test_dir in test_dirs:
        os.makedirs(project_root / test_dir, exist_ok=True)
    
    # Install test dependencies
    test_deps = [
        "pytest>=7.0.0",
        "pytest-cov>=4.0.0",
        "pytest-xdist>=3.0.0",
        "pytest-mock>=3.10.0",
        "flake8>=6.0.0",
        "black>=23.0.0",
        "bandit>=1.7.0"
    ]
    
    for dep in test_deps:
        try:
            subprocess.run([
                "python", "-m", "pip", "install", dep
            ], check=True, capture_output=True)
        except subprocess.CalledProcessError as e:
            print(f"Warning: Failed to install {dep}: {e}")
    
    print("✓ Test environment setup complete")


def generate_test_report():
    """Generate comprehensive test report."""
    print("Generating test report...")
    
    report_dir = project_root / "test_reports"
    report_dir.mkdir(exist_ok=True)
    
    # Run tests with XML output for CI/CD
    cmd = [
        "python", "-m", "pytest",
        "--junitxml=test_reports/junit.xml",
        "--cov=src",
        "--cov-report=xml:test_reports/coverage.xml",
        "--cov-report=html:test_reports/htmlcov",
        "tests/"
    ]
    
    try:
        subprocess.run(cmd, cwd=project_root, check=True)
        print("✓ Test report generated successfully")
        print(f"Reports available in: {report_dir}")
        return True
    except subprocess.CalledProcessError:
        print("✗ Test report generation failed")
        return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test runner for Lab Management System")
    parser.add_argument(
        "--type", 
        choices=["all", "unit", "integration", "api", "ui"],
        default="all",
        help="Type of tests to run"
    )
    parser.add_argument(
        "--no-coverage",
        action="store_true",
        help="Disable coverage reporting"
    )
    parser.add_argument(
        "--parallel",
        action="store_true",
        help="Run tests in parallel"
    )
    parser.add_argument(
        "--lint",
        action="store_true",
        help="Run linting only"
    )
    parser.add_argument(
        "--security",
        action="store_true",
        help="Run security scan only"
    )
    parser.add_argument(
        "--setup",
        action="store_true",
        help="Set up test environment"
    )
    parser.add_argument(
        "--report",
        action="store_true",
        help="Generate comprehensive test report"
    )
    parser.add_argument(
        "--all-checks",
        action="store_true",
        help="Run all checks (tests, linting, security)"
    )
    
    args = parser.parse_args()
    
    success = True
    
    if args.setup:
        setup_test_environment()
        return
    
    if args.lint or args.all_checks:
        success &= run_linting()
    
    if args.security or args.all_checks:
        success &= run_security_scan()
    
    if args.report:
        success &= generate_test_report()
    elif not args.lint and not args.security:
        # Run tests
        success &= run_tests(
            test_type=args.type,
            coverage=not args.no_coverage,
            parallel=args.parallel
        )
    
    if success:
        print("\n✓ All checks passed!")
        sys.exit(0)
    else:
        print("\n✗ Some checks failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
