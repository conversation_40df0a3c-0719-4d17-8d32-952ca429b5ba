#!/usr/bin/env python
"""
Test script for the consolidated lab_db database.
"""
import os
import sys
import logging

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(project_root, 'lab_db_test.log'))
    ]
)

logger = logging.getLogger('lab_db_test')

# Import the DatabaseManager
from src.database.db_manager import DatabaseManager

def main():
    """
    Test the consolidated lab_db database.
    """
    logger.info("Starting lab_db test")
    
    # Define database path
    lab_db_path = os.path.join(project_root, 'data', 'lab_db.db')
    
    # Create a new database manager
    db = DatabaseManager(db_path=lab_db_path, auto_initialize=False)
    
    # Connect to the database
    if not db.connect():
        logger.error("Failed to connect to database")
        return
    
    # Check if the database exists
    if not os.path.exists(lab_db_path):
        logger.info(f"Database does not exist: {lab_db_path}")
        
        # Initialize the database
        if db.initialize_database():
            logger.info("Database initialized successfully")
        else:
            logger.error("Failed to initialize database")
            return
    else:
        logger.info(f"Database exists: {lab_db_path}")
    
    # Test user authentication
    username = "admin"
    password = "admin123"
    
    user = db.authenticate_user(username, password)
    if user:
        logger.info(f"User authenticated: {user['username']}")
    else:
        logger.warning("User authentication failed")
        
        # Reconnect to the database
        if not db.connect():
            logger.error("Failed to connect to database")
            return
        
        # Check if any users exist
        try:
            db.cursor.execute("SELECT COUNT(*) FROM users")
            count = db.cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"Error checking users: {str(e)}")
            count = 0
        
        if count == 0:
            logger.info("No users found, creating default admin user")
            
            # Create default admin user
            user_id = db.create_user(
                username="admin",
                password="admin123",
                email="<EMAIL>",
                full_name="Administrator",
                role="admin"
            )
            
            if user_id:
                logger.info(f"Created default admin user with ID: {user_id}")
            else:
                logger.error("Failed to create default admin user")
        else:
            logger.info(f"Found {count} users in the database")
    
    # Test logging activity
    if user:
        db.log_activity(user['id'], "login", "User logged in")
        logger.info("Activity logged")
        
        # Test logging audit
        db.log_audit(user['id'], "view", "inventory", None, "Viewed inventory")
        logger.info("Audit logged")
        
        # Get recent activities
        activities = db.get_recent_activities()
        logger.info(f"Recent activities: {len(activities)}")
    
    # Disconnect from the database
    db.disconnect()
    
    logger.info("lab_db test completed successfully")

if __name__ == "__main__":
    main()