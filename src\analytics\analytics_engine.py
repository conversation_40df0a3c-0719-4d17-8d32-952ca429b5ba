"""
Advanced Analytics Engine for Science Laboratory Management System
Provides predictive insights, usage analytics, and data visualization
"""
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json
import os
import sys

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from src.database.db_manager import DatabaseManager


class AnalyticsEngine:
    """
    Advanced analytics engine for laboratory management insights.
    """
    
    def __init__(self, db_manager: DatabaseManager = None):
        """
        Initialize analytics engine.
        
        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager or DatabaseManager()
        self.cache = {}
        self.cache_ttl = 300  # 5 minutes
    
    def get_dashboard_analytics(self) -> Dict[str, Any]:
        """
        Get comprehensive dashboard analytics.
        
        Returns:
            Dictionary containing dashboard analytics data
        """
        cache_key = "dashboard_analytics"
        if self._is_cached(cache_key):
            return self.cache[cache_key]["data"]
        
        analytics = {
            "overview": self._get_overview_metrics(),
            "inventory": self._get_inventory_analytics(),
            "experiments": self._get_experiment_analytics(),
            "users": self._get_user_analytics(),
            "trends": self._get_trend_analytics(),
            "predictions": self._get_predictive_analytics(),
            "alerts": self._get_system_alerts()
        }
        
        self._cache_data(cache_key, analytics)
        return analytics
    
    def _get_overview_metrics(self) -> Dict[str, Any]:
        """Get high-level overview metrics."""
        self.db_manager.connect()
        try:
            # Total counts
            metrics = {}
            
            # Inventory metrics
            self.db_manager.cursor.execute("SELECT COUNT(*) FROM inventory_items")
            metrics["total_inventory_items"] = self.db_manager.cursor.fetchone()[0]
            
            self.db_manager.cursor.execute("""
                SELECT COUNT(*) FROM inventory_items 
                WHERE quantity <= min_quantity AND min_quantity > 0
            """)
            metrics["low_stock_items"] = self.db_manager.cursor.fetchone()[0]
            
            # Experiment metrics
            self.db_manager.cursor.execute("SELECT COUNT(*) FROM experiments")
            metrics["total_experiments"] = self.db_manager.cursor.fetchone()[0]
            
            self.db_manager.cursor.execute("""
                SELECT COUNT(*) FROM experiments WHERE status = 'in_progress'
            """)
            metrics["active_experiments"] = self.db_manager.cursor.fetchone()[0]
            
            # User metrics
            self.db_manager.cursor.execute("SELECT COUNT(*) FROM users WHERE is_active = 1")
            metrics["active_users"] = self.db_manager.cursor.fetchone()[0]
            
            # Recent activity
            self.db_manager.cursor.execute("""
                SELECT COUNT(*) FROM audit_logs 
                WHERE timestamp >= datetime('now', '-24 hours')
            """)
            metrics["daily_activities"] = self.db_manager.cursor.fetchone()[0]
            
            return metrics
            
        finally:
            self.db_manager.disconnect()
    
    def _get_inventory_analytics(self) -> Dict[str, Any]:
        """Get detailed inventory analytics."""
        self.db_manager.connect()
        try:
            analytics = {}
            
            # Category distribution
            self.db_manager.cursor.execute("""
                SELECT category, COUNT(*) as count, SUM(quantity) as total_quantity
                FROM inventory_items 
                GROUP BY category
                ORDER BY count DESC
            """)
            categories = [
                {"category": row[0], "count": row[1], "total_quantity": row[2]}
                for row in self.db_manager.cursor.fetchall()
            ]
            analytics["category_distribution"] = categories
            
            # Location utilization
            self.db_manager.cursor.execute("""
                SELECT location, COUNT(*) as item_count
                FROM inventory_items 
                WHERE location IS NOT NULL
                GROUP BY location
                ORDER BY item_count DESC
            """)
            locations = [
                {"location": row[0], "item_count": row[1]}
                for row in self.db_manager.cursor.fetchall()
            ]
            analytics["location_utilization"] = locations
            
            # Expiry analysis
            self.db_manager.cursor.execute("""
                SELECT 
                    CASE 
                        WHEN expiry_date <= date('now') THEN 'expired'
                        WHEN expiry_date <= date('now', '+30 days') THEN 'expiring_soon'
                        WHEN expiry_date <= date('now', '+90 days') THEN 'expiring_later'
                        ELSE 'long_term'
                    END as expiry_status,
                    COUNT(*) as count
                FROM inventory_items 
                WHERE expiry_date IS NOT NULL
                GROUP BY expiry_status
            """)
            expiry_analysis = [
                {"status": row[0], "count": row[1]}
                for row in self.db_manager.cursor.fetchall()
            ]
            analytics["expiry_analysis"] = expiry_analysis
            
            # Usage trends (last 30 days)
            self.db_manager.cursor.execute("""
                SELECT 
                    date(timestamp) as date,
                    COUNT(*) as transaction_count,
                    SUM(ABS(quantity)) as total_quantity_moved
                FROM inventory_transactions 
                WHERE timestamp >= datetime('now', '-30 days')
                GROUP BY date(timestamp)
                ORDER BY date
            """)
            usage_trends = [
                {
                    "date": row[0], 
                    "transaction_count": row[1],
                    "total_quantity_moved": row[2]
                }
                for row in self.db_manager.cursor.fetchall()
            ]
            analytics["usage_trends"] = usage_trends
            
            return analytics
            
        finally:
            self.db_manager.disconnect()
    
    def _get_experiment_analytics(self) -> Dict[str, Any]:
        """Get experiment analytics."""
        self.db_manager.connect()
        try:
            analytics = {}
            
            # Status distribution
            self.db_manager.cursor.execute("""
                SELECT status, COUNT(*) as count
                FROM experiments 
                GROUP BY status
            """)
            status_dist = [
                {"status": row[0], "count": row[1]}
                for row in self.db_manager.cursor.fetchall()
            ]
            analytics["status_distribution"] = status_dist
            
            # Monthly creation trends
            self.db_manager.cursor.execute("""
                SELECT 
                    strftime('%Y-%m', created_at) as month,
                    COUNT(*) as count
                FROM experiments
                WHERE created_at >= datetime('now', '-12 months')
                GROUP BY strftime('%Y-%m', created_at)
                ORDER BY month
            """)
            monthly_trends = [
                {"month": row[0], "count": row[1]}
                for row in self.db_manager.cursor.fetchall()
            ]
            analytics["monthly_creation_trends"] = monthly_trends
            
            # Average duration analysis
            self.db_manager.cursor.execute("""
                SELECT 
                    status,
                    AVG(CASE 
                        WHEN end_date IS NOT NULL AND start_date IS NOT NULL 
                        THEN julianday(end_date) - julianday(start_date) 
                    END) as avg_duration_days
                FROM experiments
                WHERE start_date IS NOT NULL
                GROUP BY status
            """)
            duration_analysis = [
                {"status": row[0], "avg_duration_days": row[1] or 0}
                for row in self.db_manager.cursor.fetchall()
            ]
            analytics["duration_analysis"] = duration_analysis
            
            # Top researchers by experiment count
            self.db_manager.cursor.execute("""
                SELECT 
                    u.full_name,
                    COUNT(e.id) as experiment_count,
                    COUNT(CASE WHEN e.status = 'completed' THEN 1 END) as completed_count
                FROM users u
                LEFT JOIN experiments e ON u.id = e.created_by
                WHERE u.role IN ('researcher', 'admin', 'lab_manager')
                GROUP BY u.id, u.full_name
                HAVING experiment_count > 0
                ORDER BY experiment_count DESC
                LIMIT 10
            """)
            top_researchers = [
                {
                    "name": row[0],
                    "experiment_count": row[1],
                    "completed_count": row[2],
                    "completion_rate": (row[2] / row[1] * 100) if row[1] > 0 else 0
                }
                for row in self.db_manager.cursor.fetchall()
            ]
            analytics["top_researchers"] = top_researchers
            
            return analytics
            
        finally:
            self.db_manager.disconnect()
    
    def _get_user_analytics(self) -> Dict[str, Any]:
        """Get user analytics."""
        self.db_manager.connect()
        try:
            analytics = {}
            
            # Role distribution
            self.db_manager.cursor.execute("""
                SELECT role, COUNT(*) as count
                FROM users 
                WHERE is_active = 1
                GROUP BY role
            """)
            role_dist = [
                {"role": row[0], "count": row[1]}
                for row in self.db_manager.cursor.fetchall()
            ]
            analytics["role_distribution"] = role_dist
            
            # Activity patterns (last 30 days)
            self.db_manager.cursor.execute("""
                SELECT 
                    strftime('%H', al.timestamp) as hour,
                    COUNT(*) as activity_count
                FROM audit_logs al
                WHERE al.timestamp >= datetime('now', '-30 days')
                GROUP BY strftime('%H', al.timestamp)
                ORDER BY hour
            """)
            hourly_activity = [
                {"hour": int(row[0]), "activity_count": row[1]}
                for row in self.db_manager.cursor.fetchall()
            ]
            analytics["hourly_activity_pattern"] = hourly_activity
            
            # Most active users
            self.db_manager.cursor.execute("""
                SELECT 
                    u.full_name,
                    u.role,
                    COUNT(al.id) as activity_count,
                    MAX(al.timestamp) as last_activity
                FROM users u
                LEFT JOIN audit_logs al ON u.id = al.user_id
                WHERE u.is_active = 1 AND al.timestamp >= datetime('now', '-30 days')
                GROUP BY u.id, u.full_name, u.role
                ORDER BY activity_count DESC
                LIMIT 10
            """)
            active_users = [
                {
                    "name": row[0],
                    "role": row[1],
                    "activity_count": row[2],
                    "last_activity": row[3]
                }
                for row in self.db_manager.cursor.fetchall()
            ]
            analytics["most_active_users"] = active_users
            
            return analytics
            
        finally:
            self.db_manager.disconnect()
    
    def _get_trend_analytics(self) -> Dict[str, Any]:
        """Get trend analytics."""
        analytics = {}
        
        # Calculate growth rates
        current_month = datetime.now().replace(day=1)
        previous_month = (current_month - timedelta(days=1)).replace(day=1)
        
        self.db_manager.connect()
        try:
            # Inventory growth
            self.db_manager.cursor.execute("""
                SELECT COUNT(*) FROM inventory_items 
                WHERE added_at >= ? AND added_at < ?
            """, (previous_month.isoformat(), current_month.isoformat()))
            prev_inventory = self.db_manager.cursor.fetchone()[0]
            
            self.db_manager.cursor.execute("""
                SELECT COUNT(*) FROM inventory_items 
                WHERE added_at >= ?
            """, (current_month.isoformat(),))
            curr_inventory = self.db_manager.cursor.fetchone()[0]
            
            inventory_growth = ((curr_inventory - prev_inventory) / max(prev_inventory, 1)) * 100
            
            # Experiment growth
            self.db_manager.cursor.execute("""
                SELECT COUNT(*) FROM experiments 
                WHERE created_at >= ? AND created_at < ?
            """, (previous_month.isoformat(), current_month.isoformat()))
            prev_experiments = self.db_manager.cursor.fetchone()[0]
            
            self.db_manager.cursor.execute("""
                SELECT COUNT(*) FROM experiments 
                WHERE created_at >= ?
            """, (current_month.isoformat(),))
            curr_experiments = self.db_manager.cursor.fetchone()[0]
            
            experiment_growth = ((curr_experiments - prev_experiments) / max(prev_experiments, 1)) * 100
            
            analytics["growth_rates"] = {
                "inventory_growth": round(inventory_growth, 2),
                "experiment_growth": round(experiment_growth, 2)
            }
            
            return analytics
            
        finally:
            self.db_manager.disconnect()
    
    def _get_predictive_analytics(self) -> Dict[str, Any]:
        """Get predictive analytics and forecasts."""
        analytics = {}
        
        # Simple linear regression for inventory usage prediction
        self.db_manager.connect()
        try:
            # Get inventory usage data for last 90 days
            self.db_manager.cursor.execute("""
                SELECT 
                    julianday(date(timestamp)) - julianday('now', '-90 days') as day_offset,
                    SUM(ABS(quantity)) as daily_usage
                FROM inventory_transactions 
                WHERE timestamp >= datetime('now', '-90 days')
                  AND transaction_type = 'removal'
                GROUP BY date(timestamp)
                ORDER BY timestamp
            """)
            
            usage_data = self.db_manager.cursor.fetchall()
            
            if len(usage_data) > 5:  # Need minimum data points
                days = np.array([row[0] for row in usage_data])
                usage = np.array([row[1] for row in usage_data])
                
                # Simple linear regression
                slope, intercept = np.polyfit(days, usage, 1)
                
                # Predict next 30 days
                future_days = np.arange(91, 121)  # Days 91-120
                predicted_usage = slope * future_days + intercept
                
                analytics["usage_forecast"] = {
                    "trend": "increasing" if slope > 0 else "decreasing",
                    "daily_change_rate": round(slope, 2),
                    "predicted_monthly_usage": round(np.sum(predicted_usage), 2)
                }
            
            # Predict items that will need restocking
            self.db_manager.cursor.execute("""
                SELECT 
                    i.name,
                    i.quantity,
                    i.min_quantity,
                    AVG(ABS(t.quantity)) as avg_daily_usage
                FROM inventory_items i
                LEFT JOIN inventory_transactions t ON i.id = t.item_id
                WHERE t.timestamp >= datetime('now', '-30 days')
                  AND t.transaction_type = 'removal'
                  AND i.min_quantity > 0
                GROUP BY i.id, i.name, i.quantity, i.min_quantity
                HAVING avg_daily_usage > 0
            """)
            
            restock_predictions = []
            for row in self.db_manager.cursor.fetchall():
                name, current_qty, min_qty, daily_usage = row
                days_until_restock = max(0, (current_qty - min_qty) / daily_usage)
                
                if days_until_restock <= 30:  # Will need restocking in 30 days
                    restock_predictions.append({
                        "item_name": name,
                        "current_quantity": current_qty,
                        "min_quantity": min_qty,
                        "days_until_restock": round(days_until_restock, 1),
                        "priority": "high" if days_until_restock <= 7 else "medium"
                    })
            
            analytics["restock_predictions"] = sorted(
                restock_predictions, 
                key=lambda x: x["days_until_restock"]
            )
            
            return analytics
            
        finally:
            self.db_manager.disconnect()
    
    def _get_system_alerts(self) -> List[Dict[str, Any]]:
        """Get system alerts and recommendations."""
        alerts = []
        
        self.db_manager.connect()
        try:
            # Low stock alerts
            self.db_manager.cursor.execute("""
                SELECT name, quantity, min_quantity, location
                FROM inventory_items 
                WHERE quantity <= min_quantity AND min_quantity > 0
                ORDER BY (quantity / min_quantity) ASC
            """)
            
            for row in self.db_manager.cursor.fetchall():
                alerts.append({
                    "type": "low_stock",
                    "severity": "high",
                    "title": f"Low Stock: {row[0]}",
                    "message": f"Only {row[1]} {row[0]} remaining (minimum: {row[2]})",
                    "location": row[3],
                    "action": "restock_needed"
                })
            
            # Expired items
            self.db_manager.cursor.execute("""
                SELECT name, expiry_date, location
                FROM inventory_items 
                WHERE expiry_date <= date('now')
                ORDER BY expiry_date ASC
            """)
            
            for row in self.db_manager.cursor.fetchall():
                alerts.append({
                    "type": "expired_item",
                    "severity": "critical",
                    "title": f"Expired: {row[0]}",
                    "message": f"Item expired on {row[1]}",
                    "location": row[2],
                    "action": "disposal_required"
                })
            
            # Long-running experiments
            self.db_manager.cursor.execute("""
                SELECT title, start_date, created_by
                FROM experiments 
                WHERE status = 'in_progress' 
                  AND start_date <= date('now', '-90 days')
                ORDER BY start_date ASC
            """)
            
            for row in self.db_manager.cursor.fetchall():
                alerts.append({
                    "type": "long_running_experiment",
                    "severity": "medium",
                    "title": f"Long-running: {row[0]}",
                    "message": f"Experiment started on {row[1]} (90+ days ago)",
                    "action": "review_required"
                })
            
            return alerts[:20]  # Limit to 20 most important alerts
            
        finally:
            self.db_manager.disconnect()
    
    def _is_cached(self, key: str) -> bool:
        """Check if data is cached and still valid."""
        if key not in self.cache:
            return False
        
        cache_time = self.cache[key]["timestamp"]
        return (datetime.now() - cache_time).seconds < self.cache_ttl
    
    def _cache_data(self, key: str, data: Any):
        """Cache data with timestamp."""
        self.cache[key] = {
            "data": data,
            "timestamp": datetime.now()
        }
    
    def generate_report(self, report_type: str, date_range: Tuple[str, str] = None) -> Dict[str, Any]:
        """
        Generate detailed analytics report.
        
        Args:
            report_type: Type of report (inventory, experiments, users, etc.)
            date_range: Optional date range tuple (start_date, end_date)
            
        Returns:
            Detailed report data
        """
        if report_type == "inventory":
            return self._generate_inventory_report(date_range)
        elif report_type == "experiments":
            return self._generate_experiment_report(date_range)
        elif report_type == "users":
            return self._generate_user_report(date_range)
        else:
            return {"error": f"Unknown report type: {report_type}"}
    
    def _generate_inventory_report(self, date_range: Tuple[str, str] = None) -> Dict[str, Any]:
        """Generate detailed inventory report."""
        # Implementation for detailed inventory reporting
        return {
            "report_type": "inventory",
            "generated_at": datetime.now().isoformat(),
            "summary": self._get_inventory_analytics(),
            "recommendations": [
                "Consider implementing automated reordering for high-usage items",
                "Review storage locations for better organization",
                "Set up alerts for items approaching expiry"
            ]
        }
    
    def _generate_experiment_report(self, date_range: Tuple[str, str] = None) -> Dict[str, Any]:
        """Generate detailed experiment report."""
        return {
            "report_type": "experiments",
            "generated_at": datetime.now().isoformat(),
            "summary": self._get_experiment_analytics(),
            "recommendations": [
                "Encourage collaboration on long-running experiments",
                "Implement milestone tracking for better progress monitoring",
                "Consider experiment templates for common procedures"
            ]
        }
    
    def _generate_user_report(self, date_range: Tuple[str, str] = None) -> Dict[str, Any]:
        """Generate detailed user report."""
        return {
            "report_type": "users",
            "generated_at": datetime.now().isoformat(),
            "summary": self._get_user_analytics(),
            "recommendations": [
                "Provide training for less active users",
                "Consider role-based dashboards for different user types",
                "Implement user onboarding improvements"
            ]
        }
