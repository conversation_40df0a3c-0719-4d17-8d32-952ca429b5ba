import os
import logging
import json
from datetime import datetime
import sqlite3

class DatabaseMigration:
    """
    Database migration utility for the Science Laboratory Management System.
    Handles database schema migrations and version tracking.
    """
    
    def __init__(self, db, migrations_dir=None):
        """
        Initialize the database migration utility.
        
        Args:
            db: The database instance
            migrations_dir (str, optional): Directory containing migration scripts
        """
        self.db = db
        self.logger = logging.getLogger("db_migration")
        
        # Set default migrations directory if not specified
        if not migrations_dir:
            migrations_dir = os.path.join(os.getcwd(), "migrations")
        
        self.migrations_dir = migrations_dir
        
        # Create migrations directory if it doesn't exist
        os.makedirs(migrations_dir, exist_ok=True)
        
        # Ensure migration tracking table exists
        self.ensure_migration_table()
    
    def ensure_migration_table(self):
        """Ensure the migration tracking table exists."""
        try:
            # Create migration tracking table if it doesn't exist
            self.db.execute_query("""
                CREATE TABLE IF NOT EXISTS migrations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    version TEXT NOT NULL UNIQUE,
                    applied_at TEXT NOT NULL,
                    description TEXT
                )
            """)
            
            self.logger.info("Migration tracking table initialized")
        
        except Exception as e:
            self.logger.error(f"Error initializing migration table: {str(e)}")
            raise
    
    def get_current_version(self):
        """
        Get the current database version.
        
        Returns:
            str: The current version or None if no migrations applied
        """
        try:
            # Query the latest migration
            result = self.db.execute_query("""
                SELECT version FROM migrations 
                ORDER BY id DESC LIMIT 1
            """)
            
            if result and len(result) > 0:
                return result[0]["version"]
            else:
                return None
        
        except Exception as e:
            self.logger.error(f"Error getting current version: {str(e)}")
            return None
    
    def get_available_migrations(self):
        """
        Get a list of available migrations.
        
        Returns:
            list: List of migration versions sorted by version
        """
        try:
            # Get all migration files
            migration_files = [f for f in os.listdir(self.migrations_dir) if f.endswith(".sql")]
            
            # Extract versions from filenames (format: V{version}__{description}.sql)
            versions = []
            for file in migration_files:
                if file.startswith("V") and "__" in file:
                    version = file.split("__")[0][1:]  # Remove 'V' prefix
                    versions.append(version)
            
            # Sort versions
            versions.sort(key=lambda v: [int(x) for x in v.split(".")])
            
            return versions
        
        except Exception as e:
            self.logger.error(f"Error getting available migrations: {str(e)}")
            return []
    
    def get_pending_migrations(self):
        """
        Get a list of pending migrations.
        
        Returns:
            list: List of pending migration versions sorted by version
        """
        try:
            # Get current version
            current_version = self.get_current_version()
            
            # Get available migrations
            available_migrations = self.get_available_migrations()
            
            # If no migrations applied yet, all are pending
            if not current_version:
                return available_migrations
            
            # Filter migrations newer than current version
            current_version_parts = [int(x) for x in current_version.split(".")]
            
            pending_migrations = []
            for version in available_migrations:
                version_parts = [int(x) for x in version.split(".")]
                
                # Compare versions
                if version_parts > current_version_parts:
                    pending_migrations.append(version)
            
            return pending_migrations
        
        except Exception as e:
            self.logger.error(f"Error getting pending migrations: {str(e)}")
            return []
    
    def get_migration_script(self, version):
        """
        Get the migration script for a specific version.
        
        Args:
            version (str): The migration version
            
        Returns:
            tuple: (script, description) or (None, None) if not found
        """
        try:
            # Find migration file
            for file in os.listdir(self.migrations_dir):
                if file.startswith(f"V{version}__") and file.endswith(".sql"):
                    # Extract description from filename
                    description = file.split("__")[1].replace(".sql", "").replace("_", " ")
                    
                    # Read script
                    with open(os.path.join(self.migrations_dir, file), 'r') as f:
                        script = f.read()
                    
                    return (script, description)
            
            return (None, None)
        
        except Exception as e:
            self.logger.error(f"Error getting migration script: {str(e)}")
            return (None, None)
    
    def apply_migration(self, version):
        """
        Apply a specific migration.
        
        Args:
            version (str): The migration version
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get migration script
            script, description = self.get_migration_script(version)
            
            if not script:
                self.logger.error(f"Migration script not found for version {version}")
                return False
            
            # Apply migration
            self.logger.info(f"Applying migration {version}: {description}")
            
            # Execute script
            self.db.execute_script(script)
            
            # Record migration
            self.db.execute_query("""
                INSERT INTO migrations (version, applied_at, description)
                VALUES (?, ?, ?)
            """, (version, datetime.now().isoformat(), description))
            
            self.logger.info(f"Migration {version} applied successfully")
            
            return True
        
        except Exception as e:
            self.logger.error(f"Error applying migration {version}: {str(e)}")
            return False
    
    def migrate(self):
        """
        Apply all pending migrations.
        
        Returns:
            tuple: (success_count, total_count)
        """
        try:
            # Get pending migrations
            pending_migrations = self.get_pending_migrations()
            
            if not pending_migrations:
                self.logger.info("No pending migrations")
                return (0, 0)
            
            self.logger.info(f"Found {len(pending_migrations)} pending migrations")
            
            # Apply migrations
            success_count = 0
            
            for version in pending_migrations:
                if self.apply_migration(version):
                    success_count += 1
                else:
                    # Stop on first failure
                    break
            
            self.logger.info(f"Applied {success_count} of {len(pending_migrations)} migrations")
            
            return (success_count, len(pending_migrations))
        
        except Exception as e:
            self.logger.error(f"Error during migration: {str(e)}")
            return (0, 0)
    
    def create_migration(self, description, script):
        """
        Create a new migration script.
        
        Args:
            description (str): Migration description
            script (str): SQL script
            
        Returns:
            str: Path to the created migration file or None if failed
        """
        try:
            # Get current version
            current_version = self.get_current_version()
            
            # Generate new version
            if current_version:
                # Increment last part of version
                version_parts = [int(x) for x in current_version.split(".")]
                version_parts[-1] += 1
                new_version = ".".join(str(x) for x in version_parts)
            else:
                # First migration
                new_version = "1.0.0"
            
            # Format description for filename
            filename_description = description.replace(" ", "_").lower()
            
            # Generate filename
            filename = f"V{new_version}__{filename_description}.sql"
            
            # Create migration file
            file_path = os.path.join(self.migrations_dir, filename)
            
            with open(file_path, 'w') as f:
                f.write(f"-- Migration: {description}\n")
                f.write(f"-- Version: {new_version}\n")
                f.write(f"-- Created: {datetime.now().isoformat()}\n\n")
                f.write(script)
            
            self.logger.info(f"Created migration {new_version}: {description}")
            
            return file_path
        
        except Exception as e:
            self.logger.error(f"Error creating migration: {str(e)}")
            return None
    
    def get_migration_history(self):
        """
        Get the migration history.
        
        Returns:
            list: List of applied migrations
        """
        try:
            # Query migration history
            return self.db.execute_query("""
                SELECT * FROM migrations 
                ORDER BY id
            """)
        
        except Exception as e:
            self.logger.error(f"Error getting migration history: {str(e)}")
            return []
    
    def generate_schema_migration(self, output_file=None):
        """
        Generate a migration script for the current schema.
        
        Args:
            output_file (str, optional): Path to output file
            
        Returns:
            str: Path to the generated file or None if failed
        """
        try:
            # Connect to database
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()
            
            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT IN ('sqlite_sequence', 'migrations')")
            tables = [row[0] for row in cursor.fetchall()]
            
            # Generate script
            script = "-- Auto-generated schema migration\n\n"
            
            for table in tables:
                # Get table schema
                cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table}'")
                table_sql = cursor.fetchone()[0]
                
                script += f"{table_sql};\n\n"
                
                # Get indexes
                cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='index' AND tbl_name='{table}' AND sql IS NOT NULL")
                indexes = cursor.fetchall()
                
                for index in indexes:
                    script += f"{index[0]};\n"
                
                script += "\n"
            
            # Close connection
            conn.close()
            
            # Save to file if specified
            if output_file:
                with open(output_file, 'w') as f:
                    f.write(script)
                
                self.logger.info(f"Schema migration generated: {output_file}")
                
                return output_file
            else:
                # Create migration
                return self.create_migration("Full schema migration", script)
        
        except Exception as e:
            self.logger.error(f"Error generating schema migration: {str(e)}")
            return None