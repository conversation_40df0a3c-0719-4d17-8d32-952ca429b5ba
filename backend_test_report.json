{"summary": {"total_tests": 9, "passed_tests": 2, "failed_tests": 7, "success_rate": 22.22222222222222, "timestamp": "2025-05-31T13:57:09.352980"}, "results": [{"test": "Database Connection", "success": true, "message": "Connected successfully", "timestamp": "2025-05-31T13:56:55.983483"}, {"test": "Database Schema", "success": false, "message": "Missing tables: ['bookings']", "timestamp": "2025-05-31T13:56:56.124481"}, {"test": "Sample Data", "success": true, "message": "Found 3 users", "timestamp": "2025-05-31T13:56:56.169893"}, {"test": "API Health Check", "success": false, "message": "Connection error: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000248F5626FD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-05-31T13:57:00.389770"}, {"test": "User Login", "success": false, "message": "Connection error: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/auth/login (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000248F563CB20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-05-31T13:57:04.622660"}, {"test": "Inventory API", "success": false, "message": "No access token available", "timestamp": "2025-05-31T13:57:04.624415"}, {"test": "Model Classes", "success": false, "message": "__init__() missing 1 required positional argument: 'security'", "timestamp": "2025-05-31T13:57:04.679086"}, {"test": "Authentication Manager", "success": false, "message": "cannot unpack non-iterable bool object", "timestamp": "2025-05-31T13:57:05.179281"}, {"test": "API Documentation", "success": false, "message": "Connection error: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/docs (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000248F563C8E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "timestamp": "2025-05-31T13:57:09.346088"}]}