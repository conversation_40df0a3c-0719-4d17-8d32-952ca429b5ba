2025-05-27 14:45:22,041 - migration - INFO - Starting database migration
2025-05-27 14:45:22,048 - migration - INFO - Created backup: C:\Users\<USER>\Desktop\Science Laboratory Management system\backups\lab_system.db.20250527144522.bak
2025-05-27 14:45:22,071 - migration - INFO - Migrating table: users
2025-05-27 14:45:22,073 - migration - INFO - Creating table: users
2025-05-27 14:45:22,081 - migration - INFO - Inserting 3 rows into users
2025-05-27 14:45:22,088 - migration - INFO - Migrating table: sessions
2025-05-27 14:45:22,089 - migration - INFO - Creating table: sessions
2025-05-27 14:45:22,094 - migration - INFO - Migrating table: permissions
2025-05-27 14:45:22,096 - migration - INFO - Creating table: permissions
2025-05-27 14:45:22,101 - migration - INFO - Migrating table: role_permissions
2025-05-27 14:45:22,102 - migration - INFO - Creating table: role_permissions
2025-05-27 14:45:22,108 - migration - INFO - Migrating table: inventory_items
2025-05-27 14:45:22,109 - migration - INFO - Creating table: inventory_items
2025-05-27 14:45:22,115 - migration - INFO - Inserting 5 rows into inventory_items
2025-05-27 14:45:22,122 - migration - INFO - Migrating table: experiments
2025-05-27 14:45:22,123 - migration - INFO - Creating table: experiments
2025-05-27 14:45:22,130 - migration - INFO - Migrating table: experiment_collaborators
2025-05-27 14:45:22,131 - migration - INFO - Creating table: experiment_collaborators
2025-05-27 14:45:22,138 - migration - INFO - Migrating table: experiment_measurements
2025-05-27 14:45:22,140 - migration - INFO - Creating table: experiment_measurements
2025-05-27 14:45:22,146 - migration - INFO - Migrating table: experiment_files
2025-05-27 14:45:22,147 - migration - INFO - Creating table: experiment_files
2025-05-27 14:45:22,153 - migration - INFO - Migrating table: experiment_comments
2025-05-27 14:45:22,154 - migration - INFO - Creating table: experiment_comments
2025-05-27 14:45:22,160 - migration - INFO - Migrating table: lab_schedules
2025-05-27 14:45:22,161 - migration - INFO - Creating table: lab_schedules
2025-05-27 14:45:22,165 - migration - INFO - Migrating table: safety_incidents
2025-05-27 14:45:22,166 - migration - INFO - Creating table: safety_incidents
2025-05-27 14:45:22,173 - migration - INFO - Migrating table: safety_checklists
2025-05-27 14:45:22,173 - migration - INFO - Creating table: safety_checklists
2025-05-27 14:45:22,178 - migration - INFO - Migrating table: inventory_transactions
2025-05-27 14:45:22,179 - migration - INFO - Creating table: inventory_transactions
2025-05-27 14:45:22,189 - migration - INFO - Migrating table: audit_logs
2025-05-27 14:45:22,190 - migration - INFO - Creating table: audit_logs
2025-05-27 14:45:22,196 - migration - INFO - Migrating table: inventory
2025-05-27 14:45:22,197 - migration - INFO - Creating table: inventory
2025-05-27 14:45:22,205 - migration - INFO - Migrating table: events
2025-05-27 14:45:22,207 - migration - INFO - Creating table: events
2025-05-27 14:45:22,213 - migration - INFO - Migrating table: reports
2025-05-27 14:45:22,214 - migration - INFO - Creating table: reports
2025-05-27 14:45:22,223 - migration - INFO - Migrating table: activity_log
2025-05-27 14:45:22,224 - migration - INFO - Creating table: activity_log
2025-05-27 14:45:22,230 - migration - INFO - Migrating table: password_reset_tokens
2025-05-27 14:45:22,230 - migration - INFO - Creating table: password_reset_tokens
2025-05-27 14:45:22,244 - migration - INFO - Migrating table: notifications
2025-05-27 14:45:22,245 - migration - INFO - Creating table: notifications
2025-05-27 14:45:22,258 - migration - INFO - Migration from C:\Users\<USER>\Desktop\Science Laboratory Management system\data\lab_system.db to C:\Users\<USER>\Desktop\Science Laboratory Management system\data\lab_db.db completed successfully
2025-05-27 14:45:22,260 - migration - INFO - Migration from C:\Users\<USER>\Desktop\Science Laboratory Management system\data\lab_system.db to C:\Users\<USER>\Desktop\Science Laboratory Management system\data\lab_db.db completed successfully
2025-05-27 14:45:22,264 - migration - INFO - Created backup: C:\Users\<USER>\Desktop\Science Laboratory Management system\backups\lab_management.db.20250527144522.bak
2025-05-27 14:45:22,266 - migration - INFO - Migrating table: users
2025-05-27 14:45:22,268 - migration - INFO - Table already exists: users
2025-05-27 14:45:22,269 - migration - INFO - Inserting 3 rows into users
2025-05-27 14:45:22,270 - migration - ERROR - Error inserting row into users: table users has 16 columns but 10 values were supplied
2025-05-27 14:45:22,270 - migration - ERROR - Error inserting row into users: table users has 16 columns but 10 values were supplied
2025-05-27 14:45:22,270 - migration - ERROR - Error inserting row into users: table users has 16 columns but 10 values were supplied
2025-05-27 14:45:22,270 - migration - INFO - Migrating table: inventory
2025-05-27 14:45:22,271 - migration - INFO - Table already exists: inventory
2025-05-27 14:45:22,272 - migration - INFO - Migrating table: experiments
2025-05-27 14:45:22,275 - migration - INFO - Table already exists: experiments
2025-05-27 14:45:22,275 - migration - INFO - Migrating table: events
2025-05-27 14:45:22,276 - migration - INFO - Table already exists: events
2025-05-27 14:45:22,277 - migration - INFO - Migrating table: reports
2025-05-27 14:45:22,277 - migration - INFO - Table already exists: reports
2025-05-27 14:45:22,278 - migration - INFO - Migrating table: activity_log
2025-05-27 14:45:22,279 - migration - INFO - Table already exists: activity_log
2025-05-27 14:45:22,279 - migration - INFO - Migration from C:\Users\<USER>\Desktop\Science Laboratory Management system\data\lab_management.db to C:\Users\<USER>\Desktop\Science Laboratory Management system\data\lab_db.db completed successfully
2025-05-27 14:45:22,280 - migration - INFO - Migration from C:\Users\<USER>\Desktop\Science Laboratory Management system\data\lab_management.db to C:\Users\<USER>\Desktop\Science Laboratory Management system\data\lab_db.db completed successfully
2025-05-27 14:45:22,284 - migration - INFO - Created backup: C:\Users\<USER>\Desktop\Science Laboratory Management system\backups\test_db.db.20250527144522.bak
2025-05-27 14:45:22,288 - migration - INFO - Migrating table: test_table
2025-05-27 14:45:22,290 - migration - INFO - Creating table: test_table
2025-05-27 14:45:22,297 - migration - INFO - Inserting 2 rows into test_table
2025-05-27 14:45:22,304 - migration - INFO - Migration from C:\Users\<USER>\Desktop\Science Laboratory Management system\data\test_db.db to C:\Users\<USER>\Desktop\Science Laboratory Management system\data\lab_db.db completed successfully
2025-05-27 14:45:22,305 - migration - INFO - Migration from C:\Users\<USER>\Desktop\Science Laboratory Management system\data\test_db.db to C:\Users\<USER>\Desktop\Science Laboratory Management system\data\lab_db.db completed successfully
2025-05-27 14:45:22,305 - migration - INFO - Database migration completed
2025-05-27 21:38:30,821 - migration - INFO - Starting database migration
2025-05-27 21:38:30,823 - migration - WARNING - Source database not found: c:\Users\<USER>\Desktop\Science Laboratory Management system\data\lab_system.db
2025-05-27 21:38:31,007 - db_manager - INFO - Database initialized successfully
2025-05-27 21:38:31,018 - db_manager - INFO - Database initialized successfully
2025-05-27 21:38:31,018 - migration - INFO - Initialized new database: c:\Users\<USER>\Desktop\Science Laboratory Management system\data\lab_db.db
2025-05-27 21:38:31,019 - migration - INFO - Database migration completed
