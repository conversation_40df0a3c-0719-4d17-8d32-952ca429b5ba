import logging
import os
import io
import base64
import qrcode
from PIL import Image, ImageDraw, ImageFont
import cv2
import numpy as np
from pyzbar.pyzbar import decode
import json
import uuid
from datetime import datetime

class BarcodeSystem:
    """
    Barcode and QR code system for the Science Laboratory Management System.
    Handles generating, scanning, and processing barcodes and QR codes.
    """
    
    def __init__(self, config):
        """
        Initialize the barcode system.
        
        Args:
            config: The application configuration
        """
        self.config = config
        self.logger = logging.getLogger("barcode_system")
        
        # Create barcode directory if it doesn't exist
        self.barcode_dir = os.path.join(os.getcwd(), "data", "barcodes")
        os.makedirs(self.barcode_dir, exist_ok=True)
        
        # Load font for barcode labels
        try:
            font_path = os.path.join(os.getcwd(), "assets", "fonts", "arial.ttf")
            if os.path.exists(font_path):
                self.font = ImageFont.truetype(font_path, 12)
            else:
                self.font = ImageFont.load_default()
        except Exception as e:
            self.logger.warning(f"Could not load font: {str(e)}")
            self.font = ImageFont.load_default()
    
    def generate_qr_code(self, data, size=200, border=4, filename=None, include_label=True, label_text=None):
        """
        Generate a QR code.
        
        Args:
            data (str or dict): Data to encode in the QR code
            size (int, optional): Size of the QR code in pixels
            border (int, optional): Border size
            filename (str, optional): Filename to save the QR code
            include_label (bool, optional): Whether to include a label
            label_text (str, optional): Text for the label (if None, uses data)
            
        Returns:
            tuple: (image, filename) where image is a PIL Image and filename is the path if saved
        """
        try:
            # Convert data to string if it's a dictionary
            if isinstance(data, dict):
                data_str = json.dumps(data)
            else:
                data_str = str(data)
            
            # Create QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_M,
                box_size=10,
                border=border,
            )
            qr.add_data(data_str)
            qr.make(fit=True)
            
            # Create image
            img = qr.make_image(fill_color="black", back_color="white")
            
            # Resize image
            img = img.resize((size, size), Image.LANCZOS)
            
            # Add label if requested
            if include_label:
                # Determine label text
                if label_text is None:
                    if isinstance(data, dict) and "id" in data:
                        label_text = f"ID: {data['id']}"
                    elif isinstance(data, dict) and "name" in data:
                        label_text = data["name"]
                    else:
                        label_text = str(data)[:30]  # Limit length
                
                # Create new image with space for label
                label_height = 30
                new_img = Image.new('RGB', (size, size + label_height), color='white')
                new_img.paste(img, (0, 0))
                
                # Add label
                draw = ImageDraw.Draw(new_img)
                
                # Center text
                text_width = self.font.getsize(label_text)[0] if hasattr(self.font, 'getsize') else draw.textlength(label_text, font=self.font)
                text_x = (size - text_width) // 2
                
                draw.text((text_x, size + 5), label_text, fill='black', font=self.font)
                
                img = new_img
            
            # Save image if filename provided
            if filename:
                # If no extension, add .png
                if not os.path.splitext(filename)[1]:
                    filename += ".png"
                
                # If not absolute path, save in barcode directory
                if not os.path.isabs(filename):
                    filename = os.path.join(self.barcode_dir, filename)
                
                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(filename), exist_ok=True)
                
                # Save image
                img.save(filename)
                self.logger.info(f"QR code saved to {filename}")
            
            return (img, filename)
        
        except Exception as e:
            self.logger.error(f"Error generating QR code: {str(e)}")
            return (None, None)
    
    def generate_barcode_for_item(self, item, include_details=True, filename=None):
        """
        Generate a barcode for an inventory item.
        
        Args:
            item (dict): The inventory item
            include_details (bool, optional): Whether to include item details
            filename (str, optional): Filename to save the barcode
            
        Returns:
            tuple: (image, filename) where image is a PIL Image and filename is the path if saved
        """
        try:
            # Create data dictionary
            data = {
                "id": item["id"],
                "type": "inventory_item",
                "timestamp": datetime.now().isoformat()
            }
            
            # Add details if requested
            if include_details:
                data["name"] = item["name"]
                data["category"] = item["category"]
                if "location" in item:
                    data["location"] = item["location"]
            
            # Generate filename if not provided
            if not filename:
                filename = f"item_{item['id']}_{datetime.now().strftime('%Y%m%d%H%M%S')}.png"
            
            # Generate QR code
            return self.generate_qr_code(data, filename=filename, label_text=item["name"])
        
        except Exception as e:
            self.logger.error(f"Error generating barcode for item: {str(e)}")
            return (None, None)
    
    def generate_barcode_for_experiment(self, experiment, include_details=True, filename=None):
        """
        Generate a barcode for an experiment.
        
        Args:
            experiment (dict): The experiment
            include_details (bool, optional): Whether to include experiment details
            filename (str, optional): Filename to save the barcode
            
        Returns:
            tuple: (image, filename) where image is a PIL Image and filename is the path if saved
        """
        try:
            # Create data dictionary
            data = {
                "id": experiment["id"],
                "type": "experiment",
                "timestamp": datetime.now().isoformat()
            }
            
            # Add details if requested
            if include_details:
                data["title"] = experiment["title"]
                data["status"] = experiment["status"]
                data["user_id"] = experiment["user_id"]
            
            # Generate filename if not provided
            if not filename:
                filename = f"experiment_{experiment['id']}_{datetime.now().strftime('%Y%m%d%H%M%S')}.png"
            
            # Generate QR code
            return self.generate_qr_code(data, filename=filename, label_text=experiment["title"])
        
        except Exception as e:
            self.logger.error(f"Error generating barcode for experiment: {str(e)}")
            return (None, None)
    
    def generate_barcode_for_user(self, user, include_details=True, filename=None):
        """
        Generate a barcode for a user.
        
        Args:
            user (dict): The user
            include_details (bool, optional): Whether to include user details
            filename (str, optional): Filename to save the barcode
            
        Returns:
            tuple: (image, filename) where image is a PIL Image and filename is the path if saved
        """
        try:
            # Create data dictionary
            data = {
                "id": user["id"],
                "type": "user",
                "timestamp": datetime.now().isoformat()
            }
            
            # Add details if requested
            if include_details:
                data["username"] = user["username"]
                data["role"] = user["role"]
                data["department"] = user["department"]
            
            # Generate filename if not provided
            if not filename:
                filename = f"user_{user['id']}_{datetime.now().strftime('%Y%m%d%H%M%S')}.png"
            
            # Generate QR code
            return self.generate_qr_code(data, filename=filename, label_text=user["username"])
        
        except Exception as e:
            self.logger.error(f"Error generating barcode for user: {str(e)}")
            return (None, None)
    
    def generate_barcode_for_location(self, location, include_details=True, filename=None):
        """
        Generate a barcode for a location.
        
        Args:
            location (dict): The location
            include_details (bool, optional): Whether to include location details
            filename (str, optional): Filename to save the barcode
            
        Returns:
            tuple: (image, filename) where image is a PIL Image and filename is the path if saved
        """
        try:
            # Create data dictionary
            data = {
                "id": location["id"] if "id" in location else str(uuid.uuid4()),
                "type": "location",
                "timestamp": datetime.now().isoformat()
            }
            
            # Add details if requested
            if include_details:
                if "name" in location:
                    data["name"] = location["name"]
                if "description" in location:
                    data["description"] = location["description"]
            
            # Generate filename if not provided
            if not filename:
                loc_id = location["id"] if "id" in location else "new"
                filename = f"location_{loc_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.png"
            
            # Generate QR code
            label = location["name"] if "name" in location else f"Location {data['id']}"
            return self.generate_qr_code(data, filename=filename, label_text=label)
        
        except Exception as e:
            self.logger.error(f"Error generating barcode for location: {str(e)}")
            return (None, None)
    
    def scan_from_image(self, image_path):
        """
        Scan barcodes from an image file.
        
        Args:
            image_path (str): Path to the image file
            
        Returns:
            list: List of decoded barcode data
        """
        try:
            # Read image
            if isinstance(image_path, str):
                image = cv2.imread(image_path)
            else:
                # Assume it's a PIL Image
                image = cv2.cvtColor(np.array(image_path), cv2.COLOR_RGB2BGR)
            
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Scan for barcodes
            barcodes = decode(gray)
            
            # Process results
            results = []
            
            for barcode in barcodes:
                # Get barcode data
                barcode_data = barcode.data.decode('utf-8')
                barcode_type = barcode.type
                
                # Try to parse as JSON
                try:
                    data = json.loads(barcode_data)
                except:
                    data = barcode_data
                
                # Add to results
                results.append({
                    "data": data,
                    "type": barcode_type,
                    "rect": barcode.rect,
                    "polygon": barcode.polygon
                })
            
            return results
        
        except Exception as e:
            self.logger.error(f"Error scanning image: {str(e)}")
            return []
    
    def scan_from_camera(self, camera_index=0, timeout=30, callback=None):
        """
        Scan barcodes from a camera.
        
        Args:
            camera_index (int, optional): Camera index
            timeout (int, optional): Timeout in seconds
            callback (function, optional): Callback function for each frame
            
        Returns:
            dict: Decoded barcode data or None if timeout
        """
        try:
            # Open camera
            cap = cv2.VideoCapture(camera_index)
            
            if not cap.isOpened():
                self.logger.error(f"Could not open camera {camera_index}")
                return None
            
            # Set start time
            start_time = datetime.now()
            
            # Scan until timeout
            while (datetime.now() - start_time).total_seconds() < timeout:
                # Read frame
                ret, frame = cap.read()
                
                if not ret:
                    continue
                
                # Convert to grayscale
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                
                # Scan for barcodes
                barcodes = decode(gray)
                
                # Process results
                for barcode in barcodes:
                    # Get barcode data
                    barcode_data = barcode.data.decode('utf-8')
                    barcode_type = barcode.type
                    
                    # Try to parse as JSON
                    try:
                        data = json.loads(barcode_data)
                    except:
                        data = barcode_data
                    
                    # Draw rectangle around barcode
                    points = barcode.polygon
                    if len(points) > 4:
                        hull = cv2.convexHull(np.array([point for point in points], dtype=np.float32))
                        cv2.polylines(frame, [hull], True, (0, 255, 0), 2)
                    else:
                        pts = np.array([point for point in points], dtype=np.int32)
                        cv2.polylines(frame, [pts], True, (0, 255, 0), 2)
                    
                    # Draw barcode data
                    x, y, w, h = barcode.rect
                    cv2.putText(frame, barcode_type, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                    
                    # Call callback if provided
                    if callback:
                        callback(frame)
                    
                    # Release camera
                    cap.release()
                    
                    # Return data
                    return {
                        "data": data,
                        "type": barcode_type,
                        "rect": barcode.rect,
                        "polygon": barcode.polygon
                    }
                
                # Call callback if provided
                if callback:
                    callback(frame)
                
                # Wait for key press
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
            
            # Release camera
            cap.release()
            
            # Return None if timeout
            return None
        
        except Exception as e:
            self.logger.error(f"Error scanning from camera: {str(e)}")
            return None
    
    def process_barcode_data(self, data, db):
        """
        Process barcode data and return relevant information.
        
        Args:
            data (dict): The barcode data
            db: Database connection
            
        Returns:
            dict: Processed data with additional information
        """
        try:
            # Check if data is a dictionary
            if not isinstance(data, dict):
                return {"error": "Invalid barcode data format"}
            
            # Check if data has type and id
            if "type" not in data or "id" not in data:
                return {"error": "Missing type or id in barcode data"}
            
            # Process based on type
            if data["type"] == "inventory_item":
                # Get item from database
                item = db.execute_query(
                    "SELECT * FROM inventory_items WHERE id = ?",
                    (data["id"],)
                )
                
                if not item or len(item) == 0:
                    return {"error": f"Inventory item not found: {data['id']}"}
                
                # Return item data
                return {
                    "type": "inventory_item",
                    "item": item[0],
                    "barcode_data": data
                }
            
            elif data["type"] == "experiment":
                # Get experiment from database
                experiment = db.execute_query(
                    "SELECT * FROM experiments WHERE id = ?",
                    (data["id"],)
                )
                
                if not experiment or len(experiment) == 0:
                    return {"error": f"Experiment not found: {data['id']}"}
                
                # Return experiment data
                return {
                    "type": "experiment",
                    "experiment": experiment[0],
                    "barcode_data": data
                }
            
            elif data["type"] == "user":
                # Get user from database
                user = db.execute_query(
                    "SELECT id, username, full_name, email, department, role, status FROM users WHERE id = ?",
                    (data["id"],)
                )
                
                if not user or len(user) == 0:
                    return {"error": f"User not found: {data['id']}"}
                
                # Return user data
                return {
                    "type": "user",
                    "user": user[0],
                    "barcode_data": data
                }
            
            elif data["type"] == "location":
                # Return location data
                return {
                    "type": "location",
                    "location_id": data["id"],
                    "name": data.get("name", "Unknown Location"),
                    "barcode_data": data
                }
            
            else:
                # Unknown type
                return {
                    "type": "unknown",
                    "barcode_data": data
                }
        
        except Exception as e:
            self.logger.error(f"Error processing barcode data: {str(e)}")
            return {"error": f"Error processing barcode data: {str(e)}"}
    
    def get_barcode_as_base64(self, data, size=200, include_label=True, label_text=None):
        """
        Generate a QR code and return it as a base64 string.
        
        Args:
            data (str or dict): Data to encode in the QR code
            size (int, optional): Size of the QR code in pixels
            include_label (bool, optional): Whether to include a label
            label_text (str, optional): Text for the label
            
        Returns:
            str: Base64-encoded image data
        """
        try:
            # Generate QR code
            img, _ = self.generate_qr_code(data, size=size, include_label=include_label, label_text=label_text)
            
            if img is None:
                return None
            
            # Convert to base64
            buffered = io.BytesIO()
            img.save(buffered, format="PNG")
            img_str = base64.b64encode(buffered.getvalue()).decode()
            
            return f"data:image/png;base64,{img_str}"
        
        except Exception as e:
            self.logger.error(f"Error generating barcode as base64: {str(e)}")
            return None
    
    def generate_batch_barcodes(self, items, output_dir=None, prefix="batch"):
        """
        Generate barcodes for a batch of items.
        
        Args:
            items (list): List of items to generate barcodes for
            output_dir (str, optional): Directory to save barcodes
            prefix (str, optional): Prefix for filenames
            
        Returns:
            list: List of generated filenames
        """
        try:
            # Set output directory
            if not output_dir:
                output_dir = os.path.join(self.barcode_dir, f"{prefix}_{datetime.now().strftime('%Y%m%d%H%M%S')}")
            
            # Create directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)
            
            # Generate barcodes
            filenames = []
            
            for i, item in enumerate(items):
                # Determine item type
                if "name" in item and "category" in item:
                    # Inventory item
                    img, filename = self.generate_barcode_for_item(
                        item,
                        filename=os.path.join(output_dir, f"item_{item['id']}.png")
                    )
                elif "title" in item and "objective" in item:
                    # Experiment
                    img, filename = self.generate_barcode_for_experiment(
                        item,
                        filename=os.path.join(output_dir, f"experiment_{item['id']}.png")
                    )
                elif "username" in item and "role" in item:
                    # User
                    img, filename = self.generate_barcode_for_user(
                        item,
                        filename=os.path.join(output_dir, f"user_{item['id']}.png")
                    )
                else:
                    # Generic item
                    img, filename = self.generate_qr_code(
                        item,
                        filename=os.path.join(output_dir, f"item_{i}.png"),
                        label_text=str(item.get("id", i))
                    )
                
                if filename:
                    filenames.append(filename)
            
            return filenames
        
        except Exception as e:
            self.logger.error(f"Error generating batch barcodes: {str(e)}")
            return []
