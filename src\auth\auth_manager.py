"""
Authentication manager for the Science Laboratory Management System.
Handles user authentication and session management.
"""
import logging
import json
import os
from datetime import datetime, timedelta

# Set up logging
logger = logging.getLogger('auth_manager')

class AuthManager:
    """
    Authentication manager class for the Science Laboratory Management System.
    Handles user authentication and session management.
    """
    
    def __init__(self, db_manager):
        """
        Initialize the authentication manager.
        
        Args:
            db_manager: The database manager
        """
        self.db_manager = db_manager
        self.current_user = None
        self.session_file = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
            'data',
            'session.json'
        )
        
        # Ensure the data directory exists
        os.makedirs(os.path.dirname(self.session_file), exist_ok=True)
        
        # Try to load existing session
        self.load_session()
    
    def login(self, username, password):
        """
        Log in a user.
        
        Args:
            username (str): The username
            password (str): The password
            
        Returns:
            bool: True if login successful, False otherwise
        """
        # Authenticate user
        user = self.db_manager.authenticate_user(username, password)
        
        if user is None:
            return False
        
        # Set current user
        self.current_user = user
        
        # Save session
        self.save_session()
        
        logger.info(f"User logged in: {username}")
        return True
    
    def logout(self):
        """
        Log out the current user.
        
        Returns:
            bool: True if logout successful, False otherwise
        """
        if self.current_user is None:
            return False
        
        # Log activity
        self.db_manager.log_activity(
            self.current_user['id'],
            "logout",
            f"User logged out: {self.current_user['username']}"
        )
        
        # Clear current user
        username = self.current_user['username']
        self.current_user = None
        
        # Clear session
        self.clear_session()
        
        logger.info(f"User logged out: {username}")
        return True
    
    def is_authenticated(self):
        """
        Check if a user is authenticated.
        
        Returns:
            bool: True if authenticated, False otherwise
        """
        return self.current_user is not None
    
    def get_current_user(self):
        """
        Get the current user.
        
        Returns:
            dict: The current user data if authenticated, None otherwise
        """
        return self.current_user
    
    def save_session(self):
        """
        Save the current session to a file.
        
        Returns:
            bool: True if successful, False otherwise
        """
        if self.current_user is None:
            return False
        
        try:
            # Create session data
            session_data = {
                'user_id': self.current_user['id'],
                'username': self.current_user['username'],
                'role': self.current_user['role'],
                'timestamp': datetime.now().isoformat(),
                'expires': (datetime.now() + timedelta(days=1)).isoformat()
            }
            
            # Save to file
            with open(self.session_file, 'w') as f:
                json.dump(session_data, f)
            
            return True
        except Exception as e:
            logger.error(f"Error saving session: {str(e)}")
            return False
    
    def load_session(self):
        """
        Load the session from a file.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if session file exists
            if not os.path.exists(self.session_file):
                return False
            
            # Load session data
            with open(self.session_file, 'r') as f:
                session_data = json.load(f)
            
            # Check if session is expired
            expires = datetime.fromisoformat(session_data['expires'])
            if expires < datetime.now():
                logger.info("Session expired")
                self.clear_session()
                return False
            
            # Get user data
            user_id = session_data['user_id']
            user = self.db_manager.get_user(user_id)
            
            if user is None:
                logger.warning(f"Session user not found: {user_id}")
                self.clear_session()
                return False
            
            # Set current user
            self.current_user = user
            
            logger.info(f"Session loaded for user: {user['username']}")
            return True
        except Exception as e:
            logger.error(f"Error loading session: {str(e)}")
            return False
    
    def clear_session(self):
        """
        Clear the session.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if session file exists
            if os.path.exists(self.session_file):
                os.remove(self.session_file)
            
            return True
        except Exception as e:
            logger.error(f"Error clearing session: {str(e)}")
            return False
    
    def has_permission(self, permission):
        """
        Check if the current user has a specific permission.
        
        Args:
            permission (str): The permission to check
            
        Returns:
            bool: True if the user has the permission, False otherwise
        """
        if not self.is_authenticated() or self.current_user is None:
            return False
        
        # For simplicity, we'll use role-based permissions
        # Add additional safety check to ensure current_user has 'role' key
        if 'role' not in self.current_user:
            logger.warning("User object missing 'role' attribute")
            return False
            
        role = self.current_user['role']
        
        # Admin has all permissions
        if role == 'admin':
            return True
        
        # Define role-based permissions
        permissions = {
            'staff': [
                'view_inventory',
                'add_inventory',
                'edit_inventory',
                'view_experiments',
                'add_experiments',
                'edit_experiments',
                'view_scheduling',
                'add_events',
                'edit_events',
                'view_reports',
                'generate_reports'
            ],
            'user': [
                'view_inventory',
                'view_experiments',
                'view_scheduling',
                'view_reports'
            ]
        }
        
        # Check if the user's role has the permission
        return permission in permissions.get(role, [])