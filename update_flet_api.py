#!/usr/bin/env python
"""
Script to update Flet API usage in the codebase.
"""
import os
import re

def update_file(file_path):
    """
    Update Flet API usage in a file.
    
    Args:
        file_path (str): Path to the file to update
    """
    try:
        # Read the file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace ft.colors with ft.Colors
        content = re.sub(r'ft\.colors\.', 'ft.Colors.', content)
        
        # Replace ft.icons with ft.Icons
        content = re.sub(r'ft\.icons\.', 'ft.Icons.', content)
        
        # Replace ft.ThemeVisualDensity with ft.VisualDensity
        content = re.sub(r'ft\.ThemeVisualDensity\.', 'ft.VisualDensity.', content)
        
        # Replace Badge(value= with Badge(text=
        content = re.sub(r'Badge\(value=', 'Badge(text=', content)
        
        # Write the updated content back to the file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Updated {file_path}")
        
    except Exception as e:
        print(f"Error updating {file_path}: {str(e)}")

def update_directory(directory):
    """
    Update Flet API usage in all Python files in a directory.
    
    Args:
        directory (str): Path to the directory to update
    """
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                update_file(file_path)

if __name__ == "__main__":
    # Update the src directory
    update_directory("src")