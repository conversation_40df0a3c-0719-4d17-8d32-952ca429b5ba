"""
Responsive Navigation System
Adapts navigation for different screen sizes and devices
"""
import flet as ft
from typing import List, Dict, Any, Callable, Optional
from enum import Enum


class ScreenSize(Enum):
    """Screen size categories."""
    MOBILE = "mobile"      # < 768px
    TABLET = "tablet"      # 768px - 1024px
    DESKTOP = "desktop"    # > 1024px


class NavigationItem:
    """Represents a navigation item."""
    
    def __init__(self, id: str, title: str, icon: str, route: str, 
                 badge: str = None, requires_auth: bool = True):
        self.id = id
        self.title = title
        self.icon = icon
        self.route = route
        self.badge = badge
        self.requires_auth = requires_auth


class ResponsiveNavigation:
    """
    Responsive navigation system that adapts to different screen sizes.
    """
    
    def __init__(self, on_navigate: Callable[[str], None] = None, 
                 current_user: Dict[str, Any] = None):
        """
        Initialize responsive navigation.
        
        Args:
            on_navigate: Callback function for navigation
            current_user: Current user information
        """
        self.on_navigate = on_navigate
        self.current_user = current_user
        self.current_route = "dashboard"
        self.screen_size = ScreenSize.DESKTOP
        self.is_mobile_menu_open = False
        
        # Navigation items
        self.nav_items = [
            NavigationItem("dashboard", "Dashboard", "dashboard", "dashboard"),
            NavigationItem("inventory", "Inventory", "inventory", "inventory"),
            NavigationItem("experiments", "Experiments", "science", "experiments"),
            NavigationItem("scheduling", "Scheduling", "calendar_month", "scheduling"),
            NavigationItem("reports", "Reports", "analytics", "reports"),
            NavigationItem("users", "Users", "people", "users"),
        ]
        
        # UI components
        self.mobile_drawer = None
        self.tablet_nav_rail = None
        self.desktop_nav_rail = None
        self.bottom_nav_bar = None
        self.hamburger_button = None
    
    def detect_screen_size(self, page: ft.Page) -> ScreenSize:
        """Detect current screen size."""
        if hasattr(page, 'window') and page.window:
            width = page.window.width or 1200
        else:
            width = 1200  # Default
        
        if width < 768:
            return ScreenSize.MOBILE
        elif width < 1024:
            return ScreenSize.TABLET
        else:
            return ScreenSize.DESKTOP
    
    def create_responsive_navigation(self, page: ft.Page) -> Dict[str, ft.Control]:
        """Create navigation components for current screen size."""
        self.screen_size = self.detect_screen_size(page)
        
        components = {}
        
        if self.screen_size == ScreenSize.MOBILE:
            components.update(self._create_mobile_navigation(page))
        elif self.screen_size == ScreenSize.TABLET:
            components.update(self._create_tablet_navigation(page))
        else:
            components.update(self._create_desktop_navigation(page))
        
        return components
    
    def _create_mobile_navigation(self, page: ft.Page) -> Dict[str, ft.Control]:
        """Create mobile navigation components."""
        # Hamburger menu button
        self.hamburger_button = ft.IconButton(
            icon=ft.icons.MENU,
            icon_color=ft.colors.WHITE,
            tooltip="Open menu",
            on_click=self._toggle_mobile_menu,
        )
        
        # Mobile drawer
        self.mobile_drawer = ft.NavigationDrawer(
            controls=[
                # Header
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Icon(ft.icons.SCIENCE, size=32, color=ft.colors.BLUE),
                            ft.Text("Lab Management", size=18, weight=ft.FontWeight.BOLD),
                        ]),
                        ft.Text("Mobile Navigation", size=12, color=ft.colors.GREY_600),
                        ft.Divider(),
                    ], spacing=8),
                    padding=20,
                ),
                # Navigation items
                *[self._create_drawer_item(item) for item in self.nav_items],
                ft.Divider(),
                # User section
                self._create_user_section(),
            ],
            bgcolor=ft.colors.WHITE,
        )
        
        # Bottom navigation bar
        self.bottom_nav_bar = ft.NavigationBar(
            destinations=[
                ft.NavigationDestination(
                    icon=getattr(ft.icons, f"{item.icon.upper()}_OUTLINED", ft.icons.CIRCLE_OUTLINED),
                    selected_icon=getattr(ft.icons, item.icon.upper(), ft.icons.CIRCLE),
                    label=item.title,
                )
                for item in self.nav_items[:5]  # Limit to 5 items for mobile
            ],
            selected_index=self._get_selected_index(),
            on_change=self._on_bottom_nav_change,
            bgcolor=ft.colors.WHITE,
            indicator_color=ft.colors.BLUE_100,
        )
        
        return {
            "hamburger_button": self.hamburger_button,
            "drawer": self.mobile_drawer,
            "bottom_nav": self.bottom_nav_bar,
        }
    
    def _create_tablet_navigation(self, page: ft.Page) -> Dict[str, ft.Control]:
        """Create tablet navigation components."""
        # Compact navigation rail
        self.tablet_nav_rail = ft.NavigationRail(
            selected_index=self._get_selected_index(),
            label_type=ft.NavigationRailLabelType.SELECTED,
            min_width=80,
            extended=False,
            bgcolor=ft.colors.BLUE_50,
            destinations=[
                ft.NavigationRailDestination(
                    icon=getattr(ft.icons, f"{item.icon.upper()}_OUTLINED", ft.icons.CIRCLE_OUTLINED),
                    selected_icon=getattr(ft.icons, item.icon.upper(), ft.icons.CIRCLE),
                    label=item.title,
                    padding=12,
                )
                for item in self.nav_items
            ],
            on_change=self._on_nav_rail_change,
            leading=ft.Container(
                content=ft.Column([
                    ft.Icon(ft.icons.SCIENCE, size=32, color=ft.colors.BLUE),
                    ft.Text("Lab", size=10, weight=ft.FontWeight.BOLD, color=ft.colors.BLUE),
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.only(top=20, bottom=10),
            ),
            trailing=ft.Container(
                content=ft.Column([
                    ft.IconButton(
                        icon=ft.icons.SETTINGS,
                        tooltip="Settings",
                        on_click=lambda e: self._navigate_to("settings"),
                    ),
                    ft.IconButton(
                        icon=ft.icons.LOGOUT,
                        tooltip="Logout",
                        on_click=self._logout,
                    ),
                ]),
                padding=ft.padding.only(bottom=20),
            ),
        )
        
        return {
            "nav_rail": self.tablet_nav_rail,
        }
    
    def _create_desktop_navigation(self, page: ft.Page) -> Dict[str, ft.Control]:
        """Create desktop navigation components."""
        # Extended navigation rail
        self.desktop_nav_rail = ft.NavigationRail(
            selected_index=self._get_selected_index(),
            label_type=ft.NavigationRailLabelType.ALL,
            min_width=100,
            min_extended_width=220,
            extended=True,
            bgcolor=ft.colors.BLUE_50,
            destinations=[
                ft.NavigationRailDestination(
                    icon=getattr(ft.icons, f"{item.icon.upper()}_OUTLINED", ft.icons.CIRCLE_OUTLINED),
                    selected_icon=getattr(ft.icons, item.icon.upper(), ft.icons.CIRCLE),
                    label=item.title,
                    padding=15,
                )
                for item in self.nav_items
            ],
            on_change=self._on_nav_rail_change,
            leading=ft.Container(
                content=ft.Column([
                    ft.Container(
                        content=ft.Icon(ft.icons.SCIENCE_OUTLINED, size=40, color=ft.colors.BLUE),
                        margin=ft.margin.only(top=20, bottom=5),
                    ),
                    ft.Text("Lab Management", size=14, weight=ft.FontWeight.BOLD, color=ft.colors.BLUE),
                    ft.Divider(thickness=1, height=20),
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.only(bottom=10),
            ),
            trailing=ft.Container(
                content=ft.Column([
                    ft.IconButton(
                        icon=ft.icons.SETTINGS,
                        icon_color=ft.colors.BLUE_900,
                        tooltip="Settings",
                        on_click=lambda e: self._navigate_to("settings"),
                    ),
                    ft.IconButton(
                        icon=ft.icons.HELP_OUTLINE,
                        icon_color=ft.colors.BLUE_900,
                        tooltip="Help",
                        on_click=lambda e: self._navigate_to("help"),
                    ),
                    ft.IconButton(
                        icon=ft.icons.LOGOUT,
                        icon_color=ft.colors.RED,
                        tooltip="Logout",
                        on_click=self._logout,
                    ),
                    ft.Container(height=20),
                    ft.IconButton(
                        icon=ft.icons.MENU,
                        icon_color=ft.colors.BLUE_900,
                        tooltip="Toggle Navigation",
                        on_click=self._toggle_desktop_nav,
                    ),
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.only(bottom=20),
            ),
        )
        
        return {
            "nav_rail": self.desktop_nav_rail,
        }
    
    def _create_drawer_item(self, item: NavigationItem) -> ft.Container:
        """Create a drawer navigation item."""
        is_selected = self.current_route == item.route
        
        return ft.Container(
            content=ft.ListTile(
                leading=ft.Icon(
                    getattr(ft.icons, item.icon.upper(), ft.icons.CIRCLE),
                    color=ft.colors.BLUE if is_selected else ft.colors.GREY_600
                ),
                title=ft.Text(
                    item.title,
                    weight=ft.FontWeight.BOLD if is_selected else ft.FontWeight.NORMAL,
                    color=ft.colors.BLUE if is_selected else ft.colors.BLACK,
                ),
                trailing=ft.Container(
                    content=ft.Text(item.badge, size=10, color=ft.colors.WHITE),
                    bgcolor=ft.colors.RED,
                    border_radius=10,
                    padding=ft.padding.symmetric(horizontal=6, vertical=2),
                    visible=bool(item.badge),
                ),
                on_click=lambda e, route=item.route: self._navigate_to(route),
            ),
            bgcolor=ft.colors.BLUE_50 if is_selected else None,
            border_radius=8,
            margin=ft.margin.symmetric(horizontal=8, vertical=2),
        )
    
    def _create_user_section(self) -> ft.Container:
        """Create user section for drawer."""
        if not self.current_user:
            return ft.Container()
        
        return ft.Container(
            content=ft.Column([
                ft.ListTile(
                    leading=ft.CircleAvatar(
                        content=ft.Text(self.current_user.get("username", "U")[0].upper()),
                        bgcolor=ft.colors.BLUE,
                    ),
                    title=ft.Text(self.current_user.get("username", "User")),
                    subtitle=ft.Text(self.current_user.get("role", "User").title()),
                ),
                ft.Row([
                    ft.TextButton("Profile", on_click=lambda e: self._navigate_to("profile")),
                    ft.TextButton("Settings", on_click=lambda e: self._navigate_to("settings")),
                    ft.TextButton("Logout", on_click=self._logout),
                ], alignment=ft.MainAxisAlignment.SPACE_AROUND),
            ]),
            padding=10,
        )
    
    def _get_selected_index(self) -> int:
        """Get selected navigation index."""
        for i, item in enumerate(self.nav_items):
            if item.route == self.current_route:
                return i
        return 0
    
    def _on_nav_rail_change(self, e):
        """Handle navigation rail changes."""
        index = e.control.selected_index
        if 0 <= index < len(self.nav_items):
            self._navigate_to(self.nav_items[index].route)
    
    def _on_bottom_nav_change(self, e):
        """Handle bottom navigation changes."""
        index = e.control.selected_index
        if 0 <= index < len(self.nav_items):
            self._navigate_to(self.nav_items[index].route)
    
    def _toggle_mobile_menu(self, e):
        """Toggle mobile menu drawer."""
        if self.mobile_drawer:
            self.mobile_drawer.open = not self.mobile_drawer.open
            e.page.update()
    
    def _toggle_desktop_nav(self, e):
        """Toggle desktop navigation rail."""
        if self.desktop_nav_rail:
            self.desktop_nav_rail.extended = not self.desktop_nav_rail.extended
            
            # Update toggle button icon
            if self.desktop_nav_rail.extended:
                e.control.icon = ft.icons.MENU
                e.control.tooltip = "Collapse Navigation"
            else:
                e.control.icon = ft.icons.MENU_OPEN
                e.control.tooltip = "Expand Navigation"
            
            e.page.update()
    
    def _navigate_to(self, route: str):
        """Navigate to a route."""
        self.current_route = route
        
        # Close mobile drawer if open
        if self.mobile_drawer and self.mobile_drawer.open:
            self.mobile_drawer.open = False
        
        # Update navigation selections
        if self.tablet_nav_rail:
            self.tablet_nav_rail.selected_index = self._get_selected_index()
        if self.desktop_nav_rail:
            self.desktop_nav_rail.selected_index = self._get_selected_index()
        if self.bottom_nav_bar:
            self.bottom_nav_bar.selected_index = self._get_selected_index()
        
        # Execute navigation callback
        if self.on_navigate:
            self.on_navigate(route)
    
    def _logout(self, e):
        """Handle logout."""
        # Implement logout logic
        if self.on_navigate:
            self.on_navigate("login")
    
    def update_route(self, route: str):
        """Update current route."""
        self.current_route = route
        
        # Update all navigation components
        if self.tablet_nav_rail:
            self.tablet_nav_rail.selected_index = self._get_selected_index()
        if self.desktop_nav_rail:
            self.desktop_nav_rail.selected_index = self._get_selected_index()
        if self.bottom_nav_bar:
            self.bottom_nav_bar.selected_index = self._get_selected_index()
    
    def update_badge(self, item_id: str, badge: str = None):
        """Update badge for navigation item."""
        for item in self.nav_items:
            if item.id == item_id:
                item.badge = badge
                break
    
    def set_user(self, user: Dict[str, Any]):
        """Set current user."""
        self.current_user = user
    
    def handle_window_resize(self, page: ft.Page):
        """Handle window resize events."""
        new_screen_size = self.detect_screen_size(page)
        
        if new_screen_size != self.screen_size:
            self.screen_size = new_screen_size
            # Recreate navigation for new screen size
            return self.create_responsive_navigation(page)
        
        return None
    
    def get_current_navigation_type(self) -> str:
        """Get current navigation type."""
        return self.screen_size.value
    
    def is_mobile(self) -> bool:
        """Check if current screen is mobile."""
        return self.screen_size == ScreenSize.MOBILE
    
    def is_tablet(self) -> bool:
        """Check if current screen is tablet."""
        return self.screen_size == ScreenSize.TABLET
    
    def is_desktop(self) -> bool:
        """Check if current screen is desktop."""
        return self.screen_size == ScreenSize.DESKTOP
