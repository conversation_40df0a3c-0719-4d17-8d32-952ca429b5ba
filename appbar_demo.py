#!/usr/bin/env python
"""
AppBar Demo - Science Laboratory Management System
Demonstrates different AppBar configurations and features
"""
import flet as ft


def main(page: ft.Page):
    """Main demo application showing different AppBar styles."""
    page.title = "AppBar Demo - Lab Management System"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window_width = 1200
    page.window_height = 800
    page.padding = 0
    
    # Current AppBar index
    current_appbar = 0
    
    def switch_appbar(e):
        """Switch between different AppBar styles."""
        nonlocal current_appbar
        current_appbar = (current_appbar + 1) % 4
        update_appbar()
    
    def show_notification(message):
        """Show a notification."""
        page.show_snack_bar(ft.SnackBar(content=ft.Text(message)))
    
    def update_appbar():
        """Update the AppBar based on current selection."""
        if current_appbar == 0:
            # Basic AppBar
            page.appbar = ft.AppBar(
                title=ft.Text("Basic AppBar"),
                center_title=False,
                bgcolor=ft.colors.BLUE,
                color=ft.colors.WHITE,
                actions=[
                    ft.IconButton(
                        icon=ft.icons.ACCOUNT_CIRCLE,
                        tooltip="User Profile",
                        on_click=lambda e: show_notification("User Profile clicked")
                    ),
                    ft.IconButton(
                        icon=ft.icons.HELP_OUTLINE,
                        tooltip="Help",
                        on_click=lambda e: show_notification("Help clicked")
                    ),
                ],
            )
            content.value = "🎯 Basic AppBar\n\nThis is the simplest AppBar with:\n• Title text\n• Blue background\n• User profile icon\n• Help icon"
            
        elif current_appbar == 1:
            # Enhanced AppBar (like our Lab Management System)
            page.appbar = ft.AppBar(
                leading=ft.Icon(ft.icons.SCIENCE, color=ft.colors.WHITE),
                title=ft.Row([
                    ft.Text("Lab Management", weight=ft.FontWeight.BOLD),
                    ft.Icon(ft.icons.CHEVRON_RIGHT, size=20, color=ft.colors.WHITE70),
                    ft.Text("Dashboard", color=ft.colors.WHITE70),
                ], spacing=5),
                center_title=False,
                bgcolor=ft.colors.BLUE,
                color=ft.colors.WHITE,
                toolbar_height=60,
                actions=[
                    ft.IconButton(
                        icon=ft.icons.SEARCH,
                        tooltip="Global Search",
                        on_click=lambda e: show_notification("Search clicked")
                    ),
                    ft.IconButton(
                        icon=ft.icons.NOTIFICATIONS,
                        tooltip="Notifications",
                        badge="3",
                        on_click=lambda e: show_notification("Notifications clicked")
                    ),
                    ft.IconButton(
                        icon=ft.icons.ACCOUNT_CIRCLE,
                        tooltip="User Profile",
                        on_click=lambda e: show_notification("User Profile clicked")
                    ),
                    ft.IconButton(
                        icon=ft.icons.HELP_OUTLINE,
                        tooltip="Help",
                        on_click=lambda e: show_notification("Help clicked")
                    ),
                ],
                elevation=4,
            )
            content.value = "🚀 Enhanced AppBar (Lab Management Style)\n\nThis AppBar includes:\n• Science icon (leading)\n• Breadcrumb navigation\n• Search functionality\n• Notification badge\n• Multiple action buttons\n• Elevated shadow"
            
        elif current_appbar == 2:
            # Dark Theme AppBar
            page.appbar = ft.AppBar(
                leading=ft.Icon(ft.icons.BIOTECH, color=ft.colors.WHITE),
                title=ft.Text("Dark Theme Lab System", weight=ft.FontWeight.BOLD),
                center_title=True,
                bgcolor=ft.colors.GREY_900,
                color=ft.colors.WHITE,
                actions=[
                    ft.IconButton(
                        icon=ft.icons.BRIGHTNESS_6,
                        tooltip="Toggle Theme",
                        on_click=lambda e: show_notification("Theme toggle clicked")
                    ),
                    ft.IconButton(
                        icon=ft.icons.SETTINGS,
                        tooltip="Settings",
                        on_click=lambda e: show_notification("Settings clicked")
                    ),
                    ft.PopupMenuButton(
                        items=[
                            ft.PopupMenuItem(text="Profile", icon=ft.icons.PERSON),
                            ft.PopupMenuItem(text="Settings", icon=ft.icons.SETTINGS),
                            ft.PopupMenuItem(),  # Divider
                            ft.PopupMenuItem(text="Logout", icon=ft.icons.LOGOUT),
                        ]
                    ),
                ],
            )
            content.value = "🌙 Dark Theme AppBar\n\nFeatures:\n• Dark background (Grey 900)\n• Centered title\n• Biotech icon\n• Theme toggle button\n• Popup menu for user actions\n• Modern dark design"
            
        else:
            # Colorful AppBar
            page.appbar = ft.AppBar(
                leading=ft.Container(
                    content=ft.Icon(ft.icons.SCIENCE, color=ft.colors.WHITE),
                    bgcolor=ft.colors.ORANGE,
                    border_radius=20,
                    padding=8,
                    margin=8,
                ),
                title=ft.Row([
                    ft.Text("🧪", size=24),
                    ft.Text("Colorful Lab System", weight=ft.FontWeight.BOLD),
                ], spacing=10),
                center_title=False,
                bgcolor=ft.colors.PURPLE,
                color=ft.colors.WHITE,
                actions=[
                    ft.Container(
                        content=ft.IconButton(
                            icon=ft.icons.FAVORITE,
                            icon_color=ft.colors.WHITE,
                            tooltip="Favorites"
                        ),
                        bgcolor=ft.colors.PINK,
                        border_radius=20,
                        margin=4,
                    ),
                    ft.Container(
                        content=ft.IconButton(
                            icon=ft.icons.STAR,
                            icon_color=ft.colors.WHITE,
                            tooltip="Star"
                        ),
                        bgcolor=ft.colors.AMBER,
                        border_radius=20,
                        margin=4,
                    ),
                ],
            )
            content.value = "🎨 Colorful AppBar\n\nCreative features:\n• Custom colored containers\n• Emoji in title\n• Purple background\n• Rounded icon containers\n• Colorful action buttons\n• Fun, modern design"
        
        page.update()
    
    # Content area
    content = ft.Text(
        "Welcome to the AppBar Demo!",
        size=16,
        text_align=ft.TextAlign.CENTER,
    )
    
    # Demo controls
    demo_controls = ft.Container(
        content=ft.Column([
            ft.Text("🎯 AppBar Demo Controls", size=20, weight=ft.FontWeight.BOLD),
            ft.Divider(),
            ft.ElevatedButton(
                "Switch AppBar Style",
                icon=ft.icons.SWAP_HORIZ,
                on_click=switch_appbar,
                width=200,
            ),
            ft.Container(height=20),
            ft.Text("📋 AppBar Components Explained:", size=16, weight=ft.FontWeight.BOLD),
            ft.Column([
                ft.Row([
                    ft.Icon(ft.icons.ARROW_FORWARD, size=16),
                    ft.Text("leading: Icon or widget on the left side"),
                ], spacing=8),
                ft.Row([
                    ft.Icon(ft.icons.ARROW_FORWARD, size=16),
                    ft.Text("title: Main title text or widget"),
                ], spacing=8),
                ft.Row([
                    ft.Icon(ft.icons.ARROW_FORWARD, size=16),
                    ft.Text("actions: List of buttons on the right side"),
                ], spacing=8),
                ft.Row([
                    ft.Icon(ft.icons.ARROW_FORWARD, size=16),
                    ft.Text("bgcolor: Background color"),
                ], spacing=8),
                ft.Row([
                    ft.Icon(ft.icons.ARROW_FORWARD, size=16),
                    ft.Text("color: Text and icon color"),
                ], spacing=8),
                ft.Row([
                    ft.Icon(ft.icons.ARROW_FORWARD, size=16),
                    ft.Text("elevation: Shadow depth"),
                ], spacing=8),
            ], spacing=8),
        ], spacing=10),
        padding=20,
        bgcolor=ft.colors.BLUE_50,
        border_radius=12,
        margin=20,
    )
    
    # Main content
    main_content = ft.Container(
        content=ft.Column([
            ft.Container(height=20),
            content,
            ft.Container(height=40),
            demo_controls,
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        expand=True,
        padding=20,
    )
    
    # Initialize with first AppBar
    update_appbar()
    
    # Add main content
    page.add(main_content)


if __name__ == "__main__":
    ft.app(target=main, port=8091, view=ft.WEB_BROWSER)
