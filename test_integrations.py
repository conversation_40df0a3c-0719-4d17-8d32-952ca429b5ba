#!/usr/bin/env python
"""
Test script for the Integration Manager.
This script can be run from any directory.
"""
import sys
import os
import traceback

# Get the absolute path to the project root directory
project_root = os.path.dirname(os.path.abspath(__file__))

# Add the project root directory to the Python path
sys.path.insert(0, project_root)

try:
    # Import the IntegrationManager
    from src.integrations.integration_manager import IntegrationManager
    
    print("=" * 50)
    print("INTEGRATION MANAGER TEST")
    print("=" * 50)
    print(f"Project root: {project_root}")
    print("-" * 50)
    
    # Create a simple configuration for testing
    config = {
        "email": {
            "enabled": False
        },
        "app": {
            "timezone": "UTC"
        },
        "files": {
            "storage_dir": os.path.join(project_root, "data", "files"),
            "max_file_size": 50,
            "allowed_extensions": "pdf,doc,docx,xls,xlsx,ppt,pptx,txt,csv,jpg,jpeg,png,gif"
        }
    }
    
    # Initialize the integration manager
    print("Initializing IntegrationManager...")
    manager = IntegrationManager(config)
    
    # Get and print the component status
    print("\nGetting component status...")
    status = manager.get_component_status()
    print("Integration Components Status:")
    for component, details in status.items():
        print(f"  {component}:")
        for key, value in details.items():
            if isinstance(value, dict):
                print(f"    {key}:")
                for k, v in value.items():
                    print(f"      {k}: {v}")
            else:
                print(f"    {key}: {value}")
    
    # Shutdown the integration manager
    print("\nShutting down IntegrationManager...")
    manager.shutdown()
    
    print("\nTest completed successfully!")
    print("=" * 50)

except Exception as e:
    print(f"Error during test: {str(e)}")
    traceback.print_exc()