#!/usr/bin/env python
"""
Debug run script for the Science Laboratory Management System.
This script ensures the proper Python path is set up and provides additional debugging.
"""
import sys
import os
import logging
import traceback

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(project_root, 'debug.log'))
    ]
)

logger = logging.getLogger('debug_runner')

try:
    logger.info("Starting application in debug mode")
    logger.info(f"Project root: {project_root}")
    logger.info(f"Python path: {sys.path}")
    
    # Import the necessary modules
    import flet as ft
    from src.utils.config import Config
    
    # Load configuration
    config_path = os.path.join(project_root, 'config', 'app_config.json')
    logger.info(f"Loading configuration from: {config_path}")
    
    if not os.path.exists(config_path):
        logger.error(f"Configuration file not found: {config_path}")
        sys.exit(1)
    
    # Create data directories if they don't exist
    data_dir = os.path.join(project_root, 'data')
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
        logger.info(f"Created data directory: {data_dir}")
    
    logs_dir = os.path.join(project_root, 'logs')
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
        logger.info(f"Created logs directory: {logs_dir}")
    
    # Define a simple app for testing
    def main(page: ft.Page):
        page.title = "Science Laboratory Management System - Debug Mode"
        page.add(ft.Text("Science Laboratory Management System - Debug Mode"))
        page.add(ft.Text(f"Project root: {project_root}"))
        page.add(ft.Text(f"Configuration file: {config_path}"))
        
        # Add a button to test the UI
        def button_clicked(e):
            page.add(ft.Text("Button clicked!"))
            page.update()
        
        page.add(ft.ElevatedButton("Test Button", on_click=button_clicked))
    
    # Run the test app
    logger.info("Running test app")
    ft.app(target=main)
    
except Exception as e:
    logger.error(f"Error: {str(e)}")
    logger.error(traceback.format_exc())