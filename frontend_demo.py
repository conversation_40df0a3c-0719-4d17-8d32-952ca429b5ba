#!/usr/bin/env python
"""
Frontend Demo for Science Laboratory Management System
Showcases all UI components and design elements
"""
import flet as ft
import sys
import os

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.ui.modern_components import ModernCard, StatCard, ModernDataTable, ModernDialog, LoadingOverlay, NotificationBanner


def main(page: ft.Page):
    """Main demo application."""
    page.title = "Lab Management System - Frontend Demo"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window_width = 1400
    page.window_height = 900
    page.padding = 20
    page.scroll = ft.ScrollMode.AUTO
    
    # Demo data
    sample_inventory_data = [
        {"id": 1, "name": "Sodium Chloride", "category": "Chemical", "quantity": "500g", "location": "Cabinet A1", "status": "Available"},
        {"id": 2, "name": "Microscope", "category": "Equipment", "quantity": "1", "location": "Lab Room 1", "status": "In Use"},
        {"id": 3, "name": "Hydrochloric Acid", "category": "Chemical", "quantity": "250ml", "location": "Fume Hood", "status": "Low Stock"},
        {"id": 4, "name": "Centrifuge", "category": "Equipment", "quantity": "1", "location": "Lab Room 2", "status": "Available"},
        {"id": 5, "name": "Ethanol", "category": "Chemical", "quantity": "1L", "location": "Cabinet B2", "status": "Available"},
    ]
    
    def show_notification(message, type_="info"):
        """Show a notification banner."""
        notification = NotificationBanner(
            message=message,
            notification_type=type_,
            dismissible=True
        )
        page.overlay.append(notification)
        page.update()
    
    def show_dialog():
        """Show a modern dialog."""
        dialog_content = ft.Column([
            ft.Text("This is a modern dialog component with rounded corners and proper styling."),
            ft.TextField(label="Sample Input", width=300),
            ft.Dropdown(
                label="Select Option",
                width=300,
                options=[
                    ft.dropdown.Option("Option 1"),
                    ft.dropdown.Option("Option 2"),
                    ft.dropdown.Option("Option 3"),
                ]
            )
        ])
        
        dialog = ModernDialog(
            title="Modern Dialog Demo",
            content=dialog_content,
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: close_dialog()),
                ft.ElevatedButton("Save", on_click=lambda e: close_dialog())
            ]
        )
        
        def close_dialog():
            page.dialog = None
            page.update()
        
        page.dialog = dialog
        dialog.open = True
        page.update()
    
    # Create header
    header = ft.Container(
        content=ft.Row([
            ft.Icon(ft.icons.SCIENCE, size=40, color=ft.colors.BLUE),
            ft.Column([
                ft.Text("Science Laboratory Management System", 
                       size=28, weight=ft.FontWeight.BOLD, color=ft.colors.BLUE),
                ft.Text("Frontend Components Demo", 
                       size=16, color=ft.colors.GREY_600)
            ], spacing=0),
            ft.Container(expand=True),
            ft.ElevatedButton(
                "Show Notification",
                icon=ft.icons.NOTIFICATIONS,
                on_click=lambda e: show_notification("This is a demo notification!", "success")
            ),
            ft.ElevatedButton(
                "Show Dialog",
                icon=ft.icons.OPEN_IN_NEW,
                on_click=lambda e: show_dialog()
            )
        ], alignment=ft.MainAxisAlignment.START),
        padding=20,
        bgcolor=ft.colors.WHITE,
        border_radius=12,
        shadow=ft.BoxShadow(
            spread_radius=1,
            blur_radius=4,
            color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
            offset=ft.Offset(0, 2)
        )
    )
    
    # Create statistics cards
    stats_row = ft.Row([
        StatCard(
            title="Total Inventory Items",
            value="1,247",
            subtitle="Active items in system",
            icon=ft.icons.INVENTORY,
            color=ft.colors.BLUE,
            trend="+12%",
            trend_positive=True,
            width=280
        ),
        StatCard(
            title="Active Experiments",
            value="23",
            subtitle="Currently running",
            icon=ft.icons.SCIENCE,
            color=ft.colors.GREEN,
            trend="+5",
            trend_positive=True,
            width=280
        ),
        StatCard(
            title="Low Stock Items",
            value="8",
            subtitle="Need restocking",
            icon=ft.icons.WARNING,
            color=ft.colors.ORANGE,
            trend="-3",
            trend_positive=False,
            width=280
        ),
        StatCard(
            title="Users Online",
            value="15",
            subtitle="Active sessions",
            icon=ft.icons.PEOPLE,
            color=ft.colors.PURPLE,
            width=280
        )
    ], spacing=20, scroll=ft.ScrollMode.AUTO)
    
    # Create modern data table
    table_columns = [
        {"key": "id", "label": "ID"},
        {"key": "name", "label": "Name"},
        {"key": "category", "label": "Category"},
        {"key": "quantity", "label": "Quantity"},
        {"key": "location", "label": "Location"},
        {"key": "status", "label": "Status"}
    ]
    
    data_table = ModernDataTable(
        columns=table_columns,
        data=sample_inventory_data,
        searchable=True,
        sortable=True,
        paginated=True,
        page_size=3,
        actions=[
            {
                "label": "Add Item",
                "icon": ft.icons.ADD,
                "on_click": lambda e: show_notification("Add Item clicked!", "info")
            },
            {
                "label": "Export",
                "icon": ft.icons.DOWNLOAD,
                "on_click": lambda e: show_notification("Export started!", "success")
            }
        ]
    )
    
    # Create feature showcase cards
    feature_cards = ft.Row([
        ModernCard(
            title="Authentication System",
            subtitle="Secure login with role-based access",
            content=ft.Column([
                ft.Text("✅ JWT Token Authentication"),
                ft.Text("✅ Role-based Permissions"),
                ft.Text("✅ Session Management"),
                ft.Text("✅ Password Security"),
            ], spacing=8),
            actions=[
                ft.TextButton("Learn More", icon=ft.icons.ARROW_FORWARD)
            ],
            width=300
        ),
        ModernCard(
            title="Inventory Management",
            subtitle="Complete tracking solution",
            content=ft.Column([
                ft.Text("✅ Barcode/QR Code Support"),
                ft.Text("✅ Expiry Date Tracking"),
                ft.Text("✅ Low Stock Alerts"),
                ft.Text("✅ Location Management"),
            ], spacing=8),
            actions=[
                ft.TextButton("View Inventory", icon=ft.icons.INVENTORY)
            ],
            width=300
        ),
        ModernCard(
            title="Experiment Tracking",
            subtitle="Research project management",
            content=ft.Column([
                ft.Text("✅ Project Lifecycle"),
                ft.Text("✅ Collaboration Tools"),
                ft.Text("✅ Data Collection"),
                ft.Text("✅ Progress Tracking"),
            ], spacing=8),
            actions=[
                ft.TextButton("Start Experiment", icon=ft.icons.SCIENCE)
            ],
            width=300
        ),
        ModernCard(
            title="Reports & Analytics",
            subtitle="Data-driven insights",
            content=ft.Column([
                ft.Text("✅ Usage Statistics"),
                ft.Text("✅ Custom Reports"),
                ft.Text("✅ Export Options"),
                ft.Text("✅ Visual Charts"),
            ], spacing=8),
            actions=[
                ft.TextButton("Generate Report", icon=ft.icons.ANALYTICS)
            ],
            width=300
        )
    ], spacing=20, scroll=ft.ScrollMode.AUTO)
    
    # Create UI components showcase
    ui_showcase = ModernCard(
        title="Modern UI Components",
        subtitle="Enhanced user interface elements",
        content=ft.Column([
            ft.Text("Component Library:", weight=ft.FontWeight.BOLD),
            ft.Row([
                ft.Chip(label=ft.Text("ModernCard"), bgcolor=ft.colors.BLUE_100),
                ft.Chip(label=ft.Text("StatCard"), bgcolor=ft.colors.GREEN_100),
                ft.Chip(label=ft.Text("ModernDataTable"), bgcolor=ft.colors.ORANGE_100),
                ft.Chip(label=ft.Text("ModernDialog"), bgcolor=ft.colors.PURPLE_100),
            ], wrap=True, spacing=8),
            ft.Divider(),
            ft.Text("Features:", weight=ft.FontWeight.BOLD),
            ft.Column([
                ft.Text("• Hover effects and animations"),
                ft.Text("• Responsive design for all screen sizes"),
                ft.Text("• Search and pagination in tables"),
                ft.Text("• Notification system with auto-dismiss"),
                ft.Text("• Loading overlays and progress indicators"),
                ft.Text("• Professional color scheme and typography"),
            ], spacing=4)
        ], spacing=12)
    )
    
    # Create main layout
    main_content = ft.Column([
        header,
        ft.Container(height=20),
        ft.Text("📊 Dashboard Statistics", size=20, weight=ft.FontWeight.BOLD),
        stats_row,
        ft.Container(height=20),
        ft.Text("📋 Data Table Component", size=20, weight=ft.FontWeight.BOLD),
        data_table,
        ft.Container(height=20),
        ft.Text("🎯 Feature Overview", size=20, weight=ft.FontWeight.BOLD),
        feature_cards,
        ft.Container(height=20),
        ui_showcase,
        ft.Container(height=40),
    ], spacing=0, scroll=ft.ScrollMode.AUTO)
    
    page.add(main_content)


if __name__ == "__main__":
    ft.app(target=main, port=8090, view=ft.WEB_BROWSER)
