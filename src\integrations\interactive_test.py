# %% [Interactive Test]
# Run this in the Python Interactive Window
import sys
import os

# Add the project root directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import the IntegrationManager
from src.integrations.integration_manager import IntegrationManager

# Create a simple configuration
config = {
    "email": {
        "enabled": False
    }
}

# Initialize the manager
manager = IntegrationManager(config)

# Get the status
status = manager.get_component_status()
print(status)

# %% [Another cell]
# You can run this cell separately
print("Testing complete!")