import flet as ft

class LoginView:
    """
    Login view for the Science Laboratory Management System.
    """
    
    def __init__(self, controller):
        """
        Initialize the login view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        self.visible = True
        self.current_user = None
        self.requires_2fa = False
        
        # Create login form controls
        self.username_field = ft.TextField(
            label="Username",
            autofocus=True,
            width=300,
        )
        
        self.password_field = ft.TextField(
            label="Password",
            password=True,
            can_reveal_password=True,
            width=300,
        )
        
        self.error_text = ft.Text(
            color=ft.colors.RED_500,
            size=12,
            visible=False,
        )
        
        self.login_button = ft.ElevatedButton(
            text="Login",
            width=300,
            on_click=self.login_clicked,
        )
        
        # Create registration and password reset links
        self.register_link = ft.TextButton(
            text="Create an account",
            on_click=self.register_clicked,
        )
        
        self.forgot_password_link = ft.TextButton(
            text="Forgot password?",
            on_click=self.forgot_password_clicked,
        )
        
        # Create 2FA verification controls
        self.verification_code_field = ft.TextField(
            label="Verification Code",
            autofocus=True,
            width=300,
            hint_text="Enter the 6-digit code from your authenticator app",
        )
        
        self.verify_button = ft.ElevatedButton(
            text="Verify",
            width=300,
            on_click=self.verify_clicked,
        )
        
        self.back_button = ft.TextButton(
            text="Back to Login",
            on_click=self.back_to_login_clicked,
        )
    
    def build(self):
        """
        Build and return the login view.
        
        Returns:
            ft.Container: The login view container
        """
        if self.requires_2fa:
            # Show 2FA verification form
            return ft.Container(
                content=ft.Column(
                    [
                        ft.Container(height=50),
                        ft.Text("Two-Factor Authentication", size=28, weight=ft.FontWeight.BOLD),
                        ft.Container(height=10),
                        ft.Text("Enter the verification code from your authenticator app", size=16),
                        ft.Container(height=30),
                        self.verification_code_field,
                        self.error_text,
                        ft.Container(height=10),
                        self.verify_button,
                        self.back_button,
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                ),
                alignment=ft.alignment.center,
                expand=True,
                visible=self.visible,
            )
        else:
            # Show regular login form
            return ft.Container(
                content=ft.Column(
                    [
                        ft.Container(height=50),
                        ft.Text("Science Laboratory Management System", size=28, weight=ft.FontWeight.BOLD),
                        ft.Container(height=10),
                        ft.Text("Login to your account", size=16),
                        ft.Container(height=30),
                        self.username_field,
                        self.password_field,
                        self.error_text,
                        ft.Container(height=10),
                        self.login_button,
                        ft.Row(
                            [self.register_link, self.forgot_password_link],
                            alignment=ft.MainAxisAlignment.CENTER,
                        ),
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                ),
                alignment=ft.alignment.center,
                expand=True,
                visible=self.visible,
            )
    
    def login_clicked(self, e):
        """
        Handle login button click.
        
        Args:
            e: The click event
        """
        username = self.username_field.value
        password = self.password_field.value
        
        if not username or not password:
            self.error_text.value = "Please enter both username and password."
            self.error_text.visible = True
            self.controller.page.update()
            return
        
        # Get client info
        ip_address = "127.0.0.1"  # In a real app, get the actual IP
        user_agent = "Web Client"  # In a real app, get the actual user agent
        
        # Attempt to login
        user, error, requires_2fa = self.controller.authenticate(username, password, ip_address, user_agent)
        
        if error:
            self.error_text.value = error
            self.error_text.visible = True
            self.controller.page.update()
            return
        
        if requires_2fa:
            # Store the user for 2FA verification
            self.current_user = user
            self.requires_2fa = True
            
            # Clear the verification code field
            self.verification_code_field.value = ""
            self.error_text.visible = False
            
            # Update the view to show 2FA form
            self.controller.page.clean()
            self.controller.page.add(self.build())
            self.controller.page.update()
            return
        
        # Complete login
        self.controller.complete_login(user)
        
        # Reset form
        self.username_field.value = ""
        self.password_field.value = ""
        self.error_text.visible = False
    
    def verify_clicked(self, e):
        """
        Handle 2FA verification button click.
        
        Args:
            e: The click event
        """
        if not self.current_user:
            self.back_to_login_clicked(None)
            return
        
        verification_code = self.verification_code_field.value
        
        if not verification_code:
            self.error_text.value = "Please enter the verification code."
            self.error_text.visible = True
            self.controller.page.update()
            return
        
        # Get client info
        ip_address = "127.0.0.1"  # In a real app, get the actual IP
        user_agent = "Web Client"  # In a real app, get the actual user agent
        
        # Verify the code
        user, error = self.controller.verify_2fa(self.current_user['id'], verification_code, ip_address, user_agent)
        
        if error:
            self.error_text.value = error
            self.error_text.visible = True
            self.controller.page.update()
            return
        
        # Complete login
        self.controller.complete_login(user)
        
        # Reset form and state
        self.verification_code_field.value = ""
        self.error_text.visible = False
        self.current_user = None
        self.requires_2fa = False
    
    def back_to_login_clicked(self, e):
        """
        Handle back to login button click.
        
        Args:
            e: The click event
        """
        self.current_user = None
        self.requires_2fa = False
        self.error_text.visible = False
        
        # Update the view to show login form
        self.controller.page.clean()
        self.controller.page.add(self.build())
        self.controller.page.update()
    
    def register_clicked(self, e):
        """
        Handle register link click.
        
        Args:
            e: The click event
        """
        self.controller.show_registration_view()
    
    def forgot_password_clicked(self, e):
        """
        Handle forgot password link click.
        
        Args:
            e: The click event
        """
        self.controller.show_password_reset_view()