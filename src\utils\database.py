import sqlite3
import os
import json
from datetime import datetime
import hashlib

class Database:
    """
    Database utility class for the Science Laboratory Management System.
    Handles database connections, initialization, and common operations.
    """
    
    def __init__(self, db_path=None):
        """
        Initialize the database connection.
        
        Args:
            db_path (str, optional): Path to the database file. Defaults to data/lab_db.db.
        """
        if db_path is None:
            # Use default path relative to project root
            current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            self.db_path = os.path.join(current_dir, "data", "lab_db.db")
        else:
            self.db_path = db_path
            
        # Ensure data directory exists
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # Initialize database if it doesn't exist
        self.initialize_database()
    
    def get_connection(self):
        """
        Get a database connection.
        
        Returns:
            sqlite3.Connection: A connection to the SQLite database
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Return rows as dictionaries
        return conn
    
    def initialize_database(self):
        """Initialize the database with required tables if they don't exist."""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Create users table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            full_name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            role TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            failed_login_attempts INTEGER DEFAULT 0,
            lockout_until TIMESTAMP,
            totp_secret TEXT,
            totp_enabled BOOLEAN DEFAULT 0,
            password_reset_token TEXT,
            password_reset_expiry TIMESTAMP,
            require_password_change BOOLEAN DEFAULT 0
        )
        ''')
        
        # Create sessions table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            token TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NOT NULL,
            ip_address TEXT,
            user_agent TEXT,
            is_active BOOLEAN DEFAULT 1,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')
        
        # Create permissions table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS permissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE NOT NULL,
            description TEXT
        )
        ''')
        
        # Create role_permissions table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS role_permissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            role TEXT NOT NULL,
            permission_id INTEGER NOT NULL,
            FOREIGN KEY (permission_id) REFERENCES permissions (id)
        )
        ''')
        
        # Create inventory_items table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS inventory_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            category TEXT NOT NULL,
            quantity REAL NOT NULL,
            unit TEXT NOT NULL,
            location TEXT,
            min_quantity REAL,
            expiry_date DATE,
            barcode TEXT UNIQUE,
            added_by INTEGER,
            added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_updated TIMESTAMP,
            notes TEXT,
            FOREIGN KEY (added_by) REFERENCES users (id)
        )
        ''')
        
        # Create experiments table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS experiments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            status TEXT NOT NULL,
            created_by INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_updated TIMESTAMP,
            data JSON,
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
        ''')
        
        # Create experiment_collaborators table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS experiment_collaborators (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            experiment_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            added_by INTEGER NOT NULL,
            added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (experiment_id) REFERENCES experiments (id),
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (added_by) REFERENCES users (id)
        )
        ''')
        
        # Create experiment_measurements table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS experiment_measurements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            experiment_id INTEGER NOT NULL,
            name TEXT NOT NULL,
            value REAL NOT NULL,
            unit TEXT,
            timestamp TIMESTAMP NOT NULL,
            notes TEXT,
            created_by INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (experiment_id) REFERENCES experiments (id),
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
        ''')
        
        # Create experiment_files table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS experiment_files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            experiment_id INTEGER NOT NULL,
            filename TEXT NOT NULL,
            file_path TEXT NOT NULL,
            file_type TEXT,
            file_size INTEGER,
            description TEXT,
            uploaded_by INTEGER NOT NULL,
            uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (experiment_id) REFERENCES experiments (id),
            FOREIGN KEY (uploaded_by) REFERENCES users (id)
        )
        ''')
        
        # Create experiment_comments table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS experiment_comments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            experiment_id INTEGER NOT NULL,
            comment_text TEXT NOT NULL,
            created_by INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (experiment_id) REFERENCES experiments (id),
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
        ''')
        
        # Create lab_schedules table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS lab_schedules (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            lab_room TEXT NOT NULL,
            start_time TIMESTAMP NOT NULL,
            end_time TIMESTAMP NOT NULL,
            created_by INTEGER NOT NULL,
            status TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
        ''')
        
        # Create safety_incidents table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS safety_incidents (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT NOT NULL,
            severity TEXT NOT NULL,
            reported_by INTEGER NOT NULL,
            reported_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status TEXT NOT NULL,
            resolution TEXT,
            resolved_at TIMESTAMP,
            FOREIGN KEY (reported_by) REFERENCES users (id)
        )
        ''')
        
        # Create safety_checklists table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS safety_checklists (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            items JSON NOT NULL,
            created_by INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
        ''')
        
        # Create inventory_transactions table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS inventory_transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_id INTEGER NOT NULL,
            transaction_type TEXT NOT NULL,
            quantity REAL NOT NULL,
            previous_quantity REAL NOT NULL,
            new_quantity REAL NOT NULL,
            unit TEXT NOT NULL,
            reason TEXT,
            reference_id INTEGER,
            reference_type TEXT,
            location TEXT,
            destination_location TEXT,
            user_id INTEGER NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,
            FOREIGN KEY (item_id) REFERENCES inventory_items (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')
        
        # Create audit_logs table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS audit_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            action TEXT NOT NULL,
            entity_type TEXT NOT NULL,
            entity_id INTEGER,
            details TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')
        
        # Create password_reset_tokens table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS password_reset_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            token TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NOT NULL,
            used BOOLEAN DEFAULT 0,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')
        
        # Insert default admin user if no users exist
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        
        if user_count == 0:
            # Create default admin user with password "admin123"
            password_hash = hashlib.sha256("admin123".encode()).hexdigest()
            cursor.execute('''
            INSERT INTO users (username, password_hash, full_name, email, role)
            VALUES (?, ?, ?, ?, ?)
            ''', ("admin", password_hash, "System Administrator", "<EMAIL>", "administrator"))
            
            # Create default technician user
            password_hash = hashlib.sha256("tech123".encode()).hexdigest()
            cursor.execute('''
            INSERT INTO users (username, password_hash, full_name, email, role)
            VALUES (?, ?, ?, ?, ?)
            ''', ("technician", password_hash, "Lab Technician", "<EMAIL>", "technician"))
            
            # Create default researcher user
            password_hash = hashlib.sha256("research123".encode()).hexdigest()
            cursor.execute('''
            INSERT INTO users (username, password_hash, full_name, email, role)
            VALUES (?, ?, ?, ?, ?)
            ''', ("researcher", password_hash, "Lab Researcher", "<EMAIL>", "researcher"))
            
            # Add some sample inventory items
            sample_items = [
                ("Hydrochloric Acid", "Chemical", 5.0, "L", "Cabinet A1", 1.0, "2025-12-31", "HCL001", 1),
                ("Microscope", "Equipment", 10, "units", "Room 101", 2, None, "MICRO001", 1),
                ("Test Tubes", "Glassware", 100, "units", "Cabinet B2", 20, None, "TUBE001", 1),
                ("Sodium Hydroxide", "Chemical", 2.5, "kg", "Cabinet A2", 0.5, "2025-10-15", "NAOH001", 1),
                ("Bunsen Burner", "Equipment", 15, "units", "Room 102", 5, None, "BURN001", 1)
            ]
            
            for item in sample_items:
                cursor.execute('''
                INSERT INTO inventory_items (name, category, quantity, unit, location, min_quantity, expiry_date, barcode, added_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', item)
        
        conn.commit()
        conn.close()
    
    def execute_query(self, query, params=None):
        """
        Execute a database query.
        
        Args:
            query (str): The SQL query to execute
            params (tuple, optional): Parameters for the query. Defaults to None.
            
        Returns:
            list: List of rows returned by the query
        """
        conn = self.get_connection()
        cursor = conn.cursor()
        
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
            
        result = cursor.fetchall()
        conn.commit()
        conn.close()
        
        return [dict(row) for row in result]
    
    def execute_insert(self, query, params=None):
        """
        Execute an insert query and return the ID of the inserted row.
        
        Args:
            query (str): The SQL query to execute
            params (tuple, optional): Parameters for the query. Defaults to None.
            
        Returns:
            int: ID of the inserted row
        """
        conn = self.get_connection()
        cursor = conn.cursor()
        
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
            
        last_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return last_id
    
    def log_audit(self, user_id, action, entity_type, entity_id=None, details=None):
        """
        Log an audit entry.
        
        Args:
            user_id (int): ID of the user performing the action
            action (str): Description of the action
            entity_type (str): Type of entity being acted upon
            entity_id (int, optional): ID of the entity. Defaults to None.
            details (str, optional): Additional details. Defaults to None.
        """
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
        INSERT INTO audit_logs (user_id, action, entity_type, entity_id, details)
        VALUES (?, ?, ?, ?, ?)
        ''', (user_id, action, entity_type, entity_id, details))
        
        conn.commit()
        conn.close()