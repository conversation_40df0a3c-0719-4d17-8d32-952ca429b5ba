import bcrypt
import secrets
import string
import logging
from datetime import datetime, timedelta

class AuthManager:
    """
    Authentication manager for the Science Laboratory Management System.
    Handles user authentication, password management, and session control.
    """
    
    def __init__(self, controller):
        """
        Initialize the authentication manager.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        self.logger = logging.getLogger("auth_manager")
        self.redirect_route = None
        self.redirect_params = None
    
    def authenticate(self, username, password):
        """
        Authenticate a user with username and password.
        
        Args:
            username (str): The username
            password (str): The password
            
        Returns:
            dict: User data if authentication successful, None otherwise
        """
        try:
            # Query user by username
            user = self.controller.db.execute_query(
                "SELECT * FROM users WHERE username = ? AND status = 'active'",
                (username,)
            )
            
            if not user or len(user) == 0:
                self.logger.warning(f"Authentication failed: User not found or inactive - {username}")
                return None
            
            user = user[0]
            
            # Verify password
            if self.verify_password(password, user["password_hash"]):
                self.logger.info(f"Authentication successful: {username}")
                return user
            else:
                self.logger.warning(f"Authentication failed: Invalid password - {username}")
                return None
        
        except Exception as e:
            self.logger.error(f"Authentication error: {str(e)}")
            return None
    
    def hash_password(self, password):
        """
        Hash a password using bcrypt.
        
        Args:
            password (str): The password to hash
            
        Returns:
            str: The password hash
        """
        # Generate salt and hash password
        salt = bcrypt.gensalt()
        password_hash = bcrypt.hashpw(password.encode('utf-8'), salt)
        
        return password_hash.decode('utf-8')
    
    def verify_password(self, password, password_hash):
        """
        Verify a password against a hash.
        
        Args:
            password (str): The password to verify
            password_hash (str): The password hash
            
        Returns:
            bool: True if password matches hash, False otherwise
        """
        try:
            # Verify password
            return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
        
        except Exception as e:
            self.logger.error(f"Password verification error: {str(e)}")
            return False
    
    def change_password(self, user_id, current_password, new_password):
        """
        Change a user's password.
        
        Args:
            user_id (int): The user ID
            current_password (str): The current password
            new_password (str): The new password
            
        Returns:
            bool: True if password changed successfully, False otherwise
        """
        try:
            # Get user
            user = self.controller.db.execute_query(
                "SELECT * FROM users WHERE id = ?",
                (user_id,)
            )
            
            if not user or len(user) == 0:
                self.logger.warning(f"Password change failed: User not found - ID {user_id}")
                return False
            
            user = user[0]
            
            # Verify current password
            if not self.verify_password(current_password, user["password_hash"]):
                self.logger.warning(f"Password change failed: Invalid current password - {user['username']}")
                return False
            
            # Hash new password
            new_password_hash = self.hash_password(new_password)
            
            # Update password
            self.controller.db.execute_query(
                "UPDATE users SET password_hash = ? WHERE id = ?",
                (new_password_hash, user_id)
            )
            
            # Log password change
            self.controller.db.log_audit(
                user_id,
                "update",
                "user",
                user_id,
                "Password changed"
            )
            
            self.logger.info(f"Password changed successfully: {user['username']}")
            
            return True
        
        except Exception as e:
            self.logger.error(f"Password change error: {str(e)}")
            return False
    
    def reset_password(self, email):
        """
        Generate a password reset token for a user.
        
        Args:
            email (str): The user's email address
            
        Returns:
            str: The reset token if successful, None otherwise
        """
        try:
            # Get user by email
            user = self.controller.db.execute_query(
                "SELECT * FROM users WHERE email = ? AND status = 'active'",
                (email,)
            )
            
            if not user or len(user) == 0:
                self.logger.warning(f"Password reset failed: User not found or inactive - {email}")
                return None
            
            user = user[0]
            
            # Generate token
            token = self.generate_token()
            
            # Set expiration (24 hours from now)
            expires_at = (datetime.now() + timedelta(hours=24)).isoformat()
            
            # Store token in database
            self.controller.db.execute_query(
                """
                INSERT INTO password_reset_tokens (
                    user_id, token, expires_at
                ) VALUES (?, ?, ?)
                """,
                (user["id"], token, expires_at)
            )
            
            # Log password reset request
            self.controller.db.log_audit(
                user["id"],
                "create",
                "password_reset",
                None,
                "Password reset requested"
            )
            
            self.logger.info(f"Password reset token generated: {user['username']}")
            
            return token
        
        except Exception as e:
            self.logger.error(f"Password reset error: {str(e)}")
            return None
    
    def verify_reset_token(self, token):
        """
        Verify a password reset token.
        
        Args:
            token (str): The reset token
            
        Returns:
            dict: User data if token is valid, None otherwise
        """
        try:
            # Get token from database
            token_data = self.controller.db.execute_query(
                """
                SELECT t.*, u.* 
                FROM password_reset_tokens t
                JOIN users u ON t.user_id = u.id
                WHERE t.token = ? AND t.used = 0
                """,
                (token,)
            )
            
            if not token_data or len(token_data) == 0:
                self.logger.warning(f"Token verification failed: Token not found or already used - {token}")
                return None
            
            token_data = token_data[0]
            
            # Check if token is expired
            expires_at = datetime.fromisoformat(token_data["expires_at"].replace('Z', '+00:00'))
            if datetime.now() > expires_at:
                self.logger.warning(f"Token verification failed: Token expired - {token}")
                return None
            
            return token_data
        
        except Exception as e:
            self.logger.error(f"Token verification error: {str(e)}")
            return None
    
    def complete_password_reset(self, token, new_password):
        """
        Complete a password reset using a token.
        
        Args:
            token (str): The reset token
            new_password (str): The new password
            
        Returns:
            bool: True if password reset successful, False otherwise
        """
        try:
            # Verify token
            token_data = self.verify_reset_token(token)
            
            if not token_data:
                return False
            
            # Hash new password
            new_password_hash = self.hash_password(new_password)
            
            # Update password
            self.controller.db.execute_query(
                "UPDATE users SET password_hash = ? WHERE id = ?",
                (new_password_hash, token_data["user_id"])
            )
            
            # Mark token as used
            self.controller.db.execute_query(
                "UPDATE password_reset_tokens SET used = 1 WHERE token = ?",
                (token,)
            )
            
            # Log password reset
            self.controller.db.log_audit(
                token_data["user_id"],
                "update",
                "user",
                token_data["user_id"],
                "Password reset completed"
            )
            
            self.logger.info(f"Password reset completed: {token_data['username']}")
            
            return True
        
        except Exception as e:
            self.logger.error(f"Password reset completion error: {str(e)}")
            return False
    
    def register_user(self, username, password, full_name, email, department, role="student"):
        """
        Register a new user.
        
        Args:
            username (str): The username
            password (str): The password
            full_name (str): The user's full name
            email (str): The user's email address
            department (str): The user's department
            role (str, optional): The user's role (default: student)
            
        Returns:
            dict: User data if registration successful, None otherwise
        """
        try:
            # Check if username already exists
            existing_user = self.controller.db.execute_query(
                "SELECT id FROM users WHERE username = ?",
                (username,)
            )
            
            if existing_user and len(existing_user) > 0:
                self.logger.warning(f"Registration failed: Username already exists - {username}")
                return None
            
            # Check if email already exists
            existing_email = self.controller.db.execute_query(
                "SELECT id FROM users WHERE email = ?",
                (email,)
            )
            
            if existing_email and len(existing_email) > 0:
                self.logger.warning(f"Registration failed: Email already exists - {email}")
                return None
            
            # Hash password
            password_hash = self.hash_password(password)
            
            # Insert new user
            self.controller.db.execute_query(
                """
                INSERT INTO users (
                    username, password_hash, full_name, email, 
                    department, role, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """,
                (
                    username,
                    password_hash,
                    full_name,
                    email,
                    department,
                    role,
                    "active",
                    datetime.now().isoformat()
                )
            )
            
            # Get the new user
            new_user = self.controller.db.execute_query(
                "SELECT * FROM users WHERE username = ?",
                (username,)
            )
            
            if new_user and len(new_user) > 0:
                new_user = new_user[0]
                
                # Log user registration
                self.controller.db.log_audit(
                    new_user["id"],
                    "create",
                    "user",
                    new_user["id"],
                    "User registered"
                )
                
                self.logger.info(f"User registered successfully: {username}")
                
                return new_user
            else:
                return None
        
        except Exception as e:
            self.logger.error(f"Registration error: {str(e)}")
            return None
    
    def generate_token(self, length=32):
        """
        Generate a random token.
        
        Args:
            length (int, optional): The token length (default: 32)
            
        Returns:
            str: The generated token
        """
        # Define character set
        alphabet = string.ascii_letters + string.digits
        
        # Generate token
        token = ''.join(secrets.choice(alphabet) for _ in range(length))
        
        return token
    
    def set_redirect_route(self, route, params=None):
        """
        Set the route to redirect to after login.
        
        Args:
            route (str): The route name
            params (dict, optional): Parameters for the route
        """
        self.redirect_route = route
        self.redirect_params = params
    
    def get_redirect_route(self):
        """
        Get the route to redirect to after login.
        
        Returns:
            tuple: (route, params) or None if no redirect
        """
        if self.redirect_route:
            route = self.redirect_route
            params = self.redirect_params
            
            # Clear redirect
            self.redirect_route = None
            self.redirect_params = None
            
            return (route, params)
        else:
            return None
    
    def validate_password_strength(self, password):
        """
        Validate password strength.
        
        Args:
            password (str): The password to validate
            
        Returns:
            tuple: (valid, message) where valid is a boolean and message is a string
        """
        # Check length
        if len(password) < 8:
            return (False, "Password must be at least 8 characters long")
        
        # Check for uppercase letter
        if not any(c.isupper() for c in password):
            return (False, "Password must contain at least one uppercase letter")
        
        # Check for lowercase letter
        if not any(c.islower() for c in password):
            return (False, "Password must contain at least one lowercase letter")
        
        # Check for digit
        if not any(c.isdigit() for c in password):
            return (False, "Password must contain at least one digit")
        
        # Check for special character
        special_chars = "!@#$%^&*()-_=+[]{}|;:,.<>?/"
        if not any(c in special_chars for c in password):
            return (False, "Password must contain at least one special character")
        
        return (True, "Password meets strength requirements")