"""
Favorites Manager for Science Laboratory Management System
Allows users to pin frequently used sections and actions
"""
import flet as ft
import json
import os
from typing import List, Dict, Any, Callable
from datetime import datetime


class FavoriteItem:
    """Represents a favorite item."""
    
    def __init__(self, id: str, title: str, subtitle: str, icon: str, route: str, data: Dict[str, Any] = None):
        self.id = id
        self.title = title
        self.subtitle = subtitle
        self.icon = icon
        self.route = route
        self.data = data or {}
        self.added_at = datetime.now().isoformat()
        self.access_count = 0
        self.last_accessed = None


class FavoritesManager:
    """
    Manages user favorites and pinned items.
    """
    
    def __init__(self, user_id: str = "default", on_navigate: Callable[[str, Dict], None] = None):
        """
        Initialize favorites manager.
        
        Args:
            user_id: User identifier for personalized favorites
            on_navigate: Callback function for navigation
        """
        self.user_id = user_id
        self.on_navigate = on_navigate
        self.favorites_file = f"data/favorites_{user_id}.json"
        self.favorites = self._load_favorites()
        self.available_items = self._get_available_items()
        
        # Ensure data directory exists
        os.makedirs(os.path.dirname(self.favorites_file), exist_ok=True)
    
    def _get_available_items(self) -> List[FavoriteItem]:
        """Get all available items that can be favorited."""
        return [
            # Main navigation
            FavoriteItem("dashboard", "Dashboard", "System overview", "dashboard", "dashboard"),
            FavoriteItem("inventory", "Inventory", "Manage items", "inventory", "inventory"),
            FavoriteItem("experiments", "Experiments", "Track research", "science", "experiments"),
            FavoriteItem("scheduling", "Scheduling", "Book resources", "calendar_month", "scheduling"),
            FavoriteItem("reports", "Reports", "Analytics", "analytics", "reports"),
            FavoriteItem("users", "Users", "User management", "people", "users"),
            
            # Quick actions
            FavoriteItem("add_inventory", "Add Inventory", "Create new item", "add_box", "inventory", {"action": "add"}),
            FavoriteItem("new_experiment", "New Experiment", "Start research", "science", "experiments", {"action": "new"}),
            FavoriteItem("book_equipment", "Book Equipment", "Schedule usage", "event", "scheduling", {"action": "book"}),
            FavoriteItem("generate_report", "Generate Report", "Create analytics", "assessment", "reports", {"action": "generate"}),
            
            # Filters and views
            FavoriteItem("low_stock", "Low Stock Items", "Items running low", "warning", "inventory", {"filter": "low_stock"}),
            FavoriteItem("active_experiments", "Active Experiments", "Ongoing research", "play_circle", "experiments", {"filter": "active"}),
            FavoriteItem("today_schedule", "Today's Schedule", "Today's bookings", "today", "scheduling", {"filter": "today"}),
            FavoriteItem("recent_reports", "Recent Reports", "Latest analytics", "history", "reports", {"filter": "recent"}),
            
            # Settings and tools
            FavoriteItem("user_profile", "My Profile", "User settings", "account_circle", "profile"),
            FavoriteItem("system_settings", "Settings", "System config", "settings", "settings"),
            FavoriteItem("help_center", "Help Center", "Documentation", "help", "help"),
            FavoriteItem("notifications", "Notifications", "System alerts", "notifications", "notifications"),
        ]
    
    def _load_favorites(self) -> List[FavoriteItem]:
        """Load favorites from file."""
        try:
            if os.path.exists(self.favorites_file):
                with open(self.favorites_file, 'r') as f:
                    data = json.load(f)
                    favorites = []
                    for item_data in data:
                        favorite = FavoriteItem(
                            item_data['id'],
                            item_data['title'],
                            item_data['subtitle'],
                            item_data['icon'],
                            item_data['route'],
                            item_data.get('data', {})
                        )
                        favorite.added_at = item_data.get('added_at', datetime.now().isoformat())
                        favorite.access_count = item_data.get('access_count', 0)
                        favorite.last_accessed = item_data.get('last_accessed')
                        favorites.append(favorite)
                    return favorites
        except Exception as e:
            print(f"Error loading favorites: {e}")
        
        return []
    
    def _save_favorites(self):
        """Save favorites to file."""
        try:
            data = []
            for favorite in self.favorites:
                data.append({
                    'id': favorite.id,
                    'title': favorite.title,
                    'subtitle': favorite.subtitle,
                    'icon': favorite.icon,
                    'route': favorite.route,
                    'data': favorite.data,
                    'added_at': favorite.added_at,
                    'access_count': favorite.access_count,
                    'last_accessed': favorite.last_accessed,
                })
            
            with open(self.favorites_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Error saving favorites: {e}")
    
    def add_favorite(self, item_id: str) -> bool:
        """Add item to favorites."""
        # Check if already favorited
        if any(fav.id == item_id for fav in self.favorites):
            return False
        
        # Find item in available items
        item = next((item for item in self.available_items if item.id == item_id), None)
        if not item:
            return False
        
        # Add to favorites
        self.favorites.append(item)
        self._save_favorites()
        return True
    
    def remove_favorite(self, item_id: str) -> bool:
        """Remove item from favorites."""
        original_count = len(self.favorites)
        self.favorites = [fav for fav in self.favorites if fav.id != item_id]
        
        if len(self.favorites) < original_count:
            self._save_favorites()
            return True
        return False
    
    def is_favorite(self, item_id: str) -> bool:
        """Check if item is favorited."""
        return any(fav.id == item_id for fav in self.favorites)
    
    def access_favorite(self, item_id: str):
        """Record access to favorite item."""
        favorite = next((fav for fav in self.favorites if fav.id == item_id), None)
        if favorite:
            favorite.access_count += 1
            favorite.last_accessed = datetime.now().isoformat()
            self._save_favorites()
            
            if self.on_navigate:
                self.on_navigate(favorite.route, favorite.data)
    
    def get_favorites_sorted(self) -> List[FavoriteItem]:
        """Get favorites sorted by access count and recency."""
        return sorted(self.favorites, key=lambda x: (x.access_count, x.last_accessed or ""), reverse=True)
    
    def create_favorites_panel(self) -> ft.Container:
        """Create favorites panel widget."""
        def refresh_favorites():
            favorites_list.controls.clear()
            
            if not self.favorites:
                favorites_list.controls.append(
                    ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.icons.STAR_OUTLINE, size=48, color=ft.colors.GREY_400),
                            ft.Text("No favorites yet", size=16, color=ft.colors.GREY_600),
                            ft.Text("Pin frequently used items", size=12, color=ft.colors.GREY_500),
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                        padding=20,
                    )
                )
            else:
                sorted_favorites = self.get_favorites_sorted()
                for favorite in sorted_favorites[:8]:  # Show top 8 favorites
                    favorite_item = self._create_favorite_item(favorite)
                    favorites_list.controls.append(favorite_item)
            
            favorites_list.update()
        
        favorites_list = ft.Column([], spacing=4)
        
        favorites_panel = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(ft.icons.STAR, color=ft.colors.AMBER),
                    ft.Text("Favorites", weight=ft.FontWeight.BOLD, size=16),
                    ft.Container(expand=True),
                    ft.IconButton(
                        icon=ft.icons.ADD,
                        tooltip="Add Favorite",
                        icon_size=20,
                        on_click=lambda e: self._show_add_favorite_dialog(e.page, refresh_favorites)
                    ),
                ]),
                ft.Divider(height=1),
                favorites_list,
            ], spacing=8),
            padding=12,
            bgcolor=ft.colors.AMBER_50,
            border_radius=8,
            border=ft.border.all(1, ft.colors.AMBER_200),
        )
        
        refresh_favorites()
        return favorites_panel
    
    def _create_favorite_item(self, favorite: FavoriteItem) -> ft.Container:
        """Create a favorite item widget."""
        return ft.Container(
            content=ft.Row([
                ft.Icon(getattr(ft.icons, favorite.icon.upper(), ft.icons.STAR), size=20, color=ft.colors.BLUE),
                ft.Column([
                    ft.Text(favorite.title, weight=ft.FontWeight.BOLD, size=12),
                    ft.Text(favorite.subtitle, size=10, color=ft.colors.GREY_600),
                ], spacing=2, expand=True),
                ft.Row([
                    ft.Text(str(favorite.access_count), size=10, color=ft.colors.GREY_500),
                    ft.IconButton(
                        icon=ft.icons.CLOSE,
                        icon_size=16,
                        tooltip="Remove favorite",
                        on_click=lambda e, fav_id=favorite.id: self._remove_favorite_click(fav_id, e)
                    ),
                ], spacing=0),
            ], spacing=8),
            padding=ft.padding.symmetric(horizontal=8, vertical=4),
            border_radius=6,
            on_click=lambda e, fav_id=favorite.id: self.access_favorite(fav_id),
            ink=True,
        )
    
    def _remove_favorite_click(self, favorite_id: str, e):
        """Handle remove favorite click."""
        self.remove_favorite(favorite_id)
        # Refresh the parent container
        if hasattr(e.page, 'update'):
            e.page.update()
    
    def _show_add_favorite_dialog(self, page: ft.Page, refresh_callback: Callable):
        """Show dialog to add new favorite."""
        def close_dialog():
            dialog.open = False
            page.update()
        
        def add_selected_favorite(item_id: str):
            if self.add_favorite(item_id):
                close_dialog()
                refresh_callback()
                page.show_snack_bar(
                    ft.SnackBar(content=ft.Text("Added to favorites!"), bgcolor=ft.colors.GREEN)
                )
            else:
                page.show_snack_bar(
                    ft.SnackBar(content=ft.Text("Already in favorites"), bgcolor=ft.colors.ORANGE)
                )
        
        # Filter out already favorited items
        available_items = [item for item in self.available_items if not self.is_favorite(item.id)]
        
        items_list = ft.Column([
            ft.ListTile(
                leading=ft.Icon(getattr(ft.icons, item.icon.upper(), ft.icons.STAR), color=ft.colors.BLUE),
                title=ft.Text(item.title),
                subtitle=ft.Text(item.subtitle),
                on_click=lambda e, item_id=item.id: add_selected_favorite(item_id),
            )
            for item in available_items
        ], spacing=0)
        
        dialog = ft.AlertDialog(
            title=ft.Text("Add to Favorites"),
            content=ft.Container(
                content=ft.Column([
                    ft.Text("Select items to add to your favorites:"),
                    ft.Container(height=10),
                    ft.Container(
                        content=items_list,
                        height=300,
                        width=400,
                    ),
                ]),
                width=400,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: close_dialog()),
            ],
        )
        
        page.dialog = dialog
        dialog.open = True
        page.update()
    
    def create_favorites_quick_bar(self) -> ft.Container:
        """Create a horizontal quick access bar for favorites."""
        def refresh_quick_bar():
            quick_bar_row.controls.clear()
            
            sorted_favorites = self.get_favorites_sorted()
            for favorite in sorted_favorites[:6]:  # Show top 6 in quick bar
                quick_item = ft.Container(
                    content=ft.Column([
                        ft.Icon(getattr(ft.icons, favorite.icon.upper(), ft.icons.STAR), size=24, color=ft.colors.BLUE),
                        ft.Text(favorite.title, size=10, text_align=ft.TextAlign.CENTER),
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=4),
                    padding=8,
                    border_radius=8,
                    on_click=lambda e, fav_id=favorite.id: self.access_favorite(fav_id),
                    ink=True,
                    tooltip=favorite.subtitle,
                    width=80,
                )
                quick_bar_row.controls.append(quick_item)
            
            if not self.favorites:
                quick_bar_row.controls.append(
                    ft.Container(
                        content=ft.Text("No favorites - pin frequently used items", 
                                      size=12, color=ft.colors.GREY_600),
                        padding=20,
                    )
                )
            
            quick_bar_row.update()
        
        quick_bar_row = ft.Row([], spacing=8, scroll=ft.ScrollMode.AUTO)
        
        quick_bar = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(ft.icons.STAR, color=ft.colors.AMBER, size=20),
                    ft.Text("Quick Access", weight=ft.FontWeight.BOLD),
                    ft.Container(expand=True),
                    ft.IconButton(
                        icon=ft.icons.ADD,
                        tooltip="Add Favorite",
                        icon_size=18,
                        on_click=lambda e: self._show_add_favorite_dialog(e.page, refresh_quick_bar)
                    ),
                ]),
                quick_bar_row,
            ], spacing=8),
            padding=12,
            bgcolor=ft.colors.BLUE_50,
            border_radius=8,
            border=ft.border.all(1, ft.colors.BLUE_200),
        )
        
        refresh_quick_bar()
        return quick_bar
    
    def create_favorite_toggle_button(self, item_id: str) -> ft.IconButton:
        """Create a toggle button for favoriting an item."""
        def toggle_favorite(e):
            if self.is_favorite(item_id):
                if self.remove_favorite(item_id):
                    e.control.icon = ft.icons.STAR_OUTLINE
                    e.control.tooltip = "Add to favorites"
                    e.page.show_snack_bar(
                        ft.SnackBar(content=ft.Text("Removed from favorites"), bgcolor=ft.colors.ORANGE)
                    )
            else:
                if self.add_favorite(item_id):
                    e.control.icon = ft.icons.STAR
                    e.control.tooltip = "Remove from favorites"
                    e.page.show_snack_bar(
                        ft.SnackBar(content=ft.Text("Added to favorites!"), bgcolor=ft.colors.GREEN)
                    )
            e.control.update()
        
        is_favorited = self.is_favorite(item_id)
        return ft.IconButton(
            icon=ft.icons.STAR if is_favorited else ft.icons.STAR_OUTLINE,
            icon_color=ft.colors.AMBER if is_favorited else ft.colors.GREY_400,
            tooltip="Remove from favorites" if is_favorited else "Add to favorites",
            on_click=toggle_favorite,
        )
