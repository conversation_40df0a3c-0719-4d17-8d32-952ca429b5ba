import flet as ft
import json
from datetime import datetime
import os
import tempfile
import csv
import io

class ExperimentView:
    """
    Experiment documentation and tracking view for the Science Laboratory Management System.
    Allows users to create, view, and manage experiment documentation, measurements, and results.
    """
    
    def __init__(self, controller):
        """
        Initialize the experiment view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        self.experiments = []
        self.filtered_experiments = []
        self.current_experiment = None
        self.current_tab_index = 0
        
        # Create filter controls
        self.search_field = ft.TextField(
            label="Search",
            prefix_icon=ft.icons.SEARCH,
            on_change=self.filter_experiments,
            expand=True,
        )
        
        self.status_dropdown = ft.Dropdown(
            label="Status",
            on_change=self.filter_experiments,
            options=[
                ft.dropdown.Option("All"),
                ft.dropdown.Option("Draft"),
                ft.dropdown.Option("Active"),
                ft.dropdown.Option("Completed"),
                ft.dropdown.Option("Archived"),
            ],
            value="All",
            width=200,
        )
        
        # Create data table
        self.data_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("Title")),
                ft.DataColumn(ft.Text("Status")),
                ft.DataColumn(ft.Text("Created By")),
                ft.DataColumn(ft.Text("Created At")),
                ft.DataColumn(ft.Text("Last Updated")),
                ft.DataColumn(ft.Text("Actions")),
            ],
            rows=[],
        )
        
        # Create form fields for adding/editing experiments
        self.experiment_id_field = ft.TextField(visible=False)
        self.title_field = ft.TextField(label="Title", required=True)
        self.description_field = ft.TextField(
            label="Description",
            multiline=True,
            min_lines=3,
            max_lines=5,
        )
        self.status_field = ft.Dropdown(
            label="Status",
            required=True,
            options=[
                ft.dropdown.Option("Draft"),
                ft.dropdown.Option("Active"),
                ft.dropdown.Option("Completed"),
                ft.dropdown.Option("Archived"),
            ],
        )
        
        # Create experiment data fields
        self.objective_field = ft.TextField(
            label="Objective",
            multiline=True,
            min_lines=2,
            max_lines=4,
        )
        
        self.materials_field = ft.TextField(
            label="Materials",
            multiline=True,
            min_lines=2,
            max_lines=4,
            hint_text="List materials used in the experiment",
        )
        
        self.procedure_field = ft.TextField(
            label="Procedure",
            multiline=True,
            min_lines=4,
            max_lines=8,
            hint_text="Describe the experimental procedure step by step",
        )
        
        self.observations_field = ft.TextField(
            label="Observations",
            multiline=True,
            min_lines=3,
            max_lines=6,
            hint_text="Record observations during the experiment",
        )
        
        self.results_field = ft.TextField(
            label="Results",
            multiline=True,
            min_lines=3,
            max_lines=6,
            hint_text="Document the results of the experiment",
        )
        
        self.conclusion_field = ft.TextField(
            label="Conclusion",
            multiline=True,
            min_lines=2,
            max_lines=4,
            hint_text="Summarize the findings and conclusions",
        )
        
        # Create collaborator fields
        self.collaborator_dropdown = ft.Dropdown(
            label="Add Collaborator",
            width=300,
        )
        
        self.collaborators_list = ft.ListView(
            spacing=10,
            padding=20,
            auto_scroll=True,
            height=200,
        )
        
        # Create measurement fields
        self.measurement_name_field = ft.TextField(
            label="Measurement Name",
            width=200,
        )
        
        self.measurement_value_field = ft.TextField(
            label="Value",
            width=150,
            keyboard_type=ft.KeyboardType.NUMBER,
        )
        
        self.measurement_unit_field = ft.TextField(
            label="Unit",
            width=100,
        )
        
        self.measurement_notes_field = ft.TextField(
            label="Notes",
            width=300,
        )
        
        self.measurements_list = ft.ListView(
            spacing=10,
            padding=20,
            auto_scroll=True,
            height=300,
        )
        
        # Create file upload fields
        self.file_picker = ft.FilePicker(
            on_result=self.file_picker_result
        )
        
        self.file_description_field = ft.TextField(
            label="File Description",
            width=300,
        )
        
        self.files_list = ft.ListView(
            spacing=10,
            padding=20,
            auto_scroll=True,
            height=200,
        )
        
        # Create comment fields
        self.comment_field = ft.TextField(
            label="Add Comment",
            multiline=True,
            min_lines=2,
            max_lines=4,
            width=500,
        )
        
        self.comments_list = ft.ListView(
            spacing=10,
            padding=20,
            auto_scroll=True,
            height=300,
        )
        
        # Create dialog for adding/editing experiments
        self.experiment_dialog = ft.AlertDialog(
            title=ft.Text("Add Experiment"),
            content=ft.Column(
                [
                    self.experiment_id_field,
                    self.title_field,
                    self.description_field,
                    self.status_field,
                    ft.Divider(),
                    ft.Text("Experiment Details", weight=ft.FontWeight.BOLD),
                    self.objective_field,
                    self.materials_field,
                    self.procedure_field,
                    self.observations_field,
                    self.results_field,
                    self.conclusion_field,
                ],
                tight=True,
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                width=600,
                height=500,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=self.close_dialog),
                ft.TextButton("Save", on_click=self.save_experiment),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Create dialog for viewing experiment details
        self.view_dialog = ft.AlertDialog(
            title=ft.Text("Experiment Details"),
            content=ft.Column(
                [
                    ft.Text("Loading..."),
                ],
                scroll=ft.ScrollMode.AUTO,
                width=800,
                height=600,
            ),
            actions=[
                ft.TextButton("Close", on_click=self.close_view_dialog),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Register the file picker
        self.controller.page.overlay.append(self.file_picker)
        self.controller.page.update()
    
    def build(self):
        """
        Build and return the experiment view.
        
        Returns:
            ft.Container: The experiment view container
        """
        # Load experiment data
        self.load_experiment_data()
        
        # Create add button
        add_button = ft.ElevatedButton(
            "New Experiment",
            icon=ft.icons.ADD,
            on_click=self.show_add_dialog,
        )
        
        # Create export button
        export_button = ft.ElevatedButton(
            "Export",
            icon=ft.icons.DOWNLOAD,
            on_click=self.export_experiments,
        )
        
        # Create header row
        header_row = ft.Row(
            [
                ft.Text("Experiment Documentation", size=24, weight=ft.FontWeight.BOLD),
                ft.Row([add_button, export_button]),
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        )
        
        # Create filters row
        filters_row = ft.Row(
            [
                self.search_field,
                self.status_dropdown,
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        )
        
        # Create data table container
        table_container = ft.Container(
            content=self.data_table,
            border=ft.border.all(1, ft.colors.GREY_300),
            border_radius=5,
            padding=10,
            expand=True,
        )
        
        # Return the complete view
        return ft.Container(
            content=ft.Column(
                [
                    header_row,
                    ft.Divider(),
                    filters_row,
                    ft.Container(height=10),
                    table_container,
                ],
                spacing=10,
                expand=True,
            ),
            padding=20,
            expand=True,
        )
    
    def load_experiment_data(self):
        """Load experiment data from the database."""
        # Get data from the experiment model
        self.experiments = self.controller.experiment_model.get_all_experiments()
        self.filtered_experiments = self.experiments.copy()
        
        # Update the data table
        self.update_data_table()
        
        # Update collaborator dropdown
        self.update_collaborator_dropdown()
    
    def update_data_table(self):
        """Update the data table with filtered experiments."""
        # Clear existing rows
        self.data_table.rows.clear()
        
        # Add rows for filtered experiments
        for experiment in self.filtered_experiments:
            # Format dates
            created_at = self._format_timestamp(experiment["created_at"])
            last_updated = self._format_timestamp(experiment["last_updated"])
            
            # Get status color
            status_color = self._get_status_color(experiment["status"])
            
            # Create row
            self.data_table.rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(experiment["title"])),
                        ft.DataCell(ft.Text(experiment["status"], color=status_color)),
                        ft.DataCell(ft.Text(experiment["creator_name"])),
                        ft.DataCell(ft.Text(created_at)),
                        ft.DataCell(ft.Text(last_updated)),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.icons.VISIBILITY,
                                        tooltip="View",
                                        on_click=lambda e, exp_id=experiment["id"]: self.show_view_dialog(e, exp_id),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.EDIT,
                                        tooltip="Edit",
                                        on_click=lambda e, exp_id=experiment["id"]: self.show_edit_dialog(e, exp_id),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.SCIENCE,
                                        tooltip="Manage Data",
                                        on_click=lambda e, exp_id=experiment["id"]: self.show_data_management(e, exp_id),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.DELETE,
                                        tooltip="Delete",
                                        on_click=lambda e, exp_id=experiment["id"]: self.delete_experiment(e, exp_id),
                                    ),
                                ]
                            )
                        ),
                    ]
                )
            )
        
        # Update the page
        self.controller.page.update()
    
    def update_collaborator_dropdown(self):
        """Update the collaborator dropdown with available users."""
        # Get all users
        users = self.controller.user_model.get_all_users()
        
        # Create options
        options = []
        for user in users:
            # Skip the current user
            if user["id"] == self.controller.current_user["id"]:
                continue
                
            options.append(ft.dropdown.Option(
                key=str(user["id"]),
                text=f"{user['full_name']} ({user['username']})"
            ))
        
        # Update the dropdown
        self.collaborator_dropdown.options = options
        self.collaborator_dropdown.value = None
    
    def filter_experiments(self, e=None):
        """
        Filter experiments based on search and dropdown values.
        
        Args:
            e: The change event (optional)
        """
        search_text = self.search_field.value.lower() if self.search_field.value else ""
        status = self.status_dropdown.value
        
        # Filter experiments
        self.filtered_experiments = []
        for experiment in self.experiments:
            # Apply search filter
            if search_text and search_text not in experiment["title"].lower() and search_text not in (experiment["description"] or "").lower():
                continue
            
            # Apply status filter
            if status != "All" and experiment["status"] != status:
                continue
            
            # Add experiment to filtered list
            self.filtered_experiments.append(experiment)
        
        # Update the data table
        self.update_data_table()
    
    def show_add_dialog(self, e):
        """
        Show dialog for adding a new experiment.
        
        Args:
            e: The click event
        """
        # Clear form fields
        self.experiment_id_field.value = ""
        self.title_field.value = ""
        self.description_field.value = ""
        self.status_field.value = "Draft"
        self.objective_field.value = ""
        self.materials_field.value = ""
        self.procedure_field.value = ""
        self.observations_field.value = ""
        self.results_field.value = ""
        self.conclusion_field.value = ""
        
        # Set dialog title
        self.experiment_dialog.title = ft.Text("Add Experiment")
        
        # Show the dialog
        self.controller.page.dialog = self.experiment_dialog
        self.experiment_dialog.open = True
        self.controller.page.update()
    
    def show_edit_dialog(self, e, experiment_id):
        """
        Show dialog for editing an experiment.
        
        Args:
            e: The click event
            experiment_id: ID of the experiment to edit
        """
        # Get the experiment
        experiment = self.controller.experiment_model.get_experiment_by_id(experiment_id)
        
        if not experiment:
            return
        
        # Set form fields
        self.experiment_id_field.value = str(experiment["id"])
        self.title_field.value = experiment["title"]
        self.description_field.value = experiment["description"] or ""
        self.status_field.value = experiment["status"]
        
        # Parse experiment data JSON
        data = {}
        if experiment["data"]:
            try:
                data = json.loads(experiment["data"])
            except:
                data = {}
        
        # Set experiment data fields
        self.objective_field.value = data.get("objective", "")
        self.materials_field.value = data.get("materials", "")
        self.procedure_field.value = data.get("procedure", "")
        self.observations_field.value = data.get("observations", "")
        self.results_field.value = data.get("results", "")
        self.conclusion_field.value = data.get("conclusion", "")
        
        # Set dialog title
        self.experiment_dialog.title = ft.Text("Edit Experiment")
        
        # Show the dialog
        self.controller.page.dialog = self.experiment_dialog
        self.experiment_dialog.open = True
        self.controller.page.update()
    
    def show_view_dialog(self, e, experiment_id):
        """
        Show dialog for viewing experiment details.
        
        Args:
            e: The click event
            experiment_id: ID of the experiment to view
        """
        # Get the experiment with all data
        experiment = self.controller.experiment_model.get_experiment_by_id(experiment_id, include_data=True)
        
        if not experiment:
            return
        
        # Parse experiment data JSON
        data = {}
        if experiment["data"]:
            try:
                data = json.loads(experiment["data"])
            except:
                data = {}
        
        # Format dates
        created_at = self._format_timestamp(experiment["created_at"])
        last_updated = self._format_timestamp(experiment["last_updated"])
        
        # Create content
        content = ft.Column(
            [
                ft.Text(experiment["title"], size=24, weight=ft.FontWeight.BOLD),
                ft.Text(f"Status: {experiment['status']}", color=self._get_status_color(experiment["status"])),
                ft.Text(f"Created by: {experiment['creator_name']} on {created_at}"),
                ft.Text(f"Last updated: {last_updated}"),
                ft.Divider(),
                
                ft.Text("Description", weight=ft.FontWeight.BOLD),
                ft.Text(experiment["description"] or "No description provided"),
                ft.Divider(),
                
                ft.Text("Objective", weight=ft.FontWeight.BOLD),
                ft.Text(data.get("objective", "No objective provided")),
                ft.Divider(),
                
                ft.Text("Materials", weight=ft.FontWeight.BOLD),
                ft.Text(data.get("materials", "No materials listed")),
                ft.Divider(),
                
                ft.Text("Procedure", weight=ft.FontWeight.BOLD),
                ft.Text(data.get("procedure", "No procedure provided")),
                ft.Divider(),
                
                ft.Text("Observations", weight=ft.FontWeight.BOLD),
                ft.Text(data.get("observations", "No observations recorded")),
                ft.Divider(),
                
                ft.Text("Results", weight=ft.FontWeight.BOLD),
                ft.Text(data.get("results", "No results recorded")),
                ft.Divider(),
                
                ft.Text("Conclusion", weight=ft.FontWeight.BOLD),
                ft.Text(data.get("conclusion", "No conclusion provided")),
            ],
            scroll=ft.ScrollMode.AUTO,
            spacing=10,
            width=800,
            height=600,
        )
        
        # Update the dialog
        self.view_dialog.title = ft.Text(f"Experiment: {experiment['title']}")
        self.view_dialog.content = content
        
        # Show the dialog
        self.controller.page.dialog = self.view_dialog
        self.view_dialog.open = True
        self.controller.page.update()
    
    def show_data_management(self, e, experiment_id):
        """
        Show the data management view for an experiment.
        
        Args:
            e: The click event
            experiment_id: ID of the experiment to manage
        """
        # Get the experiment with all data
        experiment = self.controller.experiment_model.get_experiment_by_id(experiment_id, include_data=True)
        
        if not experiment:
            return
        
        # Store the current experiment
        self.current_experiment = experiment
        
        # Create tabs for different data management sections
        tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            tabs=[
                ft.Tab(
                    text="Overview",
                    content=self._create_overview_tab(experiment),
                ),
                ft.Tab(
                    text="Measurements",
                    content=self._create_measurements_tab(experiment),
                ),
                ft.Tab(
                    text="Files",
                    content=self._create_files_tab(experiment),
                ),
                ft.Tab(
                    text="Collaborators",
                    content=self._create_collaborators_tab(experiment),
                ),
                ft.Tab(
                    text="Comments",
                    content=self._create_comments_tab(experiment),
                ),
            ],
            on_change=self.tab_changed,
        )
        
        # Create the dialog
        data_dialog = ft.AlertDialog(
            title=ft.Text(f"Data Management: {experiment['title']}"),
            content=tabs,
            actions=[
                ft.TextButton("Close", on_click=lambda e: self._close_data_dialog(e, data_dialog)),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = data_dialog
        data_dialog.open = True
        self.controller.page.update()
    
    def _create_overview_tab(self, experiment):
        """Create the overview tab content."""
        # Parse experiment data JSON
        data = {}
        if experiment["data"]:
            try:
                data = json.loads(experiment["data"])
            except:
                data = {}
        
        # Format dates
        created_at = self._format_timestamp(experiment["created_at"])
        last_updated = self._format_timestamp(experiment["last_updated"])
        
        # Create content
        return ft.Column(
            [
                ft.Text(experiment["title"], size=24, weight=ft.FontWeight.BOLD),
                ft.Text(f"Status: {experiment['status']}", color=self._get_status_color(experiment["status"])),
                ft.Text(f"Created by: {experiment['creator_name']} on {created_at}"),
                ft.Text(f"Last updated: {last_updated}"),
                ft.Divider(),
                
                ft.Text("Description", weight=ft.FontWeight.BOLD),
                ft.Text(experiment["description"] or "No description provided"),
                ft.Divider(),
                
                ft.Text("Objective", weight=ft.FontWeight.BOLD),
                ft.Text(data.get("objective", "No objective provided")),
                ft.Divider(),
                
                ft.Row(
                    [
                        ft.ElevatedButton(
                            "Export Data",
                            icon=ft.icons.DOWNLOAD,
                            on_click=lambda e: self.export_experiment_data(e, experiment["id"]),
                        ),
                        ft.ElevatedButton(
                            "Generate Report",
                            icon=ft.icons.DESCRIPTION,
                            on_click=lambda e: self.generate_experiment_report(e, experiment["id"]),
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.CENTER,
                ),
            ],
            scroll=ft.ScrollMode.AUTO,
            spacing=10,
            width=800,
            height=500,
        )
    
    def _create_measurements_tab(self, experiment):
        """Create the measurements tab content."""
        # Get measurements
        measurements = self.controller.experiment_model.get_measurements(experiment["id"])
        
        # Clear the measurements list
        self.measurements_list.controls.clear()
        
        # Add measurements to the list
        for measurement in measurements:
            self.measurements_list.controls.append(
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Row(
                                [
                                    ft.Text(measurement["name"], weight=ft.FontWeight.BOLD),
                                    ft.Text(f"{measurement['value']} {measurement['unit'] or ''}"),
                                ],
                                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                            ),
                            ft.Text(f"Recorded by {measurement['creator_name']} on {self._format_timestamp(measurement['timestamp'])}"),
                            ft.Text(measurement["notes"] or ""),
                        ]
                    ),
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                    padding=10,
                    margin=ft.margin.only(bottom=5),
                )
            )
        
        # Create add measurement button
        add_measurement_button = ft.ElevatedButton(
            "Add Measurement",
            icon=ft.icons.ADD,
            on_click=self.add_measurement,
        )
        
        # Create measurement form
        measurement_form = ft.Row(
            [
                self.measurement_name_field,
                self.measurement_value_field,
                self.measurement_unit_field,
                add_measurement_button,
            ],
            alignment=ft.MainAxisAlignment.START,
            wrap=True,
        )
        
        # Create notes field
        notes_row = ft.Row(
            [
                self.measurement_notes_field,
            ],
            alignment=ft.MainAxisAlignment.START,
        )
        
        # Create content
        return ft.Column(
            [
                ft.Text("Measurements", size=20, weight=ft.FontWeight.BOLD),
                ft.Text("Record quantitative data from your experiment"),
                ft.Divider(),
                measurement_form,
                notes_row,
                ft.Divider(),
                ft.Text("Recorded Measurements", weight=ft.FontWeight.BOLD),
                self.measurements_list,
            ],
            scroll=ft.ScrollMode.AUTO,
            spacing=10,
            width=800,
            height=500,
        )
    
    def _create_files_tab(self, experiment):
        """Create the files tab content."""
        # Get files
        files = self.controller.experiment_model.get_files(experiment["id"])
        
        # Clear the files list
        self.files_list.controls.clear()
        
        # Add files to the list
        for file in files:
            self.files_list.controls.append(
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Row(
                                [
                                    ft.Text(file["filename"], weight=ft.FontWeight.BOLD),
                                    ft.Text(f"{self._format_file_size(file['file_size'])}"),
                                ],
                                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                            ),
                            ft.Text(f"Uploaded by {file['uploader_name']} on {self._format_timestamp(file['uploaded_at'])}"),
                            ft.Text(file["description"] or ""),
                            ft.Row(
                                [
                                    ft.TextButton(
                                        "Download",
                                        icon=ft.icons.DOWNLOAD,
                                        on_click=lambda e, file_id=file["id"]: self.download_file(e, file_id),
                                    ),
                                ],
                                alignment=ft.MainAxisAlignment.END,
                            ),
                        ]
                    ),
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                    padding=10,
                    margin=ft.margin.only(bottom=5),
                )
            )
        
        # Create upload button
        upload_button = ft.ElevatedButton(
            "Select File",
            icon=ft.icons.UPLOAD_FILE,
            on_click=lambda _: self.file_picker.pick_files(allow_multiple=False),
        )
        
        # Create file form
        file_form = ft.Row(
            [
                upload_button,
                self.file_description_field,
                ft.ElevatedButton(
                    "Upload",
                    icon=ft.icons.CLOUD_UPLOAD,
                    on_click=self.upload_file,
                    disabled=True,  # Will be enabled when a file is selected
                    ref="upload_button",
                ),
            ],
            alignment=ft.MainAxisAlignment.START,
            wrap=True,
        )
        
        # Create content
        return ft.Column(
            [
                ft.Text("Files", size=20, weight=ft.FontWeight.BOLD),
                ft.Text("Upload and manage files related to your experiment"),
                ft.Divider(),
                file_form,
                ft.Divider(),
                ft.Text("Uploaded Files", weight=ft.FontWeight.BOLD),
                self.files_list,
            ],
            scroll=ft.ScrollMode.AUTO,
            spacing=10,
            width=800,
            height=500,
        )
    
    def _create_collaborators_tab(self, experiment):
        """Create the collaborators tab content."""
        # Get collaborators
        collaborators = self.controller.experiment_model.get_experiment_collaborators(experiment["id"])
        
        # Clear the collaborators list
        self.collaborators_list.controls.clear()
        
        # Add collaborators to the list
        for collaborator in collaborators:
            self.collaborators_list.controls.append(
                ft.Container(
                    content=ft.Row(
                        [
                            ft.Column(
                                [
                                    ft.Text(collaborator["full_name"], weight=ft.FontWeight.BOLD),
                                    ft.Text(collaborator["email"]),
                                ],
                                spacing=5,
                            ),
                            ft.IconButton(
                                icon=ft.icons.DELETE,
                                tooltip="Remove",
                                on_click=lambda e, user_id=collaborator["user_id"]: self.remove_collaborator(e, user_id),
                            ),
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                    ),
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                    padding=10,
                    margin=ft.margin.only(bottom=5),
                )
            )
        
        # Create add collaborator button
        add_collaborator_button = ft.ElevatedButton(
            "Add",
            icon=ft.icons.PERSON_ADD,
            on_click=self.add_collaborator,
        )
        
        # Create collaborator form
        collaborator_form = ft.Row(
            [
                self.collaborator_dropdown,
                add_collaborator_button,
            ],
            alignment=ft.MainAxisAlignment.START,
        )
        
        # Create content
        return ft.Column(
            [
                ft.Text("Collaborators", size=20, weight=ft.FontWeight.BOLD),
                ft.Text("Manage who can access and edit this experiment"),
                ft.Divider(),
                collaborator_form,
                ft.Divider(),
                ft.Text("Current Collaborators", weight=ft.FontWeight.BOLD),
                self.collaborators_list,
            ],
            scroll=ft.ScrollMode.AUTO,
            spacing=10,
            width=800,
            height=500,
        )
    
    def _create_comments_tab(self, experiment):
        """Create the comments tab content."""
        # Get comments
        comments = self.controller.experiment_model.get_comments(experiment["id"])
        
        # Clear the comments list
        self.comments_list.controls.clear()
        
        # Add comments to the list
        for comment in comments:
            self.comments_list.controls.append(
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Row(
                                [
                                    ft.Text(comment["creator_name"], weight=ft.FontWeight.BOLD),
                                    ft.Text(self._format_timestamp(comment["created_at"])),
                                ],
                                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                            ),
                            ft.Text(comment["comment_text"]),
                        ]
                    ),
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                    padding=10,
                    margin=ft.margin.only(bottom=5),
                )
            )
        
        # Create add comment button
        add_comment_button = ft.ElevatedButton(
            "Add Comment",
            icon=ft.icons.COMMENT,
            on_click=self.add_comment,
        )
        
        # Create comment form
        comment_form = ft.Column(
            [
                self.comment_field,
                ft.Row(
                    [add_comment_button],
                    alignment=ft.MainAxisAlignment.END,
                ),
            ],
            spacing=10,
        )
        
        # Create content
        return ft.Column(
            [
                ft.Text("Comments", size=20, weight=ft.FontWeight.BOLD),
                ft.Text("Discuss the experiment with collaborators"),
                ft.Divider(),
                comment_form,
                ft.Divider(),
                ft.Text("Discussion", weight=ft.FontWeight.BOLD),
                self.comments_list,
            ],
            scroll=ft.ScrollMode.AUTO,
            spacing=10,
            width=800,
            height=500,
        )
    
    def tab_changed(self, e):
        """
        Handle tab change in the data management dialog.
        
        Args:
            e: The change event
        """
        self.current_tab_index = e.control.selected_index
    
    def _close_data_dialog(self, e, dialog):
        """
        Close the data management dialog.
        
        Args:
            e: The click event
            dialog: The dialog to close
        """
        dialog.open = False
        self.controller.page.update()
        
        # Reload experiment data
        self.load_experiment_data()
    
    def add_measurement(self, e):
        """
        Add a measurement to the current experiment.
        
        Args:
            e: The click event
        """
        if not self.current_experiment:
            return
        
        # Validate fields
        if not self.measurement_name_field.value or not self.measurement_value_field.value:
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Please enter a name and value for the measurement"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
            return
        
        try:
            # Convert value to float
            value = float(self.measurement_value_field.value)
            
            # Create measurement data
            measurement_data = {
                "name": self.measurement_name_field.value,
                "value": value,
                "unit": self.measurement_unit_field.value,
                "notes": self.measurement_notes_field.value,
                "timestamp": datetime.now().isoformat(),
            }
            
            # Add the measurement
            self.controller.experiment_model.add_measurement(
                self.current_experiment["id"],
                measurement_data,
                self.controller.current_user["id"]
            )
            
            # Clear fields
            self.measurement_name_field.value = ""
            self.measurement_value_field.value = ""
            self.measurement_unit_field.value = ""
            self.measurement_notes_field.value = ""
            
            # Refresh the measurements list
            measurements = self.controller.experiment_model.get_measurements(self.current_experiment["id"])
            self.measurements_list.controls.clear()
            
            for measurement in measurements:
                self.measurements_list.controls.append(
                    ft.Container(
                        content=ft.Column(
                            [
                                ft.Row(
                                    [
                                        ft.Text(measurement["name"], weight=ft.FontWeight.BOLD),
                                        ft.Text(f"{measurement['value']} {measurement['unit'] or ''}"),
                                    ],
                                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                                ),
                                ft.Text(f"Recorded by {measurement['creator_name']} on {self._format_timestamp(measurement['timestamp'])}"),
                                ft.Text(measurement["notes"] or ""),
                            ]
                        ),
                        border=ft.border.all(1, ft.colors.GREY_300),
                        border_radius=5,
                        padding=10,
                        margin=ft.margin.only(bottom=5),
                    )
                )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Measurement added successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
            
        except ValueError:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Please enter a valid number for the value"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
    
    def file_picker_result(self, e):
        """
        Handle file picker result.
        
        Args:
            e: The result event
        """
        if not e.files or len(e.files) == 0:
            return
        
        # Store the selected file
        self.selected_file = e.files[0]
        
        # Enable the upload button
        upload_button = self.controller.page.get_control("upload_button")
        if upload_button:
            upload_button.disabled = False
            self.controller.page.update()
    
    def upload_file(self, e):
        """
        Upload a file to the current experiment.
        
        Args:
            e: The click event
        """
        if not self.current_experiment or not hasattr(self, 'selected_file'):
            return
        
        # Create a temporary directory to store the file
        temp_dir = tempfile.mkdtemp()
        file_path = os.path.join(temp_dir, self.selected_file.name)
        
        # In a real application, you would save the file to a permanent location
        # For this example, we'll just create a record in the database
        
        # Create file data
        file_data = {
            "filename": self.selected_file.name,
            "file_path": file_path,  # In a real app, this would be a permanent path
            "file_type": self.selected_file.mime_type,
            "file_size": self.selected_file.size,
            "description": self.file_description_field.value,
        }
        
        # Add the file
        self.controller.experiment_model.add_file(
            self.current_experiment["id"],
            file_data,
            self.controller.current_user["id"]
        )
        
        # Clear fields
        self.file_description_field.value = ""
        self.selected_file = None
        
        # Disable the upload button
        upload_button = self.controller.page.get_control("upload_button")
        if upload_button:
            upload_button.disabled = True
        
        # Refresh the files list
        files = self.controller.experiment_model.get_files(self.current_experiment["id"])
        self.files_list.controls.clear()
        
        for file in files:
            self.files_list.controls.append(
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Row(
                                [
                                    ft.Text(file["filename"], weight=ft.FontWeight.BOLD),
                                    ft.Text(f"{self._format_file_size(file['file_size'])}"),
                                ],
                                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                            ),
                            ft.Text(f"Uploaded by {file['uploader_name']} on {self._format_timestamp(file['uploaded_at'])}"),
                            ft.Text(file["description"] or ""),
                            ft.Row(
                                [
                                    ft.TextButton(
                                        "Download",
                                        icon=ft.icons.DOWNLOAD,
                                        on_click=lambda e, file_id=file["id"]: self.download_file(e, file_id),
                                    ),
                                ],
                                alignment=ft.MainAxisAlignment.END,
                            ),
                        ]
                    ),
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                    padding=10,
                    margin=ft.margin.only(bottom=5),
                )
            )
        
        # Show success message
        self.controller.page.snack_bar = ft.SnackBar(
            content=ft.Text("File uploaded successfully"),
            bgcolor=ft.colors.GREEN_400,
        )
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def download_file(self, e, file_id):
        """
        Download a file from the current experiment.
        
        Args:
            e: The click event
            file_id: ID of the file to download
        """
        # In a real application, you would implement file download functionality
        # For this example, we'll just show a message
        
        self.controller.page.snack_bar = ft.SnackBar(
            content=ft.Text("File download functionality would be implemented in a real application"),
            bgcolor=ft.colors.BLUE_400,
        )
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def add_collaborator(self, e):
        """
        Add a collaborator to the current experiment.
        
        Args:
            e: The click event
        """
        if not self.current_experiment or not self.collaborator_dropdown.value:
            return
        
        # Get the selected user ID
        user_id = int(self.collaborator_dropdown.value)
        
        # Add the collaborator
        success = self.controller.experiment_model.add_collaborator(
            self.current_experiment["id"],
            user_id,
            self.controller.current_user["id"]
        )
        
        if success:
            # Clear the dropdown
            self.collaborator_dropdown.value = None
            
            # Refresh the collaborators list
            collaborators = self.controller.experiment_model.get_experiment_collaborators(self.current_experiment["id"])
            self.collaborators_list.controls.clear()
            
            for collaborator in collaborators:
                self.collaborators_list.controls.append(
                    ft.Container(
                        content=ft.Row(
                            [
                                ft.Column(
                                    [
                                        ft.Text(collaborator["full_name"], weight=ft.FontWeight.BOLD),
                                        ft.Text(collaborator["email"]),
                                    ],
                                    spacing=5,
                                ),
                                ft.IconButton(
                                    icon=ft.icons.DELETE,
                                    tooltip="Remove",
                                    on_click=lambda e, user_id=collaborator["user_id"]: self.remove_collaborator(e, user_id),
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        border=ft.border.all(1, ft.colors.GREY_300),
                        border_radius=5,
                        padding=10,
                        margin=ft.margin.only(bottom=5),
                    )
                )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Collaborator added successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
        else:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Failed to add collaborator"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
    
    def remove_collaborator(self, e, user_id):
        """
        Remove a collaborator from the current experiment.
        
        Args:
            e: The click event
            user_id: ID of the user to remove
        """
        if not self.current_experiment:
            return
        
        # Remove the collaborator
        success = self.controller.experiment_model.remove_collaborator(
            self.current_experiment["id"],
            user_id,
            self.controller.current_user["id"]
        )
        
        if success:
            # Refresh the collaborators list
            collaborators = self.controller.experiment_model.get_experiment_collaborators(self.current_experiment["id"])
            self.collaborators_list.controls.clear()
            
            for collaborator in collaborators:
                self.collaborators_list.controls.append(
                    ft.Container(
                        content=ft.Row(
                            [
                                ft.Column(
                                    [
                                        ft.Text(collaborator["full_name"], weight=ft.FontWeight.BOLD),
                                        ft.Text(collaborator["email"]),
                                    ],
                                    spacing=5,
                                ),
                                ft.IconButton(
                                    icon=ft.icons.DELETE,
                                    tooltip="Remove",
                                    on_click=lambda e, user_id=collaborator["user_id"]: self.remove_collaborator(e, user_id),
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        border=ft.border.all(1, ft.colors.GREY_300),
                        border_radius=5,
                        padding=10,
                        margin=ft.margin.only(bottom=5),
                    )
                )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Collaborator removed successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
        else:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Failed to remove collaborator"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
    
    def add_comment(self, e):
        """
        Add a comment to the current experiment.
        
        Args:
            e: The click event
        """
        if not self.current_experiment or not self.comment_field.value:
            return
        
        # Add the comment
        comment_id = self.controller.experiment_model.add_comment(
            self.current_experiment["id"],
            self.comment_field.value,
            self.controller.current_user["id"]
        )
        
        if comment_id:
            # Clear the comment field
            self.comment_field.value = ""
            
            # Refresh the comments list
            comments = self.controller.experiment_model.get_comments(self.current_experiment["id"])
            self.comments_list.controls.clear()
            
            for comment in comments:
                self.comments_list.controls.append(
                    ft.Container(
                        content=ft.Column(
                            [
                                ft.Row(
                                    [
                                        ft.Text(comment["creator_name"], weight=ft.FontWeight.BOLD),
                                        ft.Text(self._format_timestamp(comment["created_at"])),
                                    ],
                                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                                ),
                                ft.Text(comment["comment_text"]),
                            ]
                        ),
                        border=ft.border.all(1, ft.colors.GREY_300),
                        border_radius=5,
                        padding=10,
                        margin=ft.margin.only(bottom=5),
                    )
                )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Comment added successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
        else:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Failed to add comment"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
    
    def save_experiment(self, e):
        """
        Save the experiment.
        
        Args:
            e: The click event
        """
        # Validate required fields
        if not self.title_field.value or not self.status_field.value:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Please fill in all required fields"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
            return
        
        # Prepare experiment data
        experiment_data = {
            "title": self.title_field.value,
            "description": self.description_field.value if self.description_field.value else None,
            "status": self.status_field.value,
            "objective": self.objective_field.value,
            "materials": self.materials_field.value,
            "procedure": self.procedure_field.value,
            "observations": self.observations_field.value,
            "results": self.results_field.value,
            "conclusion": self.conclusion_field.value,
        }
        
        # Add or update experiment
        if self.experiment_id_field.value:
            # Update existing experiment
            experiment_id = int(self.experiment_id_field.value)
            
            success = self.controller.experiment_model.update_experiment(
                experiment_id,
                experiment_data,
                self.controller.current_user["id"]
            )
            
            if success:
                # Show success message
                self.controller.page.snack_bar = ft.SnackBar(
                    content=ft.Text("Experiment updated successfully"),
                    bgcolor=ft.colors.GREEN_400,
                )
            else:
                # Show error message
                self.controller.page.snack_bar = ft.SnackBar(
                    content=ft.Text("Error updating experiment"),
                    bgcolor=ft.colors.RED_400,
                )
                self.controller.page.snack_bar.open = True
                self.controller.page.update()
                return
        else:
            # Add new experiment
            experiment_id = self.controller.experiment_model.create_experiment(
                experiment_data,
                self.controller.current_user["id"]
            )
            
            if experiment_id:
                # Show success message
                self.controller.page.snack_bar = ft.SnackBar(
                    content=ft.Text("Experiment added successfully"),
                    bgcolor=ft.colors.GREEN_400,
                )
            else:
                # Show error message
                self.controller.page.snack_bar = ft.SnackBar(
                    content=ft.Text("Error adding experiment"),
                    bgcolor=ft.colors.RED_400,
                )
                self.controller.page.snack_bar.open = True
                self.controller.page.update()
                return
        
        # Close the dialog
        self.experiment_dialog.open = False
        
        # Reload experiment data
        self.load_experiment_data()
        
        # Update the page
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def close_dialog(self, e):
        """
        Close the experiment dialog.
        
        Args:
            e: The click event
        """
        self.experiment_dialog.open = False
        self.controller.page.update()
    
    def close_view_dialog(self, e):
        """
        Close the view dialog.
        
        Args:
            e: The click event
        """
        self.view_dialog.open = False
        self.controller.page.update()
    
    def delete_experiment(self, e, experiment_id):
        """
        Delete an experiment.
        
        Args:
            e: The click event
            experiment_id: ID of the experiment to delete
        """
        # Find the experiment
        experiment = next((exp for exp in self.experiments if exp["id"] == experiment_id), None)
        
        if not experiment:
            return
        
        # Show confirmation dialog
        def confirm_delete(e):
            # Delete the experiment
            success = self.controller.experiment_model.delete_experiment(
                experiment_id,
                self.controller.current_user["id"]
            )
            
            # Close the dialog
            confirm_dialog.open = False
            
            if success:
                # Show success message
                self.controller.page.snack_bar = ft.SnackBar(
                    content=ft.Text("Experiment deleted successfully"),
                    bgcolor=ft.colors.GREEN_400,
                )
            else:
                # Show error message
                self.controller.page.snack_bar = ft.SnackBar(
                    content=ft.Text("Error deleting experiment"),
                    bgcolor=ft.colors.RED_400,
                )
            
            self.controller.page.snack_bar.open = True
            
            # Reload experiment data
            self.load_experiment_data()
            
            # Update the page
            self.controller.page.update()
        
        # Create confirmation dialog
        confirm_dialog = ft.AlertDialog(
            title=ft.Text("Confirm Delete"),
            content=ft.Text(f"Are you sure you want to delete '{experiment['title']}'? This action cannot be undone."),
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(confirm_dialog, "open", False)),
                ft.TextButton("Delete", on_click=confirm_delete),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = confirm_dialog
        confirm_dialog.open = True
        self.controller.page.update()
    
    def export_experiments(self, e):
        """
        Export all experiments to CSV.
        
        Args:
            e: The click event
        """
        # Create CSV content
        csv_content = "ID,Title,Status,Created By,Created At,Last Updated\n"
        
        for experiment in self.filtered_experiments:
            csv_content += f"{experiment['id']},{experiment['title']},{experiment['status']},{experiment['creator_name']},"
            csv_content += f"{experiment['created_at']},{experiment['last_updated']}\n"
        
        # Create a download link
        download_link = ft.Text(
            "Click here to download",
            color=ft.colors.BLUE,
            style=ft.TextStyle(decoration=ft.TextDecoration.UNDERLINE),
        )
        
        # Create dialog
        export_dialog = ft.AlertDialog(
            title=ft.Text("Export Experiments"),
            content=ft.Column(
                [
                    ft.Text("Your experiment data is ready to export."),
                    download_link,
                ],
                tight=True,
                spacing=10,
            ),
            actions=[
                ft.TextButton("Close", on_click=lambda e: setattr(export_dialog, "open", False)),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = export_dialog
        export_dialog.open = True
        self.controller.page.update()
        
        # In a real application, we would save the CSV to a file and provide a download link
    
    def export_experiment_data(self, e, experiment_id):
        """
        Export detailed data for a specific experiment.
        
        Args:
            e: The click event
            experiment_id: ID of the experiment to export
        """
        # Get the experiment data
        export_data = self.controller.experiment_model.export_experiment_data(experiment_id)
        
        if not export_data:
            return
        
        # Create a download link
        download_link = ft.Text(
            "Click here to download",
            color=ft.colors.BLUE,
            style=ft.TextStyle(decoration=ft.TextDecoration.UNDERLINE),
        )
        
        # Create dialog
        export_dialog = ft.AlertDialog(
            title=ft.Text(f"Export Data: {export_data['experiment']['title']}"),
            content=ft.Column(
                [
                    ft.Text("Your experiment data is ready to export."),
                    download_link,
                ],
                tight=True,
                spacing=10,
            ),
            actions=[
                ft.TextButton("Close", on_click=lambda e: setattr(export_dialog, "open", False)),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = export_dialog
        export_dialog.open = True
        self.controller.page.update()
        
        # In a real application, we would save the data to a file and provide a download link
    
    def generate_experiment_report(self, e, experiment_id):
        """
        Generate a report for a specific experiment.
        
        Args:
            e: The click event
            experiment_id: ID of the experiment to generate a report for
        """
        # Get the experiment data
        export_data = self.controller.experiment_model.export_experiment_data(experiment_id)
        
        if not export_data:
            return
        
        # Create a download link
        download_link = ft.Text(
            "Click here to download",
            color=ft.colors.BLUE,
            style=ft.TextStyle(decoration=ft.TextDecoration.UNDERLINE),
        )
        
        # Create dialog
        export_dialog = ft.AlertDialog(
            title=ft.Text(f"Generate Report: {export_data['experiment']['title']}"),
            content=ft.Column(
                [
                    ft.Text("Your experiment report is ready to download."),
                    download_link,
                ],
                tight=True,
                spacing=10,
            ),
            actions=[
                ft.TextButton("Close", on_click=lambda e: setattr(export_dialog, "open", False)),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = export_dialog
        export_dialog.open = True
        self.controller.page.update()
        
        # In a real application, we would generate a PDF report and provide a download link
    
    def _format_timestamp(self, timestamp):
        """Format a timestamp for display."""
        if not timestamp:
            return "N/A"
        
        try:
            dt = datetime.fromisoformat(timestamp)
            return dt.strftime("%Y-%m-%d %H:%M")
        except:
            return timestamp
    
    def _get_status_color(self, status):
        """Get the color for a status."""
        if status == "Draft":
            return ft.colors.GREY
        elif status == "Active":
            return ft.colors.GREEN
        elif status == "Completed":
            return ft.colors.BLUE
        elif status == "Archived":
            return ft.colors.ORANGE
        else:
            return ft.colors.BLACK
    
    def _format_file_size(self, size_bytes):
        """Format file size for display."""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"