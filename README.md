# Science Laboratory Management System

## Overview
This is a comprehensive management system for science laboratories, built with Python and Flet.

## Requirements
- Python 3.7+
- Flet library
- Other dependencies as specified in requirements.txt

## Installation
1. Clone or download this repository
2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

## Running the Application
To run the application, use the following command from the project root directory:
```
python main.py
```

## Troubleshooting

### ModuleNotFoundError: No module named 'src'
If you encounter this error, it means Python cannot find the 'src' module. This has been fixed in the main.py file by adding the project root directory to the Python path.

If you still encounter this issue, you can try one of these solutions:

1. Run the application from the project root directory:
   ```
   cd "path/to/Science Laboratory Management system"
   python main.py
   ```

2. Set the PYTHONPATH environment variable:
   ```
   # On Windows
   set PYTHONPATH=path/to/Science Laboratory Management system
   
   # On Linux/Mac
   export PYTHONPATH=path/to/Science Laboratory Management system
   ```

3. Create a .env file in the project root with:
   ```
   PYTHONPATH=.
   ```

## Project Structure
- `main.py`: Entry point for the application
- `src/`: Main source code directory
  - `controllers/`: Application controllers
  - `models/`: Data models
  - `views/`: UI views
  - `utils/`: Utility functions and classes
  - `integrations/`: External system integrations