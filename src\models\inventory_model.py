from datetime import datetime

class InventoryModel:
    """
    Model for inventory-related database operations.
    Handles inventory items and transactions.
    """
    
    def __init__(self, db):
        """
        Initialize the inventory model.
        
        Args:
            db: Database instance
        """
        self.db = db
    
    def get_all_items(self):
        """
        Get all inventory items.
        
        Returns:
            list: List of all inventory items
        """
        return self.db.execute_query("SELECT * FROM inventory_items")
    
    def get_item_by_id(self, item_id):
        """
        Get an inventory item by ID.
        
        Args:
            item_id (int): The item ID
            
        Returns:
            dict: Item data if found, None otherwise
        """
        items = self.db.execute_query(
            "SELECT * FROM inventory_items WHERE id = ?",
            (item_id,)
        )
        
        return items[0] if items else None
    
    def get_items_by_category(self, category):
        """
        Get inventory items by category.
        
        Args:
            category (str): The category
            
        Returns:
            list: List of items in the category
        """
        return self.db.execute_query(
            "SELECT * FROM inventory_items WHERE category = ?",
            (category,)
        )
    
    def get_items_by_location(self, location):
        """
        Get inventory items by location.
        
        Args:
            location (str): The location
            
        Returns:
            list: List of items in the location
        """
        return self.db.execute_query(
            "SELECT * FROM inventory_items WHERE location = ?",
            (location,)
        )
    
    def get_low_stock_items(self):
        """
        Get items with quantity below or equal to minimum quantity.
        
        Returns:
            list: List of low stock items
        """
        return self.db.execute_query(
            "SELECT * FROM inventory_items WHERE quantity <= min_quantity AND min_quantity IS NOT NULL"
        )
    
    def get_expiring_items(self, days=30):
        """
        Get items expiring within the specified number of days.
        
        Args:
            days (int): Number of days to check for expiry
            
        Returns:
            list: List of expiring items
        """
        today = datetime.now().strftime("%Y-%m-%d")
        future_date = datetime.now().replace(day=datetime.now().day + days).strftime("%Y-%m-%d")
        
        return self.db.execute_query(
            "SELECT * FROM inventory_items WHERE expiry_date IS NOT NULL AND expiry_date <= ? AND expiry_date >= ?",
            (future_date, today)
        )
    
    def get_expired_items(self):
        """
        Get expired items.
        
        Returns:
            list: List of expired items
        """
        today = datetime.now().strftime("%Y-%m-%d")
        
        return self.db.execute_query(
            "SELECT * FROM inventory_items WHERE expiry_date IS NOT NULL AND expiry_date < ?",
            (today,)
        )
    
    def add_item(self, item_data, user_id):
        """
        Add a new inventory item.
        
        Args:
            item_data (dict): The item data
            user_id (int): ID of the user adding the item
            
        Returns:
            int: ID of the created item
        """
        # Prepare item data
        keys = list(item_data.keys())
        placeholders = ["?"] * len(keys)
        
        # Add user and timestamp
        keys.append("added_by")
        placeholders.append("?")
        
        query = f"INSERT INTO inventory_items ({', '.join(keys)}) VALUES ({', '.join(placeholders)})"
        params = list(item_data.values())
        params.append(user_id)
        
        # Execute the insert
        item_id = self.db.execute_insert(query, tuple(params))
        
        # Record transaction
        self.record_transaction(
            item_id=item_id,
            transaction_type="addition",
            quantity=float(item_data["quantity"]),
            previous_quantity=0,
            new_quantity=float(item_data["quantity"]),
            unit=item_data["unit"],
            reason="Initial stock",
            user_id=user_id,
            location=item_data.get("location")
        )
        
        # Log the action
        self.db.log_audit(
            user_id,
            "create",
            "inventory_item",
            item_id,
            f"Added new inventory item: {item_data['name']}"
        )
        
        return item_id
    
    def update_item(self, item_id, item_data, user_id):
        """
        Update an inventory item.
        
        Args:
            item_id (int): The item ID
            item_data (dict): The updated item data
            user_id (int): ID of the user updating the item
            
        Returns:
            bool: True if update successful, False otherwise
        """
        # Get current item
        current_item = self.get_item_by_id(item_id)
        if not current_item:
            return False
        
        # Check if quantity has changed
        quantity_changed = False
        if "quantity" in item_data and float(item_data["quantity"]) != float(current_item["quantity"]):
            quantity_changed = True
            previous_quantity = float(current_item["quantity"])
            new_quantity = float(item_data["quantity"])
            quantity_change = new_quantity - previous_quantity
            transaction_type = "addition" if quantity_change > 0 else "adjustment" if quantity_change < 0 else None
        
        # Build the update query
        query_parts = []
        params = []
        
        for key, value in item_data.items():
            query_parts.append(f"{key} = ?")
            params.append(value)
        
        # Add last_updated timestamp
        query_parts.append("last_updated = ?")
        params.append(datetime.now().isoformat())
        
        query = f"UPDATE inventory_items SET {', '.join(query_parts)} WHERE id = ?"
        params.append(item_id)
        
        # Execute the update
        self.db.execute_query(query, tuple(params))
        
        # Record transaction if quantity changed
        if quantity_changed and transaction_type:
            self.record_transaction(
                item_id=item_id,
                transaction_type=transaction_type,
                quantity=abs(quantity_change),
                previous_quantity=previous_quantity,
                new_quantity=new_quantity,
                unit=current_item["unit"],
                reason="Manual adjustment",
                user_id=user_id,
                location=item_data.get("location", current_item["location"])
            )
        
        # Log the action
        self.db.log_audit(
            user_id,
            "update",
            "inventory_item",
            item_id,
            f"Updated inventory item: {current_item['name']}"
        )
        
        return True
    
    def delete_item(self, item_id, user_id):
        """
        Delete an inventory item.
        
        Args:
            item_id (int): The item ID
            user_id (int): ID of the user deleting the item
            
        Returns:
            bool: True if deletion successful, False otherwise
        """
        # Get current item
        item = self.get_item_by_id(item_id)
        if not item:
            return False
        
        # Delete the item
        self.db.execute_query(
            "DELETE FROM inventory_items WHERE id = ?",
            (item_id,)
        )
        
        # Log the action
        self.db.log_audit(
            user_id,
            "delete",
            "inventory_item",
            item_id,
            f"Deleted inventory item: {item['name']}"
        )
        
        return True
    
    def update_item_quantity(self, item_id, quantity_change, reason, user_id, reference_id=None, reference_type=None, location=None, notes=None):
        """
        Update an item's quantity and record the transaction.
        
        Args:
            item_id (int): The item ID
            quantity_change (float): The quantity change (positive for addition, negative for removal)
            reason (str): Reason for the quantity change
            user_id (int): ID of the user making the change
            reference_id (int, optional): ID of a related entity (e.g., experiment ID)
            reference_type (str, optional): Type of the related entity (e.g., "experiment")
            location (str, optional): Location of the item
            notes (str, optional): Additional notes
            
        Returns:
            bool: True if update successful, False otherwise
        """
        # Get current item
        item = self.get_item_by_id(item_id)
        if not item:
            return False
        
        # Calculate new quantity
        previous_quantity = float(item["quantity"])
        new_quantity = previous_quantity + quantity_change
        
        # Ensure quantity doesn't go below zero
        if new_quantity < 0:
            return False
        
        # Determine transaction type
        transaction_type = "addition" if quantity_change > 0 else "removal"
        
        # Update item quantity
        self.db.execute_query(
            "UPDATE inventory_items SET quantity = ?, last_updated = ? WHERE id = ?",
            (new_quantity, datetime.now().isoformat(), item_id)
        )
        
        # Record transaction
        self.record_transaction(
            item_id=item_id,
            transaction_type=transaction_type,
            quantity=abs(quantity_change),
            previous_quantity=previous_quantity,
            new_quantity=new_quantity,
            unit=item["unit"],
            reason=reason,
            reference_id=reference_id,
            reference_type=reference_type,
            location=location or item["location"],
            user_id=user_id,
            notes=notes
        )
        
        # Log the action
        self.db.log_audit(
            user_id,
            "update",
            "inventory_item",
            item_id,
            f"{transaction_type.capitalize()} of {abs(quantity_change)} {item['unit']} to/from {item['name']}"
        )
        
        return True
    
    def transfer_item(self, item_id, quantity, source_location, destination_location, reason, user_id, reference_id=None, reference_type=None, notes=None):
        """
        Transfer an item from one location to another.
        
        Args:
            item_id (int): The item ID
            quantity (float): The quantity to transfer
            source_location (str): Source location
            destination_location (str): Destination location
            reason (str): Reason for the transfer
            user_id (int): ID of the user making the transfer
            reference_id (int, optional): ID of a related entity
            reference_type (str, optional): Type of the related entity
            notes (str, optional): Additional notes
            
        Returns:
            bool: True if transfer successful, False otherwise
        """
        # Get current item
        item = self.get_item_by_id(item_id)
        if not item:
            return False
        
        # Ensure quantity is valid
        if quantity <= 0 or quantity > float(item["quantity"]):
            return False
        
        # Record the transfer transaction
        transaction_id = self.record_transaction(
            item_id=item_id,
            transaction_type="transfer",
            quantity=quantity,
            previous_quantity=float(item["quantity"]),
            new_quantity=float(item["quantity"]),  # Quantity doesn't change, just location
            unit=item["unit"],
            reason=reason,
            reference_id=reference_id,
            reference_type=reference_type,
            location=source_location,
            destination_location=destination_location,
            user_id=user_id,
            notes=notes
        )
        
        # Log the action
        self.db.log_audit(
            user_id,
            "transfer",
            "inventory_item",
            item_id,
            f"Transferred {quantity} {item['unit']} of {item['name']} from {source_location} to {destination_location}"
        )
        
        return transaction_id is not None
    
    def record_transaction(self, item_id, transaction_type, quantity, previous_quantity, new_quantity, 
                          unit, user_id, reason=None, reference_id=None, reference_type=None, 
                          location=None, destination_location=None, notes=None):
        """
        Record an inventory transaction.
        
        Args:
            item_id (int): The item ID
            transaction_type (str): Type of transaction (addition, removal, transfer, adjustment)
            quantity (float): The quantity involved in the transaction
            previous_quantity (float): The quantity before the transaction
            new_quantity (float): The quantity after the transaction
            unit (str): The unit of measurement
            user_id (int): ID of the user making the transaction
            reason (str, optional): Reason for the transaction
            reference_id (int, optional): ID of a related entity
            reference_type (str, optional): Type of the related entity
            location (str, optional): Location of the item
            destination_location (str, optional): Destination location for transfers
            notes (str, optional): Additional notes
            
        Returns:
            int: ID of the created transaction
        """
        return self.db.execute_insert(
            """INSERT INTO inventory_transactions 
               (item_id, transaction_type, quantity, previous_quantity, new_quantity, unit, 
                reason, reference_id, reference_type, location, destination_location, user_id, notes) 
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
            (item_id, transaction_type, quantity, previous_quantity, new_quantity, unit, 
             reason, reference_id, reference_type, location, destination_location, user_id, notes)
        )
    
    def get_transactions(self, item_id=None, transaction_type=None, start_date=None, end_date=None, user_id=None, limit=100):
        """
        Get inventory transactions with optional filtering.
        
        Args:
            item_id (int, optional): Filter by item ID
            transaction_type (str, optional): Filter by transaction type
            start_date (str, optional): Filter by start date (ISO format)
            end_date (str, optional): Filter by end date (ISO format)
            user_id (int, optional): Filter by user ID
            limit (int, optional): Maximum number of transactions to return
            
        Returns:
            list: List of transactions
        """
        query = """
            SELECT 
                t.*,
                i.name as item_name,
                i.category,
                u.username,
                u.full_name
            FROM 
                inventory_transactions t
                JOIN inventory_items i ON t.item_id = i.id
                JOIN users u ON t.user_id = u.id
            WHERE 1=1
        """
        params = []
        
        # Add filters
        if item_id:
            query += " AND t.item_id = ?"
            params.append(item_id)
        
        if transaction_type:
            query += " AND t.transaction_type = ?"
            params.append(transaction_type)
        
        if start_date:
            query += " AND t.timestamp >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND t.timestamp <= ?"
            params.append(end_date)
        
        if user_id:
            query += " AND t.user_id = ?"
            params.append(user_id)
        
        # Add order and limit
        query += " ORDER BY t.timestamp DESC LIMIT ?"
        params.append(limit)
        
        return self.db.execute_query(query, tuple(params))
    
    def get_item_transaction_history(self, item_id, limit=50):
        """
        Get transaction history for a specific item.
        
        Args:
            item_id (int): The item ID
            limit (int, optional): Maximum number of transactions to return
            
        Returns:
            list: List of transactions for the item
        """
        return self.get_transactions(item_id=item_id, limit=limit)
    
    def get_categories(self):
        """
        Get all unique categories.
        
        Returns:
            list: List of unique categories
        """
        result = self.db.execute_query("SELECT DISTINCT category FROM inventory_items WHERE category IS NOT NULL")
        return [item["category"] for item in result]
    
    def get_locations(self):
        """
        Get all unique locations.
        
        Returns:
            list: List of unique locations
        """
        result = self.db.execute_query("SELECT DISTINCT location FROM inventory_items WHERE location IS NOT NULL")
        return [item["location"] for item in result]