#!/usr/bin/env python
"""
Navigation Demo for Science Laboratory Management System
Demonstrates navigation components, patterns, and interactions
"""
import flet as ft
import time
from datetime import datetime


def main(page: ft.Page):
    """Main navigation demo application."""
    page.title = "Navigation Demo - Lab Management System"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window_width = 1400
    page.window_height = 900
    page.padding = 0
    
    # Demo state
    current_route = "dashboard"
    nav_extended = True
    notification_count = 3
    
    def show_notification(message, type_="info"):
        """Show a notification."""
        page.show_snack_bar(
            ft.SnackBar(
                content=ft.Text(message),
                bgcolor=ft.colors.BLUE if type_ == "info" else ft.colors.GREEN,
                action="OK"
            )
        )
    
    def update_breadcrumb(route):
        """Update breadcrumb navigation."""
        route_names = {
            "dashboard": "Dashboard",
            "inventory": "Inventory Management", 
            "experiments": "Experiment Tracking",
            "scheduling": "Resource Scheduling",
            "reports": "Reports & Analytics",
            "users": "User Management",
            "settings": "System Settings"
        }
        breadcrumb_text.value = route_names.get(route, route.capitalize())
        page.update()
    
    def navigate_to(route):
        """Navigate to a specific route."""
        nonlocal current_route
        current_route = route
        
        # Update breadcrumb
        update_breadcrumb(route)
        
        # Update navigation rail selection
        route_indices = {
            "dashboard": 0, "inventory": 1, "experiments": 2,
            "scheduling": 3, "reports": 4, "users": 5
        }
        if route in route_indices:
            nav_rail.selected_index = route_indices[route]
        
        # Update content area
        update_content_area(route)
        
        # Show navigation feedback
        show_notification(f"Navigated to {route_names.get(route, route)}")
    
    def update_content_area(route):
        """Update the main content area based on route."""
        route_content = {
            "dashboard": create_dashboard_content(),
            "inventory": create_inventory_content(),
            "experiments": create_experiments_content(),
            "scheduling": create_scheduling_content(),
            "reports": create_reports_content(),
            "users": create_users_content(),
            "settings": create_settings_content()
        }
        
        content_area.content = route_content.get(route, create_default_content(route))
        page.update()
    
    def create_dashboard_content():
        """Create dashboard content."""
        return ft.Column([
            ft.Text("📊 Dashboard Overview", size=24, weight=ft.FontWeight.BOLD),
            ft.Container(height=20),
            ft.Row([
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.icons.INVENTORY, size=40, color=ft.colors.BLUE),
                            ft.Text("1,247", size=32, weight=ft.FontWeight.BOLD),
                            ft.Text("Total Items", size=14, color=ft.colors.GREY_600),
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=20,
                        width=200
                    )
                ),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.icons.SCIENCE, size=40, color=ft.colors.GREEN),
                            ft.Text("23", size=32, weight=ft.FontWeight.BOLD),
                            ft.Text("Active Experiments", size=14, color=ft.colors.GREY_600),
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=20,
                        width=200
                    )
                ),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.icons.WARNING, size=40, color=ft.colors.ORANGE),
                            ft.Text("8", size=32, weight=ft.FontWeight.BOLD),
                            ft.Text("Low Stock Items", size=14, color=ft.colors.GREY_600),
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=20,
                        width=200
                    )
                ),
            ], spacing=20),
            ft.Container(height=30),
            ft.Text("🚀 Quick Actions", size=18, weight=ft.FontWeight.BOLD),
            ft.Row([
                ft.ElevatedButton("Add Inventory Item", icon=ft.icons.ADD, on_click=lambda e: navigate_to("inventory")),
                ft.ElevatedButton("New Experiment", icon=ft.icons.SCIENCE, on_click=lambda e: navigate_to("experiments")),
                ft.ElevatedButton("View Reports", icon=ft.icons.ANALYTICS, on_click=lambda e: navigate_to("reports")),
            ], spacing=10)
        ], scroll=ft.ScrollMode.AUTO)
    
    def create_inventory_content():
        """Create inventory content."""
        return ft.Column([
            ft.Text("📦 Inventory Management", size=24, weight=ft.FontWeight.BOLD),
            ft.Text("Manage chemicals, equipment, and laboratory supplies", color=ft.colors.GREY_600),
            ft.Container(height=20),
            ft.Row([
                ft.ElevatedButton("Add Item", icon=ft.icons.ADD),
                ft.ElevatedButton("Import CSV", icon=ft.icons.UPLOAD),
                ft.ElevatedButton("Export Data", icon=ft.icons.DOWNLOAD),
                ft.OutlinedButton("Low Stock Alert", icon=ft.icons.WARNING),
            ], spacing=10),
            ft.Container(height=20),
            ft.DataTable(
                columns=[
                    ft.DataColumn(ft.Text("Name")),
                    ft.DataColumn(ft.Text("Category")),
                    ft.DataColumn(ft.Text("Quantity")),
                    ft.DataColumn(ft.Text("Location")),
                    ft.DataColumn(ft.Text("Status")),
                ],
                rows=[
                    ft.DataRow(cells=[
                        ft.DataCell(ft.Text("Sodium Chloride")),
                        ft.DataCell(ft.Text("Chemical")),
                        ft.DataCell(ft.Text("500g")),
                        ft.DataCell(ft.Text("Cabinet A1")),
                        ft.DataCell(ft.Text("Available", color=ft.colors.GREEN)),
                    ]),
                    ft.DataRow(cells=[
                        ft.DataCell(ft.Text("Microscope")),
                        ft.DataCell(ft.Text("Equipment")),
                        ft.DataCell(ft.Text("1")),
                        ft.DataCell(ft.Text("Lab Room 1")),
                        ft.DataCell(ft.Text("In Use", color=ft.colors.ORANGE)),
                    ]),
                    ft.DataRow(cells=[
                        ft.DataCell(ft.Text("Hydrochloric Acid")),
                        ft.DataCell(ft.Text("Chemical")),
                        ft.DataCell(ft.Text("250ml")),
                        ft.DataCell(ft.Text("Fume Hood")),
                        ft.DataCell(ft.Text("Low Stock", color=ft.colors.RED)),
                    ]),
                ]
            )
        ], scroll=ft.ScrollMode.AUTO)
    
    def create_experiments_content():
        """Create experiments content."""
        return ft.Column([
            ft.Text("🧪 Experiment Tracking", size=24, weight=ft.FontWeight.BOLD),
            ft.Text("Track research projects and experimental procedures", color=ft.colors.GREY_600),
            ft.Container(height=20),
            ft.Row([
                ft.ElevatedButton("New Experiment", icon=ft.icons.ADD),
                ft.ElevatedButton("Templates", icon=ft.icons.CONTENT_COPY),
                ft.OutlinedButton("Collaboration", icon=ft.icons.PEOPLE),
            ], spacing=10),
            ft.Container(height=20),
            ft.Text("📋 Active Experiments", size=18, weight=ft.FontWeight.BOLD),
            ft.Column([
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Text("Protein Synthesis Study", weight=ft.FontWeight.BOLD),
                                ft.Container(expand=True),
                                ft.Chip(label=ft.Text("In Progress"), bgcolor=ft.colors.BLUE_100),
                            ]),
                            ft.Text("Investigating protein synthesis mechanisms in E. coli", color=ft.colors.GREY_600),
                            ft.Row([
                                ft.Text("Started: 2025-01-10", size=12),
                                ft.Container(expand=True),
                                ft.Text("Researcher: Dr. Smith", size=12),
                            ]),
                        ]),
                        padding=15
                    )
                ),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Text("Chemical Reaction Analysis", weight=ft.FontWeight.BOLD),
                                ft.Container(expand=True),
                                ft.Chip(label=ft.Text("Planning"), bgcolor=ft.colors.ORANGE_100),
                            ]),
                            ft.Text("Analysis of catalytic reactions under various conditions", color=ft.colors.GREY_600),
                            ft.Row([
                                ft.Text("Started: 2025-01-15", size=12),
                                ft.Container(expand=True),
                                ft.Text("Researcher: Dr. Johnson", size=12),
                            ]),
                        ]),
                        padding=15
                    )
                ),
            ], spacing=10)
        ], scroll=ft.ScrollMode.AUTO)
    
    def create_scheduling_content():
        """Create scheduling content."""
        return ft.Column([
            ft.Text("📅 Resource Scheduling", size=24, weight=ft.FontWeight.BOLD),
            ft.Text("Book laboratory equipment and facilities", color=ft.colors.GREY_600),
            ft.Container(height=20),
            ft.Row([
                ft.ElevatedButton("New Booking", icon=ft.icons.ADD),
                ft.ElevatedButton("Calendar View", icon=ft.icons.CALENDAR_MONTH),
                ft.OutlinedButton("Availability", icon=ft.icons.SCHEDULE),
            ], spacing=10),
            ft.Container(height=20),
            ft.Text("📋 Today's Schedule", size=18, weight=ft.FontWeight.BOLD),
            ft.Text("Monday, January 20, 2025", color=ft.colors.GREY_600),
            ft.Container(height=10),
            ft.Column([
                ft.ListTile(
                    leading=ft.Icon(ft.icons.SCIENCE, color=ft.colors.BLUE),
                    title=ft.Text("Microscope Lab - Room 101"),
                    subtitle=ft.Text("09:00 - 11:00 | Dr. Smith"),
                    trailing=ft.Chip(label=ft.Text("Confirmed"), bgcolor=ft.colors.GREEN_100),
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.icons.BIOTECH, color=ft.colors.GREEN),
                    title=ft.Text("Centrifuge - Lab 2"),
                    subtitle=ft.Text("14:00 - 16:00 | Dr. Johnson"),
                    trailing=ft.Chip(label=ft.Text("Pending"), bgcolor=ft.colors.ORANGE_100),
                ),
                ft.ListTile(
                    leading=ft.Icon(ft.icons.SCIENCE, color=ft.colors.PURPLE),
                    title=ft.Text("Fume Hood - Lab 3"),
                    subtitle=ft.Text("16:30 - 18:00 | Student Group A"),
                    trailing=ft.Chip(label=ft.Text("Available"), bgcolor=ft.colors.BLUE_100),
                ),
            ])
        ], scroll=ft.ScrollMode.AUTO)
    
    def create_reports_content():
        """Create reports content."""
        return ft.Column([
            ft.Text("📊 Reports & Analytics", size=24, weight=ft.FontWeight.BOLD),
            ft.Text("Generate insights and analytics reports", color=ft.colors.GREY_600),
            ft.Container(height=20),
            ft.Row([
                ft.ElevatedButton("Generate Report", icon=ft.icons.ANALYTICS),
                ft.ElevatedButton("Export PDF", icon=ft.icons.PICTURE_AS_PDF),
                ft.OutlinedButton("Schedule Report", icon=ft.icons.SCHEDULE),
            ], spacing=10),
            ft.Container(height=20),
            ft.Text("📈 Quick Reports", size=18, weight=ft.FontWeight.BOLD),
            ft.Row([
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("Inventory Usage", weight=ft.FontWeight.BOLD),
                            ft.Text("Last 30 days", color=ft.colors.GREY_600),
                            ft.ElevatedButton("View Report", size=ft.ControlSize.SMALL),
                        ]),
                        padding=15,
                        width=200
                    )
                ),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("Experiment Progress", weight=ft.FontWeight.BOLD),
                            ft.Text("Current quarter", color=ft.colors.GREY_600),
                            ft.ElevatedButton("View Report", size=ft.ControlSize.SMALL),
                        ]),
                        padding=15,
                        width=200
                    )
                ),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text("Resource Utilization", weight=ft.FontWeight.BOLD),
                            ft.Text("Equipment usage", color=ft.colors.GREY_600),
                            ft.ElevatedButton("View Report", size=ft.ControlSize.SMALL),
                        ]),
                        padding=15,
                        width=200
                    )
                ),
            ], spacing=15)
        ], scroll=ft.ScrollMode.AUTO)
    
    def create_users_content():
        """Create users content."""
        return ft.Column([
            ft.Text("👥 User Management", size=24, weight=ft.FontWeight.BOLD),
            ft.Text("Manage system users and permissions", color=ft.colors.GREY_600),
            ft.Container(height=20),
            ft.Row([
                ft.ElevatedButton("Add User", icon=ft.icons.PERSON_ADD),
                ft.ElevatedButton("Import Users", icon=ft.icons.UPLOAD),
                ft.OutlinedButton("Permissions", icon=ft.icons.SECURITY),
            ], spacing=10),
            ft.Container(height=20),
            ft.Text("👤 Active Users", size=18, weight=ft.FontWeight.BOLD),
            ft.Column([
                ft.ListTile(
                    leading=ft.CircleAvatar(content=ft.Text("AS"), bgcolor=ft.colors.BLUE),
                    title=ft.Text("Admin Smith"),
                    subtitle=ft.Text("Administrator | <EMAIL>"),
                    trailing=ft.Chip(label=ft.Text("Online"), bgcolor=ft.colors.GREEN_100),
                ),
                ft.ListTile(
                    leading=ft.CircleAvatar(content=ft.Text("DJ"), bgcolor=ft.colors.GREEN),
                    title=ft.Text("Dr. Johnson"),
                    subtitle=ft.Text("Researcher | <EMAIL>"),
                    trailing=ft.Chip(label=ft.Text("Offline"), bgcolor=ft.colors.GREY_100),
                ),
                ft.ListTile(
                    leading=ft.CircleAvatar(content=ft.Text("SG"), bgcolor=ft.colors.ORANGE),
                    title=ft.Text("Student Group"),
                    subtitle=ft.Text("Student | <EMAIL>"),
                    trailing=ft.Chip(label=ft.Text("Online"), bgcolor=ft.colors.GREEN_100),
                ),
            ])
        ], scroll=ft.ScrollMode.AUTO)
    
    def create_settings_content():
        """Create settings content."""
        return ft.Column([
            ft.Text("⚙️ System Settings", size=24, weight=ft.FontWeight.BOLD),
            ft.Text("Configure system preferences and options", color=ft.colors.GREY_600),
            ft.Container(height=20),
            ft.Text("🎨 Appearance", size=18, weight=ft.FontWeight.BOLD),
            ft.Row([
                ft.Switch(label="Dark Mode", value=False),
                ft.Switch(label="Compact View", value=False),
            ], spacing=20),
            ft.Container(height=20),
            ft.Text("🔔 Notifications", size=18, weight=ft.FontWeight.BOLD),
            ft.Column([
                ft.Switch(label="Email Notifications", value=True),
                ft.Switch(label="Push Notifications", value=True),
                ft.Switch(label="Low Stock Alerts", value=True),
            ], spacing=10),
            ft.Container(height=20),
            ft.Text("🔒 Security", size=18, weight=ft.FontWeight.BOLD),
            ft.Column([
                ft.Switch(label="Two-Factor Authentication", value=False),
                ft.Switch(label="Session Timeout", value=True),
                ft.ElevatedButton("Change Password", icon=ft.icons.LOCK),
            ], spacing=10),
        ], scroll=ft.ScrollMode.AUTO)
    
    def create_default_content(route):
        """Create default content for unknown routes."""
        return ft.Column([
            ft.Text(f"🚧 {route.title()} Page", size=24, weight=ft.FontWeight.BOLD),
            ft.Text("This page is under construction.", color=ft.colors.GREY_600),
            ft.Container(height=20),
            ft.ElevatedButton("Back to Dashboard", icon=ft.icons.DASHBOARD, on_click=lambda e: navigate_to("dashboard")),
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, alignment=ft.MainAxisAlignment.CENTER)
    
    def toggle_nav_rail(e):
        """Toggle navigation rail extended state."""
        nonlocal nav_extended
        nav_extended = not nav_extended
        nav_rail.extended = nav_extended
        
        if nav_extended:
            e.control.icon = ft.icons.MENU
            e.control.tooltip = "Collapse Navigation"
        else:
            e.control.icon = ft.icons.MENU_OPEN
            e.control.tooltip = "Expand Navigation"
        
        page.update()
    
    def nav_rail_change(e):
        """Handle navigation rail selection changes."""
        routes = ["dashboard", "inventory", "experiments", "scheduling", "reports", "users"]
        if 0 <= e.control.selected_index < len(routes):
            navigate_to(routes[e.control.selected_index])
    
    # Create breadcrumb text
    breadcrumb_text = ft.Text("Dashboard", color=ft.colors.WHITE, size=14)
    
    # Create app bar
    app_bar = ft.AppBar(
        leading=ft.Icon(ft.icons.SCIENCE, color=ft.colors.WHITE),
        title=ft.Row([
            ft.Text("Navigation Demo", weight=ft.FontWeight.BOLD),
            ft.Icon(ft.icons.CHEVRON_RIGHT, size=20, color=ft.colors.WHITE70),
            breadcrumb_text,
        ], spacing=5),
        center_title=False,
        bgcolor=ft.colors.BLUE,
        color=ft.colors.WHITE,
        toolbar_height=60,
        actions=[
            ft.IconButton(
                icon=ft.icons.SEARCH,
                tooltip="Global Search",
                on_click=lambda e: show_notification("Search functionality demo")
            ),
            ft.IconButton(
                icon=ft.icons.NOTIFICATIONS,
                tooltip="Notifications",
                badge=str(notification_count),
                on_click=lambda e: show_notification("3 new notifications")
            ),
            ft.IconButton(
                icon=ft.icons.ACCOUNT_CIRCLE,
                tooltip="User Profile",
                on_click=lambda e: show_notification("User profile menu")
            ),
            ft.IconButton(
                icon=ft.icons.HELP_OUTLINE,
                tooltip="Help",
                on_click=lambda e: show_notification("Help system demo")
            ),
        ],
        elevation=4,
    )
    
    # Create navigation rail
    nav_rail = ft.NavigationRail(
        selected_index=0,
        label_type=ft.NavigationRailLabelType.ALL,
        min_width=100,
        min_extended_width=220,
        extended=nav_extended,
        bgcolor=ft.colors.BLUE_50,
        leading=ft.Container(
            content=ft.Column([
                ft.Container(
                    content=ft.Icon(ft.icons.SCIENCE_OUTLINED, size=40, color=ft.colors.BLUE),
                    margin=ft.margin.only(top=20, bottom=5),
                ),
                ft.Text("Navigation Demo", size=14, weight=ft.FontWeight.BOLD, color=ft.colors.BLUE),
                ft.Divider(thickness=1, height=20),
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            padding=ft.padding.only(bottom=10),
        ),
        trailing=ft.Container(
            content=ft.Column([
                ft.IconButton(
                    icon=ft.icons.SETTINGS,
                    icon_color=ft.colors.BLUE_900,
                    tooltip="Settings",
                    on_click=lambda e: navigate_to("settings"),
                ),
                ft.IconButton(
                    icon=ft.icons.HELP_OUTLINE,
                    icon_color=ft.colors.BLUE_900,
                    tooltip="Help",
                    on_click=lambda e: show_notification("Help system"),
                ),
                ft.Container(height=20),
                ft.IconButton(
                    icon=ft.icons.MENU,
                    icon_color=ft.colors.BLUE_900,
                    tooltip="Toggle Navigation",
                    on_click=toggle_nav_rail,
                ),
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            padding=ft.padding.only(bottom=20),
        ),
        destinations=[
            ft.NavigationRailDestination(
                icon=ft.icons.DASHBOARD_OUTLINED,
                selected_icon=ft.icons.DASHBOARD,
                label="Dashboard",
                padding=15,
            ),
            ft.NavigationRailDestination(
                icon=ft.icons.INVENTORY_2_OUTLINED,
                selected_icon=ft.icons.INVENTORY_2,
                label="Inventory",
                padding=15,
            ),
            ft.NavigationRailDestination(
                icon=ft.icons.SCIENCE_OUTLINED,
                selected_icon=ft.icons.SCIENCE,
                label="Experiments",
                padding=15,
            ),
            ft.NavigationRailDestination(
                icon=ft.icons.CALENDAR_MONTH_OUTLINED,
                selected_icon=ft.icons.CALENDAR_MONTH,
                label="Scheduling",
                padding=15,
            ),
            ft.NavigationRailDestination(
                icon=ft.icons.INSERT_CHART_OUTLINED,
                selected_icon=ft.icons.INSERT_CHART,
                label="Reports",
                padding=15,
            ),
            ft.NavigationRailDestination(
                icon=ft.icons.PEOPLE_OUTLINE,
                selected_icon=ft.icons.PEOPLE,
                label="Users",
                padding=15,
            ),
        ],
        on_change=nav_rail_change,
    )
    
    # Create content area
    content_area = ft.Container(
        expand=True,
        content=create_dashboard_content(),
        padding=20,
    )
    
    # Create main layout
    main_layout = ft.Column([
        app_bar,
        ft.Container(height=1, bgcolor=ft.colors.BLUE_200),
        ft.Row([
            nav_rail,
            ft.VerticalDivider(width=1),
            content_area,
        ], expand=True),
    ], expand=True)
    
    page.add(main_layout)
    
    # Show welcome message
    show_notification("Welcome to the Navigation Demo! Click on navigation items to explore.")


if __name__ == "__main__":
    ft.app(target=main, port=8092, view=ft.WEB_BROWSER)
