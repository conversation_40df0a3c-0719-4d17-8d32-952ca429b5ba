import os
import shutil
import logging
import uuid
import json
import hashlib
import mimetypes
import zipfile
import io
import base64
from datetime import datetime
import threading
import queue

# Optional imports - these will be checked before use
try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False

class FileStorage:
    """
    File and document storage system for the Science Laboratory Management System.
    Handles file uploads, downloads, and management.
    """
    
    def __init__(self, config):
        """
        Initialize the file storage system.
        
        Args:
            config: The application configuration
        """
        self.config = config
        self.logger = logging.getLogger("file_storage")
        
        # Get storage configuration
        self.storage_dir = config.get("files", "storage_dir", os.path.join(os.getcwd(), "data", "files"))
        self.max_file_size = config.get("files", "max_file_size", 50) * 1024 * 1024  # Convert MB to bytes
        self.allowed_extensions = config.get("files", "allowed_extensions", "pdf,doc,docx,xls,xlsx,ppt,pptx,txt,csv,jpg,jpeg,png,gif")
        
        if isinstance(self.allowed_extensions, str):
            self.allowed_extensions = self.allowed_extensions.split(',')
        
        # Create storage directories
        self.create_storage_structure()
        
        # Initialize file processing queue
        self.processing_queue = queue.Queue()
        self.processing_thread = None
        self.stop_processing = threading.Event()
        
        # Start processing thread
        self.start_processing_thread()
    
    def create_storage_structure(self):
        """Create the storage directory structure."""
        try:
            # Create main storage directory
            os.makedirs(self.storage_dir, exist_ok=True)
            
            # Create subdirectories for different file types
            subdirs = [
                "documents",
                "images",
                "experiments",
                "reports",
                "safety",
                "temp",
                "backups"
            ]
            
            for subdir in subdirs:
                os.makedirs(os.path.join(self.storage_dir, subdir), exist_ok=True)
            
            self.logger.info(f"Storage directory structure created at {self.storage_dir}")
        
        except Exception as e:
            self.logger.error(f"Error creating storage structure: {str(e)}")
            raise
    
    def start_processing_thread(self):
        """Start the file processing thread."""
        if self.processing_thread is None or not self.processing_thread.is_alive():
            self.stop_processing.clear()
            self.processing_thread = threading.Thread(target=self.process_files, daemon=True)
            self.processing_thread.start()
            self.logger.info("File processing thread started")
    
    def stop_processing_thread(self):
        """Stop the file processing thread."""
        if self.processing_thread and self.processing_thread.is_alive():
            self.stop_processing.set()
            self.processing_thread.join(timeout=5)
            self.logger.info("File processing thread stopped")
    
    def process_files(self):
        """File processing thread function."""
        self.logger.info("File processing thread running")
        
        while not self.stop_processing.is_set():
            try:
                # Get file from queue with timeout
                try:
                    file_data = self.processing_queue.get(timeout=1)
                except queue.Empty:
                    continue
                
                # Process file based on type
                if file_data["action"] == "scan_virus":
                    self.scan_file_for_viruses(file_data["file_path"])
                elif file_data["action"] == "generate_thumbnail":
                    self.generate_thumbnail(file_data["file_path"])
                elif file_data["action"] == "extract_text":
                    self.extract_text_from_file(file_data["file_path"])
                
                # Mark task as done
                self.processing_queue.task_done()
            
            except Exception as e:
                self.logger.error(f"Error in file processing thread: {str(e)}")
        
        self.logger.info("File processing thread exiting")
    
    def save_file(self, file_data, filename=None, directory=None, entity_type=None, entity_id=None, user_id=None, metadata=None):
        """
        Save a file to storage.
        
        Args:
            file_data (bytes or str): File data or path to file
            filename (str, optional): Filename
            directory (str, optional): Subdirectory to save in
            entity_type (str, optional): Type of entity the file is associated with
            entity_id (int, optional): ID of the entity the file is associated with
            user_id (int, optional): ID of the user uploading the file
            metadata (dict, optional): Additional metadata for the file
            
        Returns:
            dict: File information or None if failed
        """
        try:
            # Generate filename if not provided
            if not filename:
                filename = f"file_{uuid.uuid4()}"
            
            # Clean filename
            filename = self.sanitize_filename(filename)
            
            # Check file extension
            ext = os.path.splitext(filename)[1].lower().lstrip('.')
            if ext not in self.allowed_extensions:
                self.logger.warning(f"File extension not allowed: {ext}")
                return None
            
            # Determine directory
            if not directory:
                # Choose directory based on file type
                if ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff']:
                    directory = "images"
                elif ext in ['pdf', 'doc', 'docx', 'txt', 'rtf']:
                    directory = "documents"
                elif entity_type == "experiment":
                    directory = "experiments"
                elif entity_type == "report":
                    directory = "reports"
                elif entity_type == "safety":
                    directory = "safety"
                else:
                    directory = "documents"
            
            # Create full directory path
            dir_path = os.path.join(self.storage_dir, directory)
            os.makedirs(dir_path, exist_ok=True)
            
            # Create entity subdirectory if provided
            if entity_type and entity_id:
                dir_path = os.path.join(dir_path, f"{entity_type}_{entity_id}")
                os.makedirs(dir_path, exist_ok=True)
            
            # Generate unique filename if file already exists
            file_path = os.path.join(dir_path, filename)
            if os.path.exists(file_path):
                base_name, ext = os.path.splitext(filename)
                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                filename = f"{base_name}_{timestamp}{ext}"
                file_path = os.path.join(dir_path, filename)
            
            # Save file
            if isinstance(file_data, bytes):
                # Save bytes directly
                with open(file_path, 'wb') as f:
                    f.write(file_data)
            elif isinstance(file_data, str) and os.path.exists(file_data):
                # Copy existing file
                shutil.copy2(file_data, file_path)
            else:
                self.logger.error("Invalid file data")
                return None
            
            # Get file size
            file_size = os.path.getsize(file_path)
            
            # Calculate file hash
            file_hash = self.calculate_file_hash(file_path)
            
            # Determine MIME type
            mime_type, _ = mimetypes.guess_type(file_path)
            if not mime_type:
                mime_type = "application/octet-stream"
            
            # Create file info
            file_info = {
                "filename": filename,
                "original_filename": filename,
                "path": file_path,
                "relative_path": os.path.relpath(file_path, self.storage_dir),
                "size": file_size,
                "mime_type": mime_type,
                "hash": file_hash,
                "extension": ext,
                "directory": directory,
                "entity_type": entity_type,
                "entity_id": entity_id,
                "user_id": user_id,
                "upload_time": datetime.now().isoformat(),
                "metadata": metadata or {}
            }
            
            # Queue file for processing
            self.processing_queue.put({
                "action": "scan_virus",
                "file_path": file_path
            })
            
            # Generate thumbnail for images
            if ext in ['jpg', 'jpeg', 'png', 'gif']:
                self.processing_queue.put({
                    "action": "generate_thumbnail",
                    "file_path": file_path
                })
            
            # Extract text from documents
            if ext in ['pdf', 'doc', 'docx', 'txt']:
                self.processing_queue.put({
                    "action": "extract_text",
                    "file_path": file_path
                })
            
            self.logger.info(f"File saved: {file_path}")
            
            return file_info
        
        except Exception as e:
            self.logger.error(f"Error saving file: {str(e)}")
            return None
    
    def save_file_from_upload(self, upload_data, entity_type=None, entity_id=None, user_id=None, metadata=None):
        """
        Save a file from upload data.
        
        Args:
            upload_data: Upload data (depends on web framework)
            entity_type (str, optional): Type of entity the file is associated with
            entity_id (int, optional): ID of the entity the file is associated with
            user_id (int, optional): ID of the user uploading the file
            metadata (dict, optional): Additional metadata for the file
            
        Returns:
            dict: File information or None if failed
        """
        try:
            # Extract filename and data from upload
            if hasattr(upload_data, 'filename'):
                # Flet FilePickerUploadFile
                filename = upload_data.name
                file_data = upload_data.data
            elif isinstance(upload_data, dict) and 'name' in upload_data and 'data' in upload_data:
                # Dictionary with name and data
                filename = upload_data['name']
                file_data = upload_data['data']
            elif isinstance(upload_data, tuple) and len(upload_data) >= 2:
                # Tuple of (filename, data)
                filename, file_data = upload_data[:2]
            else:
                self.logger.error("Unsupported upload data format")
                return None
            
            # Save file
            return self.save_file(
                file_data,
                filename=filename,
                entity_type=entity_type,
                entity_id=entity_id,
                user_id=user_id,
                metadata=metadata
            )
        
        except Exception as e:
            self.logger.error(f"Error saving uploaded file: {str(e)}")
            return None
    
    def get_file(self, file_id=None, file_path=None):
        """
        Get file information.
        
        Args:
            file_id (int, optional): File ID in database
            file_path (str, optional): Path to file
            
        Returns:
            dict: File information or None if not found
        """
        try:
            # If file_id is provided, get file path from database
            if file_id is not None:
                # This would be implemented with a database query
                # For this example, we'll just return None
                self.logger.warning("get_file by file_id not implemented")
                return None
            
            # Check if file exists
            if not file_path or not os.path.exists(file_path):
                self.logger.warning(f"File not found: {file_path}")
                return None
            
            # Get file information
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            mime_type, _ = mimetypes.guess_type(file_path)
            if not mime_type:
                mime_type = "application/octet-stream"
            
            ext = os.path.splitext(filename)[1].lower().lstrip('.')
            
            # Calculate file hash
            file_hash = self.calculate_file_hash(file_path)
            
            # Create file info
            file_info = {
                "filename": filename,
                "path": file_path,
                "relative_path": os.path.relpath(file_path, self.storage_dir) if file_path.startswith(self.storage_dir) else file_path,
                "size": file_size,
                "mime_type": mime_type,
                "hash": file_hash,
                "extension": ext,
                "last_modified": datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat()
            }
            
            return file_info
        
        except Exception as e:
            self.logger.error(f"Error getting file info: {str(e)}")
            return None
    
    def read_file(self, file_path, as_bytes=False):
        """
        Read file contents.
        
        Args:
            file_path (str): Path to file
            as_bytes (bool, optional): Return as bytes instead of string
            
        Returns:
            str or bytes: File contents or None if failed
        """
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                self.logger.warning(f"File not found: {file_path}")
                return None
            
            # Read file
            if as_bytes:
                with open(file_path, 'rb') as f:
                    return f.read()
            else:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
        
        except Exception as e:
            self.logger.error(f"Error reading file: {str(e)}")
            return None
    
    def delete_file(self, file_path):
        """
        Delete a file.
        
        Args:
            file_path (str): Path to file
            
        Returns:
            bool: True if deleted, False if failed
        """
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                self.logger.warning(f"File not found: {file_path}")
                return False
            
            # Delete file
            os.remove(file_path)
            self.logger.info(f"File deleted: {file_path}")
            return True
        
        except Exception as e:
            self.logger.error(f"Error deleting file: {str(e)}")
            return False
    
    def calculate_file_hash(self, file_path):
        """
        Calculate SHA-256 hash of a file.
        
        Args:
            file_path (str): Path to file
            
        Returns:
            str: Hex digest of hash
        """
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                # Read and update hash in chunks
                for byte_block in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(byte_block)
            return sha256_hash.hexdigest()
        
        except Exception as e:
            self.logger.error(f"Error calculating file hash: {str(e)}")
            return None
    
    def sanitize_filename(self, filename):
        """
        Sanitize a filename to remove invalid characters.
        
        Args:
            filename (str): Filename to sanitize
            
        Returns:
            str: Sanitized filename
        """
        # Replace invalid characters with underscores
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Limit length
        if len(filename) > 255:
            base, ext = os.path.splitext(filename)
            filename = base[:255-len(ext)] + ext
        
        return filename
    
    def generate_thumbnail(self, file_path, max_size=(200, 200)):
        """
        Generate a thumbnail for an image.
        
        Args:
            file_path (str): Path to image file
            max_size (tuple, optional): Maximum thumbnail size
            
        Returns:
            str: Path to thumbnail or None if failed
        """
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                self.logger.warning(f"File not found: {file_path}")
                return None
            
            # Check if file is an image
            ext = os.path.splitext(file_path)[1].lower().lstrip('.')
            if ext not in ['jpg', 'jpeg', 'png', 'gif']:
                self.logger.warning(f"File is not an image: {file_path}")
                return None
            
            # Try to import PIL
            try:
                from PIL import Image
            except ImportError:
                self.logger.warning("PIL not available, cannot generate thumbnail")
                return None
            
            # Generate thumbnail
            img = Image.open(file_path)
            img.thumbnail(max_size)
            
            # Save thumbnail
            thumb_dir = os.path.join(os.path.dirname(file_path), "thumbnails")
            os.makedirs(thumb_dir, exist_ok=True)
            
            thumb_path = os.path.join(thumb_dir, os.path.basename(file_path))
            img.save(thumb_path)
            
            self.logger.info(f"Thumbnail generated: {thumb_path}")
            return thumb_path
        
        except Exception as e:
            self.logger.error(f"Error generating thumbnail: {str(e)}")
            return None
    
    def scan_file_for_viruses(self, file_path):
        """
        Scan a file for viruses.
        
        Args:
            file_path (str): Path to file
            
        Returns:
            bool: True if clean, False if infected, None if scan failed
        """
        # This is a placeholder for virus scanning
        # In a real application, you would integrate with an antivirus API
        self.logger.info(f"Virus scan placeholder for: {file_path}")
        return True
    
    def extract_text_from_file(self, file_path):
        """
        Extract text from a document file.
        
        Args:
            file_path (str): Path to file
            
        Returns:
            str: Extracted text or None if failed
        """
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                self.logger.warning(f"File not found: {file_path}")
                return None
            
            # Get file extension
            ext = os.path.splitext(file_path)[1].lower().lstrip('.')
            
            # Extract text based on file type
            if ext == 'txt':
                # Plain text file
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    return f.read()
            
            elif ext == 'pdf' and PYPDF2_AVAILABLE:
                # PDF file
                text = ""
                with open(file_path, 'rb') as f:
                    pdf_reader = PyPDF2.PdfReader(f)
                    for page_num in range(len(pdf_reader.pages)):
                        page = pdf_reader.pages[page_num]
                        text += page.extract_text() + "\n"
                return text
            
            else:
                self.logger.warning(f"Text extraction not supported for file type: {ext}")
                return None
        
        except Exception as e:
            self.logger.error(f"Error extracting text: {str(e)}")
            return None
    
    def create_zip_archive(self, files, output_path=None):
        """
        Create a ZIP archive from multiple files.
        
        Args:
            files (list): List of file paths to include
            output_path (str, optional): Path to save ZIP file
            
        Returns:
            str: Path to ZIP file or None if failed
        """
        try:
            # Generate output path if not provided
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                output_path = os.path.join(self.storage_dir, "temp", f"archive_{timestamp}.zip")
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Create ZIP file
            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in files:
                    if os.path.exists(file_path):
                        # Add file to ZIP with just the filename (not full path)
                        zipf.write(file_path, os.path.basename(file_path))
                    else:
                        self.logger.warning(f"File not found, skipping: {file_path}")
            
            self.logger.info(f"ZIP archive created: {output_path}")
            return output_path
        
        except Exception as e:
            self.logger.error(f"Error creating ZIP archive: {str(e)}")
            return None
    
    def get_storage_stats(self):
        """
        Get storage statistics.
        
        Returns:
            dict: Storage statistics
        """
        try:
            stats = {
                "total_size": 0,
                "total_files": 0,
                "by_type": {},
                "by_directory": {}
            }
            
            # Walk through storage directory
            for root, dirs, files in os.walk(self.storage_dir):
                # Skip thumbnails directory
                if "thumbnails" in root:
                    continue
                
                # Get directory name
                dir_name = os.path.basename(root)
                if dir_name not in stats["by_directory"]:
                    stats["by_directory"][dir_name] = {
                        "size": 0,
                        "count": 0
                    }
                
                # Process files
                for file in files:
                    file_path = os.path.join(root, file)
                    file_size = os.path.getsize(file_path)
                    
                    # Update total stats
                    stats["total_size"] += file_size
                    stats["total_files"] += 1
                    
                    # Update directory stats
                    stats["by_directory"][dir_name]["size"] += file_size
                    stats["by_directory"][dir_name]["count"] += 1
                    
                    # Update file type stats
                    ext = os.path.splitext(file)[1].lower().lstrip('.')
                    if not ext:
                        ext = "unknown"
                    
                    if ext not in stats["by_type"]:
                        stats["by_type"][ext] = {
                            "size": 0,
                            "count": 0
                        }
                    
                    stats["by_type"][ext]["size"] += file_size
                    stats["by_type"][ext]["count"] += 1
            
            return stats
        
        except Exception as e:
            self.logger.error(f"Error getting storage stats: {str(e)}")
            return None