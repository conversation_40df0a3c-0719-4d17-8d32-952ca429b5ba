import re

# Read the file
with open('c:/Users/<USER>/Desktop/Science Laboratory Management system/src/integrations/file_storage.py', 'r', encoding='utf-8') as f:
    content = f.read()

# Replace inline PyPDF2 imports with conditional checks
pattern = r"""            elif ext == '\.pdf':
                try:
                    # Try to use PyPDF2
                    import PyPDF2
                    
                    text = ""
                    with open\(file_path, 'rb'\) as f:
                        pdf = PyPDF2\.PdfReader\(f\)
                        for page in pdf\.pages:
                            text \+= page\.extract_text\(\) \+ "\\n"
                    
                    return text
                
                except ImportError:
                    self\.logger\.warning\("PyPDF2 not installed, cannot extract text from PDF"\)
                    return None"""

replacement = """            elif ext == '.pdf':
                if PYPDF2_AVAILABLE:
                    try:
                        text = ""
                        with open(file_path, 'rb') as f:
                            pdf = PyPDF2.PdfReader(f)
                            for page in pdf.pages:
                                text += page.extract_text() + "\\n"
                        
                        return text
                    except Exception as e:
                        self.logger.error(f"Error extracting text from PDF: {str(e)}")
                        return None
                else:
                    self.logger.warning("PyPDF2 not installed, cannot extract text from PDF")
                    return None"""

# Use regex to replace all occurrences
modified_content = re.sub(pattern, replacement, content)

# Write the modified content back to the file
with open('c:/Users/<USER>/Desktop/Science Laboratory Management system/src/integrations/file_storage.py', 'w', encoding='utf-8') as f:
    f.write(modified_content)

print("File updated successfully!")