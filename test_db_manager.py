#!/usr/bin/env python
"""
Test script for the DatabaseManager class.
"""
import os
import sys
import logging

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(project_root, 'db_test.log'))
    ]
)

logger = logging.getLogger('db_test')

# Import the DatabaseManager
from src.database.db_manager import DatabaseManager

def main():
    """
    Test the DatabaseManager class.
    """
    logger.info("Starting database manager test")
    
    # Create a test database
    test_db_path = os.path.join(project_root, 'data', 'test_db.db')
    
    # Remove the test database if it exists
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
        logger.info(f"Removed existing test database: {test_db_path}")
    
    # Create a new database manager with auto_initialize=False
    db = DatabaseManager(db_path=test_db_path, auto_initialize=False)
    
    # Connect to the database
    if not db.connect():
        logger.error("Failed to connect to database")
        return
    
    # Initialize the database
    if db.initialize_database():
        logger.info("Database initialized successfully")
    else:
        logger.error("Failed to initialize database")
        return
    
    # Test user authentication
    username = "admin"
    password = "admin123"
    
    user = db.authenticate_user(username, password)
    if user:
        logger.info(f"User authenticated: {user['username']}")
    else:
        logger.error("User authentication failed")
    
    # Test logging activity
    db.log_activity(user['id'], "login", "User logged in")
    logger.info("Activity logged")
    
    # Test logging audit
    db.log_audit(user['id'], "view", "inventory", None, "Viewed inventory")
    logger.info("Audit logged")
    
    # Get recent activities
    activities = db.get_recent_activities()
    logger.info(f"Recent activities: {len(activities)}")
    
    logger.info("Database manager test completed successfully")

if __name__ == "__main__":
    main()