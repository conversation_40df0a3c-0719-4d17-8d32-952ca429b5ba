import flet as ft
from datetime import datetime
import secrets
import string

class PasswordResetView:
    """
    Password reset view for the Science Laboratory Management System.
    Allows users to request a password reset and set a new password.
    """
    
    def __init__(self, controller):
        """
        Initialize the password reset view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        self.visible = True
        self.current_step = "request"  # "request" or "reset"
        self.reset_token = None
        self.user_id = None
        
        # Create request form controls
        self.email_field = ft.TextField(
            label="Email",
            autofocus=True,
            width=300,
            helper_text="Enter the email address associated with your account",
            keyboard_type=ft.KeyboardType.EMAIL,
        )
        
        self.request_error_text = ft.Text(
            color=ft.colors.RED_500,
            size=12,
            visible=False,
        )
        
        self.request_button = ft.ElevatedButton(
            text="Request Password Reset",
            width=300,
            on_click=self.request_clicked,
        )
        
        # Create reset form controls
        self.token_field = ft.TextField(
            label="Reset Token",
            width=300,
            helper_text="Enter the token sent to your email",
        )
        
        self.new_password_field = ft.TextField(
            label="New Password",
            password=True,
            can_reveal_password=True,
            width=300,
            helper_text="Minimum 8 characters with letters and numbers",
        )
        
        self.confirm_password_field = ft.TextField(
            label="Confirm Password",
            password=True,
            can_reveal_password=True,
            width=300,
        )
        
        self.reset_error_text = ft.Text(
            color=ft.colors.RED_500,
            size=12,
            visible=False,
        )
        
        self.reset_button = ft.ElevatedButton(
            text="Reset Password",
            width=300,
            on_click=self.reset_clicked,
        )
        
        # Create back to login button
        self.login_link = ft.TextButton(
            text="Back to Login",
            on_click=self.login_clicked,
        )
    
    def build(self):
        """
        Build and return the password reset view.
        
        Returns:
            ft.Container: The password reset view container
        """
        if self.current_step == "request":
            content = ft.Column(
                [
                    ft.Container(height=50),
                    ft.Text("Password Reset", size=28, weight=ft.FontWeight.BOLD),
                    ft.Container(height=10),
                    ft.Text("Enter your email to receive a password reset link", size=16),
                    ft.Container(height=30),
                    self.email_field,
                    self.request_error_text,
                    ft.Container(height=10),
                    self.request_button,
                    self.login_link,
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            )
        else:  # reset step
            content = ft.Column(
                [
                    ft.Container(height=50),
                    ft.Text("Reset Your Password", size=28, weight=ft.FontWeight.BOLD),
                    ft.Container(height=10),
                    ft.Text("Enter the token sent to your email and your new password", size=16),
                    ft.Container(height=30),
                    self.token_field,
                    self.new_password_field,
                    self.confirm_password_field,
                    self.reset_error_text,
                    ft.Container(height=10),
                    self.reset_button,
                    self.login_link,
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            )
        
        return ft.Container(
            content=content,
            alignment=ft.alignment.center,
            expand=True,
            visible=self.visible,
        )
    
    def request_clicked(self, _):
        """
        Handle request password reset button click.
        
        Args:
            _: The click event (unused)
        """
        email = self.email_field.value
        
        if not email:
            self.request_error_text.value = "Please enter your email address."
            self.request_error_text.visible = True
            self.controller.page.update()
            return
        
        # Find user with this email
        user = self.controller.db.execute_query(
            "SELECT id, username FROM users WHERE email = ?",
            (email,)
        )
        
        if not user or len(user) == 0:
            self.request_error_text.value = "No account found with this email address."
            self.request_error_text.visible = True
            self.controller.page.update()
            return
        
        # Generate a reset token
        token = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(8))
        
        # Store token in database
        self.controller.db.execute_query(
            "INSERT INTO password_resets (user_id, token, created_at, expires_at) VALUES (?, ?, ?, ?)",
            (
                user[0]["id"],
                token,
                datetime.now().isoformat(),
                (datetime.now() + self.controller.time_utils.timedelta(hours=24)).isoformat()
            )
        )
        
        # In a real application, we would send an email with the token
        # For this example, we'll just show the token on the screen
        self.reset_token = token
        self.user_id = user[0]["id"]
        
        # Show success message with token
        self.controller.page.snack_bar = ft.SnackBar(
            content=ft.Text(f"Reset token: {token}. In a real app, this would be emailed to you."),
            bgcolor=ft.colors.GREEN_400,
            duration=20000,  # Show for 20 seconds
        )
        self.controller.page.snack_bar.open = True
        
        # Switch to reset step
        self.current_step = "reset"
        
        # Update the view
        self.controller.page.update()
    
    def reset_clicked(self, _):
        """
        Handle reset password button click.
        
        Args:
            _: The click event (unused)
        """
        token = self.token_field.value
        new_password = self.new_password_field.value
        confirm_password = self.confirm_password_field.value
        
        # Validate input
        if not token or not new_password or not confirm_password:
            self.reset_error_text.value = "Please fill in all fields."
            self.reset_error_text.visible = True
            self.controller.page.update()
            return
        
        if new_password != confirm_password:
            self.reset_error_text.value = "Passwords do not match."
            self.reset_error_text.visible = True
            self.controller.page.update()
            return
        
        if len(new_password) < 8:
            self.reset_error_text.value = "Password must be at least 8 characters long."
            self.reset_error_text.visible = True
            self.controller.page.update()
            return
        
        if not (any(c.isalpha() for c in new_password) and any(c.isdigit() for c in new_password)):
            self.reset_error_text.value = "Password must contain both letters and numbers."
            self.reset_error_text.visible = True
            self.controller.page.update()
            return
        
        # Verify token
        reset = self.controller.db.execute_query(
            "SELECT user_id FROM password_resets WHERE token = ? AND expires_at > ? LIMIT 1",
            (token, datetime.now().isoformat())
        )
        
        if not reset or len(reset) == 0:
            self.reset_error_text.value = "Invalid or expired reset token."
            self.reset_error_text.visible = True
            self.controller.page.update()
            return
        
        # Update password
        user_id = reset[0]["user_id"]
        hashed_password = self.controller.security.hash_password(new_password)
        
        self.controller.db.execute_query(
            "UPDATE users SET password = ? WHERE id = ?",
            (hashed_password, user_id)
        )
        
        # Delete used token
        self.controller.db.execute_query(
            "DELETE FROM password_resets WHERE token = ?",
            (token,)
        )
        
        # Show success message
        self.controller.page.snack_bar = ft.SnackBar(
            content=ft.Text("Password reset successful. You can now log in with your new password."),
            bgcolor=ft.colors.GREEN_400,
        )
        self.controller.page.snack_bar.open = True
        
        # Navigate to login view
        self.controller.show_login_view()
        
        # Update the page
        self.controller.page.update()
    
    def login_clicked(self, _):
        """
        Handle login link click.
        
        Args:
            _: The click event (unused)
        """
        self.controller.show_login_view()
        # Reset the view state
        self.current_step = "request"