import flet as ft
import json
from datetime import datetime, timedelta
import csv
import os

class ReportsView:
    """
    Reports and analytics view for the Science Laboratory Management System.
    Provides usage reports, audit trails, and data export functionality.
    """
    
    def __init__(self, controller):
        """
        Initialize the reports view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        self.current_tab = "lab_utilization"  # Default tab
        
        # Create tabs
        self.tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            tabs=[
                ft.Tab(
                    text="Lab Utilization",
                    icon=ft.icons.ANALYTICS,
                    content=ft.Container(padding=10),
                ),
                ft.Tab(
                    text="Inventory Consumption",
                    icon=ft.icons.INVENTORY,
                    content=ft.Container(padding=10),
                ),
                ft.Tab(
                    text="Audit Trail",
                    icon=ft.icons.HISTORY,
                    content=ft.Container(padding=10),
                ),
                ft.Tab(
                    text="Safety Compliance",
                    icon=ft.icons.HEALTH_AND_SAFETY,
                    content=ft.Container(padding=10),
                ),
                ft.Tab(
                    text="Data Export",
                    icon=ft.icons.DOWNLOAD,
                    content=ft.Container(padding=10),
                ),
            ],
            on_change=self.tab_changed,
        )
        
        # Date range controls
        self.date_range_dropdown = ft.Dropdown(
            label="Date Range",
            options=[
                ft.dropdown.Option("last_7_days", "Last 7 Days"),
                ft.dropdown.Option("last_30_days", "Last 30 Days"),
                ft.dropdown.Option("last_90_days", "Last 90 Days"),
                ft.dropdown.Option("this_year", "This Year"),
                ft.dropdown.Option("custom", "Custom Range"),
            ],
            value="last_30_days",
            on_change=self.date_range_changed,
            width=200,
        )
        
        self.start_date_picker = ft.TextField(
            label="Start Date (YYYY-MM-DD)",
            value=self.get_default_start_date(),
            width=200,
            visible=False,
        )
        
        self.end_date_picker = ft.TextField(
            label="End Date (YYYY-MM-DD)",
            value=self.get_default_end_date(),
            width=200,
            visible=False,
        )
        
        self.apply_filter_button = ft.ElevatedButton(
            text="Apply Filter",
            icon=ft.icons.FILTER_ALT,
            on_click=self.apply_filter_clicked,
        )
        
        # Lab utilization controls
        self.room_dropdown = ft.Dropdown(
            label="Room",
            options=[
                ft.dropdown.Option("all", "All Rooms"),
            ],
            value="all",
            width=200,
        )
        
        self.utilization_chart = ft.Container(
            content=ft.Text("Loading chart..."),
            height=300,
            border=ft.border.all(1, ft.colors.GREY_300),
            border_radius=5,
            padding=10,
        )
        
        self.utilization_stats = ft.Row(
            [
                self.create_stat_card("Total Hours", "0", ft.icons.ACCESS_TIME),
                self.create_stat_card("Peak Usage", "0%", ft.icons.TRENDING_UP),
                self.create_stat_card("Avg. Daily Usage", "0 hrs", ft.icons.EQUALIZER),
                self.create_stat_card("Most Used Room", "N/A", ft.icons.ROOM),
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            wrap=True,
        )
        
        # Inventory consumption controls
        self.category_dropdown = ft.Dropdown(
            label="Category",
            options=[
                ft.dropdown.Option("all", "All Categories"),
                ft.dropdown.Option("chemicals", "Chemicals"),
                ft.dropdown.Option("consumables", "Consumables"),
                ft.dropdown.Option("equipment", "Equipment"),
                ft.dropdown.Option("safety", "Safety"),
            ],
            value="all",
            width=200,
        )
        
        self.consumption_chart = ft.Container(
            content=ft.Text("Loading chart..."),
            height=300,
            border=ft.border.all(1, ft.colors.GREY_300),
            border_radius=5,
            padding=10,
        )
        
        self.consumption_stats = ft.Row(
            [
                self.create_stat_card("Total Items Used", "0", ft.icons.SHOPPING_CART),
                self.create_stat_card("Most Used Item", "N/A", ft.icons.STAR),
                self.create_stat_card("Low Stock Items", "0", ft.icons.WARNING),
                self.create_stat_card("Restock Value", "$0", ft.icons.ATTACH_MONEY),
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            wrap=True,
        )
        
        # Audit trail controls
        self.user_dropdown = ft.Dropdown(
            label="User",
            options=[
                ft.dropdown.Option("all", "All Users"),
            ],
            value="all",
            width=200,
        )
        
        self.action_dropdown = ft.Dropdown(
            label="Action",
            options=[
                ft.dropdown.Option("all", "All Actions"),
                ft.dropdown.Option("create", "Create"),
                ft.dropdown.Option("update", "Update"),
                ft.dropdown.Option("delete", "Delete"),
                ft.dropdown.Option("login", "Login"),
                ft.dropdown.Option("logout", "Logout"),
            ],
            value="all",
            width=200,
        )
        
        self.entity_dropdown = ft.Dropdown(
            label="Entity Type",
            options=[
                ft.dropdown.Option("all", "All Entities"),
                ft.dropdown.Option("user", "User"),
                ft.dropdown.Option("inventory", "Inventory"),
                ft.dropdown.Option("experiment", "Experiment"),
                ft.dropdown.Option("schedule", "Schedule"),
                ft.dropdown.Option("safety", "Safety"),
            ],
            value="all",
            width=200,
        )
        
        self.audit_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("Timestamp")),
                ft.DataColumn(ft.Text("User")),
                ft.DataColumn(ft.Text("Action")),
                ft.DataColumn(ft.Text("Entity Type")),
                ft.DataColumn(ft.Text("Entity ID")),
                ft.DataColumn(ft.Text("Details")),
            ],
            rows=[],
        )
        
        # Safety compliance controls
        self.compliance_chart = ft.Container(
            content=ft.Text("Loading chart..."),
            height=300,
            border=ft.border.all(1, ft.colors.GREY_300),
            border_radius=5,
            padding=10,
        )
        
        self.compliance_stats = ft.Row(
            [
                self.create_stat_card("Compliance Rate", "0%", ft.icons.VERIFIED),
                self.create_stat_card("Open Incidents", "0", ft.icons.REPORT_PROBLEM),
                self.create_stat_card("Overdue Checks", "0", ft.icons.SCHEDULE),
                self.create_stat_card("Safety Score", "0/100", ft.icons.STARS),
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            wrap=True,
        )
        
        self.compliance_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("Date")),
                ft.DataColumn(ft.Text("Checklist")),
                ft.DataColumn(ft.Text("Completed By")),
                ft.DataColumn(ft.Text("Status")),
                ft.DataColumn(ft.Text("Issues")),
                ft.DataColumn(ft.Text("Actions")),
            ],
            rows=[],
        )
        
        # Data export controls
        self.export_type_dropdown = ft.Dropdown(
            label="Export Type",
            options=[
                ft.dropdown.Option("lab_utilization", "Lab Utilization"),
                ft.dropdown.Option("inventory_consumption", "Inventory Consumption"),
                ft.dropdown.Option("audit_trail", "Audit Trail"),
                ft.dropdown.Option("safety_compliance", "Safety Compliance"),
                ft.dropdown.Option("users", "Users"),
                ft.dropdown.Option("inventory", "Inventory"),
                ft.dropdown.Option("experiments", "Experiments"),
                ft.dropdown.Option("schedules", "Schedules"),
            ],
            value="lab_utilization",
            width=300,
        )
        
        self.export_format_dropdown = ft.Dropdown(
            label="Export Format",
            options=[
                ft.dropdown.Option("csv", "CSV"),
                ft.dropdown.Option("json", "JSON"),
                ft.dropdown.Option("pdf", "PDF"),
            ],
            value="csv",
            width=200,
        )
        
        self.include_headers_checkbox = ft.Checkbox(
            label="Include Headers",
            value=True,
        )
        
        self.export_button = ft.ElevatedButton(
            text="Export Data",
            icon=ft.icons.DOWNLOAD,
            on_click=self.export_data_clicked,
        )
        
        self.export_status_text = ft.Text(
            visible=False,
            color=ft.colors.GREEN,
        )
    
    def build(self):
        """
        Build and return the reports view.
        
        Returns:
            ft.Container: The reports view container
        """
        # Load data for dropdowns
        self.load_dropdown_data()
        
        # Build lab utilization tab
        lab_utilization_content = ft.Column(
            [
                ft.Row(
                    [
                        ft.Text("Lab Utilization Report", size=20, weight=ft.FontWeight.BOLD),
                        ft.Row(
                            [
                                self.date_range_dropdown,
                                self.start_date_picker,
                                self.end_date_picker,
                                self.apply_filter_button,
                            ],
                            alignment=ft.MainAxisAlignment.END,
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Row(
                    [
                        self.room_dropdown,
                    ],
                ),
                ft.Container(height=10),
                self.utilization_stats,
                ft.Container(height=20),
                ft.Text("Utilization Over Time", size=16, weight=ft.FontWeight.BOLD),
                self.utilization_chart,
            ],
            spacing=10,
            scroll=ft.ScrollMode.AUTO,
        )
        
        # Build inventory consumption tab
        inventory_consumption_content = ft.Column(
            [
                ft.Row(
                    [
                        ft.Text("Inventory Consumption Report", size=20, weight=ft.FontWeight.BOLD),
                        ft.Row(
                            [
                                self.date_range_dropdown,
                                self.start_date_picker,
                                self.end_date_picker,
                                self.apply_filter_button,
                            ],
                            alignment=ft.MainAxisAlignment.END,
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Row(
                    [
                        self.category_dropdown,
                    ],
                ),
                ft.Container(height=10),
                self.consumption_stats,
                ft.Container(height=20),
                ft.Text("Consumption Over Time", size=16, weight=ft.FontWeight.BOLD),
                self.consumption_chart,
            ],
            spacing=10,
            scroll=ft.ScrollMode.AUTO,
        )
        
        # Build audit trail tab
        audit_trail_content = ft.Column(
            [
                ft.Row(
                    [
                        ft.Text("Audit Trail", size=20, weight=ft.FontWeight.BOLD),
                        ft.Row(
                            [
                                self.date_range_dropdown,
                                self.start_date_picker,
                                self.end_date_picker,
                                self.apply_filter_button,
                            ],
                            alignment=ft.MainAxisAlignment.END,
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Row(
                    [
                        self.user_dropdown,
                        self.action_dropdown,
                        self.entity_dropdown,
                    ],
                    alignment=ft.MainAxisAlignment.START,
                ),
                ft.Container(height=10),
                ft.Container(
                    content=self.audit_table,
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                    padding=10,
                    expand=True,
                ),
            ],
            spacing=10,
            scroll=ft.ScrollMode.AUTO,
            expand=True,
        )
        
        # Build safety compliance tab
        safety_compliance_content = ft.Column(
            [
                ft.Row(
                    [
                        ft.Text("Safety Compliance Report", size=20, weight=ft.FontWeight.BOLD),
                        ft.Row(
                            [
                                self.date_range_dropdown,
                                self.start_date_picker,
                                self.end_date_picker,
                                self.apply_filter_button,
                            ],
                            alignment=ft.MainAxisAlignment.END,
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Container(height=10),
                self.compliance_stats,
                ft.Container(height=20),
                ft.Text("Compliance Trend", size=16, weight=ft.FontWeight.BOLD),
                self.compliance_chart,
                ft.Container(height=20),
                ft.Text("Recent Safety Checklists", size=16, weight=ft.FontWeight.BOLD),
                ft.Container(
                    content=self.compliance_table,
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                    padding=10,
                    height=300,
                ),
            ],
            spacing=10,
            scroll=ft.ScrollMode.AUTO,
        )
        
        # Build data export tab
        data_export_content = ft.Column(
            [
                ft.Text("Data Export", size=20, weight=ft.FontWeight.BOLD),
                ft.Container(height=10),
                ft.Card(
                    content=ft.Container(
                        content=ft.Column(
                            [
                                ft.Text("Export Settings", size=16, weight=ft.FontWeight.BOLD),
                                ft.Divider(),
                                self.export_type_dropdown,
                                ft.Row(
                                    [
                                        self.export_format_dropdown,
                                        self.include_headers_checkbox,
                                    ],
                                    alignment=ft.MainAxisAlignment.START,
                                ),
                                ft.Row(
                                    [
                                        self.date_range_dropdown,
                                        self.start_date_picker,
                                        self.end_date_picker,
                                    ],
                                    alignment=ft.MainAxisAlignment.START,
                                ),
                                ft.Container(height=10),
                                ft.Row(
                                    [
                                        self.export_button,
                                        self.export_status_text,
                                    ],
                                    alignment=ft.MainAxisAlignment.START,
                                ),
                            ],
                            spacing=10,
                        ),
                        padding=20,
                    ),
                ),
                ft.Container(height=20),
                ft.Text("Export History", size=16, weight=ft.FontWeight.BOLD),
                ft.Container(
                    content=ft.DataTable(
                        columns=[
                            ft.DataColumn(ft.Text("Date")),
                            ft.DataColumn(ft.Text("Type")),
                            ft.DataColumn(ft.Text("Format")),
                            ft.DataColumn(ft.Text("User")),
                            ft.DataColumn(ft.Text("File")),
                            ft.DataColumn(ft.Text("Actions")),
                        ],
                        rows=[
                            ft.DataRow(
                                cells=[
                                    ft.DataCell(ft.Text(datetime.now().strftime("%Y-%m-%d %H:%M"))),
                                    ft.DataCell(ft.Text("Lab Utilization")),
                                    ft.DataCell(ft.Text("CSV")),
                                    ft.DataCell(ft.Text("Admin")),
                                    ft.DataCell(ft.Text("lab_utilization_20230601.csv")),
                                    ft.DataCell(
                                        ft.Row(
                                            [
                                                ft.IconButton(
                                                    icon=ft.icons.DOWNLOAD,
                                                    tooltip="Download",
                                                    on_click=lambda e: print("Download clicked"),
                                                ),
                                                ft.IconButton(
                                                    icon=ft.icons.DELETE,
                                                    tooltip="Delete",
                                                    on_click=lambda e: print("Delete clicked"),
                                                ),
                                            ]
                                        )
                                    ),
                                ]
                            ),
                        ],
                    ),
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                    padding=10,
                ),
            ],
            spacing=10,
            scroll=ft.ScrollMode.AUTO,
        )
        
        # Set tab contents
        self.tabs.tabs[0].content = lab_utilization_content
        self.tabs.tabs[1].content = inventory_consumption_content
        self.tabs.tabs[2].content = audit_trail_content
        self.tabs.tabs[3].content = safety_compliance_content
        self.tabs.tabs[4].content = data_export_content
        
        # Load initial data
        self.load_report_data()
        
        # Return the complete view
        return ft.Container(
            content=ft.Column(
                [
                    ft.Text("Reports & Analytics", size=24, weight=ft.FontWeight.BOLD),
                    ft.Container(height=10),
                    self.tabs,
                ],
                spacing=10,
                expand=True,
            ),
            padding=20,
            expand=True,
        )
    
    def load_dropdown_data(self):
        """Load data for dropdown menus."""
        # Load rooms for room dropdown
        rooms = self.controller.db.execute_query(
            "SELECT id, room_number, name FROM lab_rooms ORDER BY room_number"
        )
        
        self.room_dropdown.options = [ft.dropdown.Option("all", "All Rooms")]
        for room in rooms:
            self.room_dropdown.options.append(
                ft.dropdown.Option(str(room["id"]), f"{room['room_number']} - {room['name']}")
            )
        
        # Load users for user dropdown
        users = self.controller.db.execute_query(
            "SELECT id, username, full_name FROM users ORDER BY username"
        )
        
        self.user_dropdown.options = [ft.dropdown.Option("all", "All Users")]
        for user in users:
            self.user_dropdown.options.append(
                ft.dropdown.Option(str(user["id"]), f"{user['username']} ({user['full_name']})")
            )
    
    def load_report_data(self):
        """Load report data based on current tab and filters."""
        if self.current_tab == "lab_utilization":
            self.load_lab_utilization_data()
        elif self.current_tab == "inventory_consumption":
            self.load_inventory_consumption_data()
        elif self.current_tab == "audit_trail":
            self.load_audit_trail_data()
        elif self.current_tab == "safety_compliance":
            self.load_safety_compliance_data()
    
    def load_lab_utilization_data(self):
        """Load lab utilization report data."""
        # Get date range
        start_date, end_date = self.get_date_range()
        
        # Get room filter
        room_id = self.room_dropdown.value
        
        # Build query
        if room_id == "all":
            # Query for all rooms
            query = """
                SELECT 
                    s.start_time, 
                    s.end_time, 
                    s.room_id,
                    r.room_number,
                    r.name as room_name,
                    u.username,
                    u.full_name
                FROM 
                    lab_schedules s
                    JOIN lab_rooms r ON s.room_id = r.id
                    JOIN users u ON s.user_id = u.id
                WHERE 
                    s.start_time >= ? AND s.end_time <= ?
                ORDER BY 
                    s.start_time
            """
            params = (start_date, end_date)
        else:
            # Query for specific room
            query = """
                SELECT 
                    s.start_time, 
                    s.end_time, 
                    s.room_id,
                    r.room_number,
                    r.name as room_name,
                    u.username,
                    u.full_name
                FROM 
                    lab_schedules s
                    JOIN lab_rooms r ON s.room_id = r.id
                    JOIN users u ON s.user_id = u.id
                WHERE 
                    s.start_time >= ? AND s.end_time <= ? AND s.room_id = ?
                ORDER BY 
                    s.start_time
            """
            params = (start_date, end_date, int(room_id))
        
        # Execute query
        schedules = self.controller.db.execute_query(query, params)
        
        # Calculate statistics
        total_hours = 0
        room_usage = {}
        daily_usage = {}
        peak_usage = 0
        
        for schedule in schedules:
            # Calculate duration in hours
            start = datetime.fromisoformat(schedule["start_time"].replace('Z', '+00:00'))
            end = datetime.fromisoformat(schedule["end_time"].replace('Z', '+00:00'))
            duration = (end - start).total_seconds() / 3600  # Convert to hours
            
            # Add to total hours
            total_hours += duration
            
            # Add to room usage
            room_key = f"{schedule['room_number']} - {schedule['room_name']}"
            if room_key in room_usage:
                room_usage[room_key] += duration
            else:
                room_usage[room_key] = duration
            
            # Add to daily usage
            day = start.date().isoformat()
            if day in daily_usage:
                daily_usage[day] += duration
            else:
                daily_usage[day] = duration
        
        # Find most used room
        most_used_room = "N/A"
        if room_usage:
            most_used_room = max(room_usage.items(), key=lambda x: x[1])[0]
        
        # Calculate peak usage
        if daily_usage:
            peak_usage = max(daily_usage.values())
        
        # Calculate average daily usage
        avg_daily_usage = 0
        if daily_usage:
            avg_daily_usage = sum(daily_usage.values()) / len(daily_usage)
        
        # Update statistics
        self.utilization_stats.controls[0].content.controls[1].value = f"{total_hours:.1f}"
        self.utilization_stats.controls[1].content.controls[1].value = f"{peak_usage:.1f} hrs"
        self.utilization_stats.controls[2].content.controls[1].value = f"{avg_daily_usage:.1f} hrs"
        self.utilization_stats.controls[3].content.controls[1].value = most_used_room
        
        # Update chart (placeholder for now)
        self.utilization_chart.content = ft.Text(
            f"Chart would show utilization data for {len(schedules)} schedules from {start_date} to {end_date}",
            color=ft.colors.BLUE,
        )
        
        # Update the page
        self.controller.page.update()
    
    def load_inventory_consumption_data(self):
        """Load inventory consumption report data."""
        # Get date range
        start_date, end_date = self.get_date_range()
        
        # Get category filter
        category = self.category_dropdown.value
        
        # Build query
        if category == "all":
            # Query for all categories
            query = """
                SELECT 
                    t.timestamp,
                    t.item_id,
                    t.quantity,
                    i.name as item_name,
                    i.category,
                    i.unit,
                    i.price_per_unit
                FROM 
                    inventory_transactions t
                    JOIN inventory_items i ON t.item_id = i.id
                WHERE 
                    t.timestamp >= ? AND t.timestamp <= ? AND t.transaction_type = 'consumption'
                ORDER BY 
                    t.timestamp
            """
            params = (start_date, end_date)
        else:
            # Query for specific category
            query = """
                SELECT 
                    t.timestamp,
                    t.item_id,
                    t.quantity,
                    i.name as item_name,
                    i.category,
                    i.unit,
                    i.price_per_unit
                FROM 
                    inventory_transactions t
                    JOIN inventory_items i ON t.item_id = i.id
                WHERE 
                    t.timestamp >= ? AND t.timestamp <= ? AND t.transaction_type = 'consumption' AND i.category = ?
                ORDER BY 
                    t.timestamp
            """
            params = (start_date, end_date, category)
        
        # Execute query
        transactions = self.controller.db.execute_query(query, params)
        
        # Calculate statistics
        total_items = 0
        item_usage = {}
        total_value = 0
        
        for transaction in transactions:
            # Add to total items
            total_items += transaction["quantity"]
            
            # Add to item usage
            if transaction["item_name"] in item_usage:
                item_usage[transaction["item_name"]] += transaction["quantity"]
            else:
                item_usage[transaction["item_name"]] = transaction["quantity"]
            
            # Add to total value
            total_value += transaction["quantity"] * transaction["price_per_unit"]
        
        # Find most used item
        most_used_item = "N/A"
        if item_usage:
            most_used_item = max(item_usage.items(), key=lambda x: x[1])[0]
        
        # Get low stock items
        low_stock_query = """
            SELECT COUNT(*) as count
            FROM inventory_items
            WHERE current_quantity <= reorder_level
        """
        low_stock_result = self.controller.db.execute_query(low_stock_query)
        low_stock_count = low_stock_result[0]["count"] if low_stock_result else 0
        
        # Update statistics
        self.consumption_stats.controls[0].content.controls[1].value = str(total_items)
        self.consumption_stats.controls[1].content.controls[1].value = most_used_item
        self.consumption_stats.controls[2].content.controls[1].value = str(low_stock_count)
        self.consumption_stats.controls[3].content.controls[1].value = f"${total_value:.2f}"
        
        # Update chart (placeholder for now)
        self.consumption_chart.content = ft.Text(
            f"Chart would show consumption data for {len(transactions)} transactions from {start_date} to {end_date}",
            color=ft.colors.BLUE,
        )
        
        # Update the page
        self.controller.page.update()
    
    def load_audit_trail_data(self):
        """Load audit trail data."""
        # Get date range
        start_date, end_date = self.get_date_range()
        
        # Get filters
        user_id = self.user_dropdown.value
        action = self.action_dropdown.value
        entity_type = self.entity_dropdown.value
        
        # Build query parts
        query_parts = ["timestamp >= ? AND timestamp <= ?"]
        params = [start_date, end_date]
        
        if user_id != "all":
            query_parts.append("user_id = ?")
            params.append(int(user_id))
        
        if action != "all":
            query_parts.append("action = ?")
            params.append(action)
        
        if entity_type != "all":
            query_parts.append("entity_type = ?")
            params.append(entity_type)
        
        # Build the complete query
        query = f"""
            SELECT 
                a.id,
                a.timestamp,
                a.user_id,
                a.action,
                a.entity_type,
                a.entity_id,
                a.details,
                u.username,
                u.full_name
            FROM 
                audit_logs a
                JOIN users u ON a.user_id = u.id
            WHERE 
                {" AND ".join(query_parts)}
            ORDER BY 
                a.timestamp DESC
            LIMIT 100
        """
        
        # Execute query
        audit_logs = self.controller.db.execute_query(query, tuple(params))
        
        # Clear existing rows
        self.audit_table.rows.clear()
        
        # Add rows for audit logs
        for log in audit_logs:
            # Format timestamp
            timestamp = self.format_timestamp(log["timestamp"])
            
            # Format action and entity type
            action = log["action"].capitalize()
            entity_type = log["entity_type"].replace("_", " ").capitalize()
            
            # Create row
            self.audit_table.rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(timestamp)),
                        ft.DataCell(ft.Text(f"{log['username']} ({log['full_name']})")),
                        ft.DataCell(ft.Text(action)),
                        ft.DataCell(ft.Text(entity_type)),
                        ft.DataCell(ft.Text(str(log["entity_id"]))),
                        ft.DataCell(ft.Text(log["details"])),
                    ]
                )
            )
        
        # Update the page
        self.controller.page.update()
    
    def load_safety_compliance_data(self):
        """Load safety compliance report data."""
        # Get date range
        start_date, end_date = self.get_date_range()
        
        # Query safety checklists
        checklists_query = """
            SELECT 
                c.id,
                c.date_performed,
                c.checklist_type,
                c.status,
                c.issues_found,
                c.completed_by_id,
                u.username,
                u.full_name
            FROM 
                safety_checklists c
                JOIN users u ON c.completed_by_id = u.id
            WHERE 
                c.date_performed >= ? AND c.date_performed <= ?
            ORDER BY 
                c.date_performed DESC
            LIMIT 20
        """
        
        checklists = self.controller.db.execute_query(checklists_query, (start_date, end_date))
        
        # Query safety incidents
        incidents_query = """
            SELECT COUNT(*) as count
            FROM safety_incidents
            WHERE date_reported >= ? AND date_reported <= ?
        """
        
        incidents_result = self.controller.db.execute_query(incidents_query, (start_date, end_date))
        incident_count = incidents_result[0]["count"] if incidents_result else 0
        
        # Calculate statistics
        total_checklists = len(checklists)
        compliant_checklists = sum(1 for c in checklists if c["status"] == "compliant")
        compliance_rate = (compliant_checklists / total_checklists * 100) if total_checklists > 0 else 0
        
        # Count overdue checks
        overdue_query = """
            SELECT COUNT(*) as count
            FROM safety_checklists
            WHERE due_date < ? AND status != 'completed'
        """
        
        overdue_result = self.controller.db.execute_query(overdue_query, (datetime.now().isoformat(),))
        overdue_count = overdue_result[0]["count"] if overdue_result else 0
        
        # Calculate safety score (simplified)
        safety_score = int(compliance_rate - (incident_count * 5) - (overdue_count * 2))
        safety_score = max(0, min(100, safety_score))  # Clamp between 0 and 100
        
        # Update statistics
        self.compliance_stats.controls[0].content.controls[1].value = f"{compliance_rate:.1f}%"
        self.compliance_stats.controls[1].content.controls[1].value = str(incident_count)
        self.compliance_stats.controls[2].content.controls[1].value = str(overdue_count)
        self.compliance_stats.controls[3].content.controls[1].value = f"{safety_score}/100"
        
        # Update chart (placeholder for now)
        self.compliance_chart.content = ft.Text(
            f"Chart would show compliance trend for {total_checklists} checklists from {start_date} to {end_date}",
            color=ft.colors.BLUE,
        )
        
        # Clear existing rows in compliance table
        self.compliance_table.rows.clear()
        
        # Add rows for checklists
        for checklist in checklists:
            # Format date
            date = self.format_date(checklist["date_performed"])
            
            # Format checklist type
            checklist_type = checklist["checklist_type"].replace("_", " ").capitalize()
            
            # Get status color
            status_color = self.get_status_color(checklist["status"])
            
            # Create row
            self.compliance_table.rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(date)),
                        ft.DataCell(ft.Text(checklist_type)),
                        ft.DataCell(ft.Text(f"{checklist['username']} ({checklist['full_name']})")),
                        ft.DataCell(ft.Text(checklist["status"].capitalize(), color=status_color)),
                        ft.DataCell(ft.Text(str(checklist["issues_found"] or 0))),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.icons.VISIBILITY,
                                        tooltip="View Details",
                                        on_click=lambda e, c_id=checklist["id"]: self.view_checklist_details(e, c_id),
                                    ),
                                ]
                            )
                        ),
                    ]
                )
            )
        
        # Update the page
        self.controller.page.update()
    
    def tab_changed(self, e):
        """
        Handle tab change.
        
        Args:
            e: The change event
        """
        tab_index = e.control.selected_index
        tab_names = ["lab_utilization", "inventory_consumption", "audit_trail", "safety_compliance", "data_export"]
        self.current_tab = tab_names[tab_index]
        
        # Load data for the selected tab
        if self.current_tab != "data_export":
            self.load_report_data()
    
    def date_range_changed(self, e):
        """
        Handle date range dropdown change.
        
        Args:
            e: The change event
        """
        # Show/hide custom date fields
        if self.date_range_dropdown.value == "custom":
            self.start_date_picker.visible = True
            self.end_date_picker.visible = True
        else:
            self.start_date_picker.visible = False
            self.end_date_picker.visible = False
            
            # Set default dates based on selection
            start_date, end_date = self.get_date_range()
            self.start_date_picker.value = start_date
            self.end_date_picker.value = end_date
        
        # Update the page
        self.controller.page.update()
    
    def apply_filter_clicked(self, e):
        """
        Handle apply filter button click.
        
        Args:
            e: The click event
        """
        # Load data with new filters
        self.load_report_data()
    
    def export_data_clicked(self, e):
        """
        Handle export data button click.
        
        Args:
            e: The click event
        """
        # Get export settings
        export_type = self.export_type_dropdown.value
        export_format = self.export_format_dropdown.value
        include_headers = self.include_headers_checkbox.value
        start_date, end_date = self.get_date_range()
        
        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{export_type}_{timestamp}.{export_format}"
        
        # Export data based on type
        if export_format == "csv":
            self.export_to_csv(export_type, filename, include_headers, start_date, end_date)
        elif export_format == "json":
            self.export_to_json(export_type, filename, start_date, end_date)
        elif export_format == "pdf":
            # PDF export would be implemented in a real application
            self.export_status_text.value = "PDF export not implemented in this example"
            self.export_status_text.color = ft.colors.ORANGE
            self.export_status_text.visible = True
        
        # Update the page
        self.controller.page.update()
    
    def export_to_csv(self, export_type, filename, include_headers, start_date, end_date):
        """
        Export data to CSV file.
        
        Args:
            export_type (str): Type of data to export
            filename (str): Output filename
            include_headers (bool): Whether to include headers
            start_date (str): Start date for filtering
            end_date (str): End date for filtering
        """
        # Get data based on export type
        if export_type == "lab_utilization":
            # Query for lab utilization
            query = """
                SELECT 
                    s.start_time, 
                    s.end_time, 
                    r.room_number,
                    r.name as room_name,
                    u.username,
                    u.full_name,
                    s.title,
                    s.description
                FROM 
                    lab_schedules s
                    JOIN lab_rooms r ON s.room_id = r.id
                    JOIN users u ON s.user_id = u.id
                WHERE 
                    s.start_time >= ? AND s.end_time <= ?
                ORDER BY 
                    s.start_time
            """
            data = self.controller.db.execute_query(query, (start_date, end_date))
            
            # Define headers
            headers = ["Start Time", "End Time", "Room Number", "Room Name", "Username", "Full Name", "Title", "Description"]
        
        elif export_type == "audit_trail":
            # Query for audit trail
            query = """
                SELECT 
                    a.timestamp,
                    u.username,
                    u.full_name,
                    a.action,
                    a.entity_type,
                    a.entity_id,
                    a.details
                FROM 
                    audit_logs a
                    JOIN users u ON a.user_id = u.id
                WHERE 
                    a.timestamp >= ? AND a.timestamp <= ?
                ORDER BY 
                    a.timestamp DESC
            """
            data = self.controller.db.execute_query(query, (start_date, end_date))
            
            # Define headers
            headers = ["Timestamp", "Username", "Full Name", "Action", "Entity Type", "Entity ID", "Details"]
        
        else:
            # For other export types, we would implement similar logic
            # For this example, we'll just show a message
            self.export_status_text.value = f"Export for {export_type} not fully implemented in this example"
            self.export_status_text.color = ft.colors.ORANGE
            self.export_status_text.visible = True
            return
        
        try:
            # Create export directory if it doesn't exist
            export_dir = os.path.join(os.getcwd(), "exports")
            os.makedirs(export_dir, exist_ok=True)
            
            # Write to CSV file
            filepath = os.path.join(export_dir, filename)
            with open(filepath, 'w', newline='') as csvfile:
                writer = csv.writer(csvfile)
                
                # Write headers if requested
                if include_headers:
                    writer.writerow(headers)
                
                # Write data rows
                for row in data:
                    writer.writerow(row.values())
            
            # Show success message
            self.export_status_text.value = f"Data exported successfully to {filepath}"
            self.export_status_text.color = ft.colors.GREEN
            self.export_status_text.visible = True
            
            # Log the export
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "export",
                "report",
                None,
                f"Exported {export_type} data to CSV"
            )
        except Exception as e:
            # Show error message
            self.export_status_text.value = f"Error exporting data: {str(e)}"
            self.export_status_text.color = ft.colors.RED
            self.export_status_text.visible = True
    
    def export_to_json(self, export_type, filename, start_date, end_date):
        """
        Export data to JSON file.
        
        Args:
            export_type (str): Type of data to export
            filename (str): Output filename
            start_date (str): Start date for filtering
            end_date (str): End date for filtering
        """
        # Get data based on export type (similar to CSV export)
        if export_type == "lab_utilization":
            # Query for lab utilization
            query = """
                SELECT 
                    s.start_time, 
                    s.end_time, 
                    r.room_number,
                    r.name as room_name,
                    u.username,
                    u.full_name,
                    s.title,
                    s.description
                FROM 
                    lab_schedules s
                    JOIN lab_rooms r ON s.room_id = r.id
                    JOIN users u ON s.user_id = u.id
                WHERE 
                    s.start_time >= ? AND s.end_time <= ?
                ORDER BY 
                    s.start_time
            """
            data = self.controller.db.execute_query(query, (start_date, end_date))
        
        elif export_type == "audit_trail":
            # Query for audit trail
            query = """
                SELECT 
                    a.timestamp,
                    u.username,
                    u.full_name,
                    a.action,
                    a.entity_type,
                    a.entity_id,
                    a.details
                FROM 
                    audit_logs a
                    JOIN users u ON a.user_id = u.id
                WHERE 
                    a.timestamp >= ? AND a.timestamp <= ?
                ORDER BY 
                    a.timestamp DESC
            """
            data = self.controller.db.execute_query(query, (start_date, end_date))
        
        else:
            # For other export types, we would implement similar logic
            # For this example, we'll just show a message
            self.export_status_text.value = f"Export for {export_type} not fully implemented in this example"
            self.export_status_text.color = ft.colors.ORANGE
            self.export_status_text.visible = True
            return
        
        try:
            # Create export directory if it doesn't exist
            export_dir = os.path.join(os.getcwd(), "exports")
            os.makedirs(export_dir, exist_ok=True)
            
            # Write to JSON file
            filepath = os.path.join(export_dir, filename)
            with open(filepath, 'w') as jsonfile:
                json.dump(data, jsonfile, indent=2, default=str)
            
            # Show success message
            self.export_status_text.value = f"Data exported successfully to {filepath}"
            self.export_status_text.color = ft.colors.GREEN
            self.export_status_text.visible = True
            
            # Log the export
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "export",
                "report",
                None,
                f"Exported {export_type} data to JSON"
            )
        except Exception as e:
            # Show error message
            self.export_status_text.value = f"Error exporting data: {str(e)}"
            self.export_status_text.color = ft.colors.RED
            self.export_status_text.visible = True
    
    def view_checklist_details(self, e, checklist_id):
        """
        Show dialog with checklist details.
        
        Args:
            e: The click event
            checklist_id: ID of the checklist to view
        """
        # Query checklist details
        checklist_query = """
            SELECT 
                c.*,
                u.username,
                u.full_name
            FROM 
                safety_checklists c
                JOIN users u ON c.completed_by_id = u.id
            WHERE 
                c.id = ?
        """
        
        checklist = self.controller.db.execute_query(checklist_query, (checklist_id,))
        
        if not checklist or len(checklist) == 0:
            return
        
        checklist = checklist[0]
        
        # Query checklist items
        items_query = """
            SELECT * FROM safety_checklist_items WHERE checklist_id = ?
        """
        
        items = self.controller.db.execute_query(items_query, (checklist_id,))
        
        # Create items list
        items_list = ft.ListView(
            spacing=2,
            padding=10,
            auto_scroll=True,
            height=300,
        )
        
        for item in items:
            # Get status color
            status_color = ft.colors.GREEN if item["status"] == "pass" else ft.colors.RED
            
            # Create item
            items_list.controls.append(
                ft.Container(
                    content=ft.Row(
                        [
                            ft.Icon(
                                ft.icons.CHECK_CIRCLE if item["status"] == "pass" else ft.icons.CANCEL,
                                color=status_color,
                            ),
                            ft.Text(item["description"], expand=True),
                            ft.Text(item["notes"] or ""),
                        ],
                        alignment=ft.MainAxisAlignment.START,
                    ),
                    padding=5,
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                    margin=ft.margin.only(bottom=5),
                )
            )
        
        # Create dialog
        dialog = ft.AlertDialog(
            title=ft.Text(f"Safety Checklist: {checklist['checklist_type'].replace('_', ' ').capitalize()}"),
            content=ft.Column(
                [
                    ft.Text(f"Date: {self.format_date(checklist['date_performed'])}"),
                    ft.Text(f"Completed by: {checklist['username']} ({checklist['full_name']})"),
                    ft.Text(f"Status: {checklist['status'].capitalize()}", color=self.get_status_color(checklist['status'])),
                    ft.Text(f"Issues found: {checklist['issues_found'] or 0}"),
                    ft.Divider(),
                    ft.Text("Checklist Items:", weight=ft.FontWeight.BOLD),
                    items_list,
                    ft.Divider(),
                    ft.Text("Notes:", weight=ft.FontWeight.BOLD),
                    ft.Container(
                        content=ft.Text(checklist["notes"] or "No notes"),
                        border=ft.border.all(1, ft.colors.GREY_300),
                        border_radius=5,
                        padding=10,
                        width=400,
                    ),
                ],
                scroll=ft.ScrollMode.AUTO,
                width=500,
                height=500,
            ),
            actions=[
                ft.TextButton("Close", on_click=lambda e: setattr(dialog, "open", False)),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = dialog
        dialog.open = True
        self.controller.page.update()
    
    def get_date_range(self):
        """
        Get start and end dates based on selected date range.
        
        Returns:
            tuple: (start_date, end_date)
        """
        if self.date_range_dropdown.value == "custom":
            return (self.start_date_picker.value, self.end_date_picker.value)
        
        end_date = datetime.now().isoformat()
        
        if self.date_range_dropdown.value == "last_7_days":
            start_date = (datetime.now() - timedelta(days=7)).isoformat()
        elif self.date_range_dropdown.value == "last_30_days":
            start_date = (datetime.now() - timedelta(days=30)).isoformat()
        elif self.date_range_dropdown.value == "last_90_days":
            start_date = (datetime.now() - timedelta(days=90)).isoformat()
        elif self.date_range_dropdown.value == "this_year":
            start_date = datetime(datetime.now().year, 1, 1).isoformat()
        else:
            # Default to last 30 days
            start_date = (datetime.now() - timedelta(days=30)).isoformat()
        
        return (start_date, end_date)
    
    def get_default_start_date(self):
        """
        Get default start date (30 days ago).
        
        Returns:
            str: Default start date
        """
        return (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
    
    def get_default_end_date(self):
        """
        Get default end date (today).
        
        Returns:
            str: Default end date
        """
        return datetime.now().strftime("%Y-%m-%d")
    
    def format_timestamp(self, timestamp):
        """
        Format a timestamp for display.
        
        Args:
            timestamp (str): The timestamp to format
            
        Returns:
            str: Formatted timestamp
        """
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return timestamp
    
    def format_date(self, date_str):
        """
        Format a date string for display.
        
        Args:
            date_str (str): The date string to format
            
        Returns:
            str: Formatted date
        """
        try:
            dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d")
        except:
            return date_str
    
    def get_status_color(self, status):
        """
        Get color for a status.
        
        Args:
            status (str): The status
            
        Returns:
            str: Color for the status
        """
        if status in ["compliant", "pass", "completed", "active"]:
            return ft.colors.GREEN
        elif status in ["non_compliant", "fail", "overdue"]:
            return ft.colors.RED
        elif status in ["partial", "in_progress", "pending"]:
            return ft.colors.ORANGE
        else:
            return ft.colors.BLACK
    
    def create_stat_card(self, title, value, icon):
        """
        Create a statistics card.
        
        Args:
            title (str): Card title
            value (str): Statistic value
            icon (str): Icon to display
            
        Returns:
            ft.Card: The statistics card
        """
        return ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Row(
                            [
                                ft.Icon(icon, color=ft.colors.BLUE),
                                ft.Text(title, weight=ft.FontWeight.BOLD),
                            ],
                            alignment=ft.MainAxisAlignment.START,
                        ),
                        ft.Text(
                            value,
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.colors.BLUE,
                        ),
                    ],
                    spacing=5,
                    horizontal_alignment=ft.CrossAxisAlignment.START,
                ),
                width=200,
                padding=15,
            ),
        )