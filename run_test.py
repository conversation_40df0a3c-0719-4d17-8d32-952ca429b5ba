#!/usr/bin/env python
"""
Run script for testing the Integration Manager.
This script ensures the proper Python path is set up.
"""
import sys
import os

# Add the project root directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Now we can import using the project structure
from src.integrations.integration_manager import IntegrationManager

# Example usage
if __name__ == "__main__":
    # Create a simple configuration for testing
    config = {
        "email": {
            "enabled": False
        }
    }
    
    try:
        # Initialize the integration manager
        print("Initializing IntegrationManager...")
        manager = IntegrationManager(config)
        
        # Get and print the component status
        print("Getting component status...")
        status = manager.get_component_status()
        print("Integration Components Status:")
        print(status)
        
        # Shutdown the integration manager
        print("Shutting down IntegrationManager...")
        manager.shutdown()
        
        print("Test completed successfully!")
    except Exception as e:
        print(f"Error during test: {str(e)}")
        import traceback
        traceback.print_exc()