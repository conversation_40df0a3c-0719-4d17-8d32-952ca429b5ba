{"zencoder.enableRepoIndexing": true, "python.defaultInterpreterPath": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\PythonCodingPack\\python.exe", "python.analysis.extraPaths": ["${workspaceFolder}", "${workspaceFolder}/src", "c:/Users/<USER>/Desktop/Science Laboratory Management system", "c:/Users/<USER>/Desktop/Science Laboratory Management system/src"], "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "python.linting.enabled": true, "CodeGPT.apiKey": "CodeGPT Plus Beta"}