#!/usr/bin/env python
"""
Simple Flet application for the Science Laboratory Management System.
This script is used to test basic Flet functionality.
"""
import flet as ft
import os
import sys
import logging
from datetime import datetime

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(project_root, 'simple_app.log'))
    ]
)

logger = logging.getLogger('simple_app')

def main(page: ft.Page):
    """
    Main function for the simple Flet application.
    
    Args:
        page (ft.Page): The Flet page
    """
    try:
        # Set page properties
        page.title = "Simple Science Laboratory Management System"
        page.padding = 10
        page.theme_mode = ft.ThemeMode.LIGHT
        
        # Set window properties
        if hasattr(page, 'window') and page.window:
            page.window.width = 800
            page.window.height = 600
            page.window.min_width = 400
            page.window.min_height = 300
        
        # Set up theme
        page.theme = ft.Theme(
            color_scheme_seed=ft.Colors.BLUE,
            visual_density=ft.VisualDensity.COMFORTABLE,
        )
        
        # Create a simple layout
        page.add(
            ft.Text("Simple Science Laboratory Management System", size=24, weight=ft.FontWeight.BOLD),
            ft.Divider(),
            ft.Text(f"Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"),
            ft.Divider(),
        )
        
        # Create a simple form
        username_field = ft.TextField(label="Username", autofocus=True)
        password_field = ft.TextField(label="Password", password=True)
        
        def login_clicked(e):
            if not username_field.value:
                username_field.error_text = "Username cannot be empty"
                page.update()
                return
            
            if not password_field.value:
                password_field.error_text = "Password cannot be empty"
                page.update()
                return
            
            page.add(ft.Text(f"Login successful for {username_field.value}"))
            page.update()
        
        login_button = ft.ElevatedButton("Login", on_click=login_clicked)
        
        page.add(
            ft.Card(
                content=ft.Container(
                    content=ft.Column(
                        [
                            ft.Text("Login", size=20, weight=ft.FontWeight.BOLD),
                            username_field,
                            password_field,
                            login_button,
                        ],
                        spacing=10,
                    ),
                    padding=20,
                ),
            )
        )
        
        # Add a simple data table
        data_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("ID")),
                ft.DataColumn(ft.Text("Name")),
                ft.DataColumn(ft.Text("Category")),
                ft.DataColumn(ft.Text("Quantity")),
            ],
            rows=[
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("1")),
                        ft.DataCell(ft.Text("Microscope")),
                        ft.DataCell(ft.Text("Equipment")),
                        ft.DataCell(ft.Text("5")),
                    ],
                ),
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("2")),
                        ft.DataCell(ft.Text("Test Tubes")),
                        ft.DataCell(ft.Text("Glassware")),
                        ft.DataCell(ft.Text("100")),
                    ],
                ),
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("3")),
                        ft.DataCell(ft.Text("Bunsen Burner")),
                        ft.DataCell(ft.Text("Equipment")),
                        ft.DataCell(ft.Text("10")),
                    ],
                ),
            ],
        )
        
        page.add(
            ft.Text("Inventory", size=20, weight=ft.FontWeight.BOLD),
            data_table,
        )
        
        # Add a simple chart
        chart = ft.BarChart(
            bar_groups=[
                ft.BarChartGroup(
                    x=0,
                    bar_rods=[
                        ft.BarChartRod(
                            from_y=0,
                            to_y=5,
                            width=40,
                            color=ft.Colors.BLUE,
                            tooltip="Microscopes",
                        ),
                    ],
                ),
                ft.BarChartGroup(
                    x=1,
                    bar_rods=[
                        ft.BarChartRod(
                            from_y=0,
                            to_y=100,
                            width=40,
                            color=ft.Colors.GREEN,
                            tooltip="Test Tubes",
                        ),
                    ],
                ),
                ft.BarChartGroup(
                    x=2,
                    bar_rods=[
                        ft.BarChartRod(
                            from_y=0,
                            to_y=10,
                            width=40,
                            color=ft.Colors.AMBER,
                            tooltip="Bunsen Burners",
                        ),
                    ],
                ),
            ],
            border=ft.Border(
                bottom=ft.BorderSide(width=3, color=ft.Colors.GREY),
                left=ft.BorderSide(width=3, color=ft.Colors.GREY),
            ),
            left_axis=ft.ChartAxis(
                labels=[
                    ft.ChartAxisLabel(value=0, label=ft.Text("0")),
                    ft.ChartAxisLabel(value=50, label=ft.Text("50")),
                    ft.ChartAxisLabel(value=100, label=ft.Text("100")),
                ],
            ),
            bottom_axis=ft.ChartAxis(
                labels=[
                    ft.ChartAxisLabel(value=0, label=ft.Text("Microscopes")),
                    ft.ChartAxisLabel(value=1, label=ft.Text("Test Tubes")),
                    ft.ChartAxisLabel(value=2, label=ft.Text("Bunsen Burners")),
                ],
            ),
            horizontal_grid_lines=ft.ChartGridLines(
                interval=25,
                color=ft.Colors.GREY_300,
                width=1,
            ),
            tooltip_bgcolor=ft.Colors.GREY_800,
            max_y=100,
            interactive=True,
            expand=True,
        )
        
        page.add(
            ft.Text("Inventory Chart", size=20, weight=ft.FontWeight.BOLD),
            ft.Container(
                content=chart,
                height=300,
                border=ft.Border(
                    bottom=ft.BorderSide(width=1, color=ft.Colors.GREY_400),
                ),
                padding=10,
            ),
        )
        
        # Add a footer
        page.add(
            ft.Container(
                content=ft.Row(
                    [
                        ft.Text("© 2025 Science Laboratory Management System"),
                        ft.Container(expand=True),
                        ft.Text(f"Version: 1.0.0"),
                    ],
                    spacing=10,
                ),
                padding=10,
                bgcolor=ft.Colors.BLUE_50,
            ),
        )
        
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        
        # Show error message
        page.add(
            ft.Text(f"An error occurred: {str(e)}", color=ft.Colors.RED),
        )
        page.update()

if __name__ == "__main__":
    try:
        logger.info("Starting simple application")
        ft.app(target=main)
    except Exception as e:
        logger.error(f"Error starting application: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())