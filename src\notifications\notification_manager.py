"""
Enhanced Notification Manager for Science Laboratory Management System
Provides real-time notifications, email alerts, and push notifications
"""
import json
import smtplib
import ssl
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON>ultipart
from typing import Dict, List, Any, Optional, Callable
from enum import Enum
import threading
import time
import os
import sys

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from src.database.db_manager import DatabaseManager


class NotificationType(Enum):
    """Notification type enumeration."""
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class NotificationChannel(Enum):
    """Notification channel enumeration."""
    IN_APP = "in_app"
    EMAIL = "email"
    PUSH = "push"
    SMS = "sms"


class NotificationPriority(Enum):
    """Notification priority enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class NotificationManager:
    """
    Enhanced notification manager with multiple delivery channels.
    """
    
    def __init__(self, db_manager: DatabaseManager = None, config: Dict[str, Any] = None):
        """
        Initialize notification manager.
        
        Args:
            db_manager: Database manager instance
            config: Configuration dictionary
        """
        self.db_manager = db_manager or DatabaseManager()
        self.config = config or self._load_config()
        self.subscribers = {}  # Channel -> List of callbacks
        self.notification_queue = []
        self.is_running = False
        self.worker_thread = None
        
        # Initialize database tables
        self._init_notification_tables()
        
        # Start background worker
        self.start_worker()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load notification configuration."""
        config_path = "config/notification_config.json"
        default_config = {
            "email": {
                "enabled": False,
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "username": "",
                "password": "",
                "from_address": "",
                "use_tls": True
            },
            "push": {
                "enabled": False,
                "vapid_public_key": "",
                "vapid_private_key": "",
                "vapid_email": ""
            },
            "in_app": {
                "enabled": True,
                "max_notifications": 100,
                "auto_dismiss_seconds": 5
            },
            "rules": {
                "low_stock_threshold": 10,
                "expiry_warning_days": 30,
                "experiment_overdue_days": 7
            }
        }
        
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    loaded_config = json.load(f)
                    default_config.update(loaded_config)
        except Exception as e:
            print(f"Error loading notification config: {e}")
        
        return default_config
    
    def _init_notification_tables(self):
        """Initialize notification database tables."""
        self.db_manager.connect()
        try:
            # Notifications table
            self.db_manager.cursor.execute('''
                CREATE TABLE IF NOT EXISTS notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    type TEXT NOT NULL,
                    priority TEXT NOT NULL,
                    channels TEXT NOT NULL,
                    data JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    read_at TIMESTAMP,
                    dismissed_at TIMESTAMP,
                    expires_at TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Notification preferences table
            self.db_manager.cursor.execute('''
                CREATE TABLE IF NOT EXISTS notification_preferences (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    notification_type TEXT NOT NULL,
                    channels TEXT NOT NULL,
                    enabled INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    UNIQUE(user_id, notification_type)
                )
            ''')
            
            # Notification templates table
            self.db_manager.cursor.execute('''
                CREATE TABLE IF NOT EXISTS notification_templates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    title_template TEXT NOT NULL,
                    message_template TEXT NOT NULL,
                    type TEXT NOT NULL,
                    priority TEXT NOT NULL,
                    default_channels TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            self.db_manager.connection.commit()
            
            # Insert default templates
            self._insert_default_templates()
            
        finally:
            self.db_manager.disconnect()
    
    def _insert_default_templates(self):
        """Insert default notification templates."""
        templates = [
            {
                "name": "low_stock_alert",
                "title_template": "Low Stock Alert: {item_name}",
                "message_template": "Item '{item_name}' is running low. Current quantity: {current_quantity}, Minimum: {min_quantity}",
                "type": "warning",
                "priority": "high",
                "default_channels": json.dumps(["in_app", "email"])
            },
            {
                "name": "item_expired",
                "title_template": "Expired Item: {item_name}",
                "message_template": "Item '{item_name}' has expired on {expiry_date}. Please remove from inventory.",
                "type": "critical",
                "priority": "urgent",
                "default_channels": json.dumps(["in_app", "email"])
            },
            {
                "name": "experiment_completed",
                "title_template": "Experiment Completed: {experiment_title}",
                "message_template": "Experiment '{experiment_title}' has been marked as completed by {user_name}.",
                "type": "success",
                "priority": "medium",
                "default_channels": json.dumps(["in_app"])
            },
            {
                "name": "user_login",
                "title_template": "New Login",
                "message_template": "User {user_name} logged in from {ip_address}",
                "type": "info",
                "priority": "low",
                "default_channels": json.dumps(["in_app"])
            },
            {
                "name": "system_backup",
                "title_template": "System Backup {status}",
                "message_template": "System backup {status} at {timestamp}. {details}",
                "type": "info",
                "priority": "medium",
                "default_channels": json.dumps(["in_app", "email"])
            }
        ]
        
        for template in templates:
            try:
                self.db_manager.cursor.execute('''
                    INSERT OR IGNORE INTO notification_templates 
                    (name, title_template, message_template, type, priority, default_channels)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    template["name"],
                    template["title_template"],
                    template["message_template"],
                    template["type"],
                    template["priority"],
                    template["default_channels"]
                ))
            except Exception as e:
                print(f"Error inserting template {template['name']}: {e}")
    
    def create_notification(
        self,
        title: str,
        message: str,
        notification_type: NotificationType = NotificationType.INFO,
        priority: NotificationPriority = NotificationPriority.MEDIUM,
        channels: List[NotificationChannel] = None,
        user_id: int = None,
        data: Dict[str, Any] = None,
        expires_in_hours: int = 24
    ) -> int:
        """
        Create a new notification.
        
        Args:
            title: Notification title
            message: Notification message
            notification_type: Type of notification
            priority: Priority level
            channels: Delivery channels
            user_id: Target user ID (None for system-wide)
            data: Additional data
            expires_in_hours: Hours until notification expires
            
        Returns:
            Notification ID
        """
        if channels is None:
            channels = [NotificationChannel.IN_APP]
        
        expires_at = datetime.now() + timedelta(hours=expires_in_hours)
        
        self.db_manager.connect()
        try:
            notification_id = self.db_manager.execute_insert('''
                INSERT INTO notifications 
                (user_id, title, message, type, priority, channels, data, expires_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                user_id,
                title,
                message,
                notification_type.value,
                priority.value,
                json.dumps([ch.value for ch in channels]),
                json.dumps(data or {}),
                expires_at.isoformat()
            ))
            
            # Add to processing queue
            notification = {
                "id": notification_id,
                "user_id": user_id,
                "title": title,
                "message": message,
                "type": notification_type.value,
                "priority": priority.value,
                "channels": [ch.value for ch in channels],
                "data": data or {},
                "created_at": datetime.now().isoformat()
            }
            
            self.notification_queue.append(notification)
            
            return notification_id
            
        finally:
            self.db_manager.disconnect()
    
    def create_from_template(
        self,
        template_name: str,
        template_data: Dict[str, Any],
        user_id: int = None,
        override_channels: List[NotificationChannel] = None
    ) -> int:
        """
        Create notification from template.
        
        Args:
            template_name: Name of the template
            template_data: Data to fill template placeholders
            user_id: Target user ID
            override_channels: Override default channels
            
        Returns:
            Notification ID
        """
        self.db_manager.connect()
        try:
            # Get template
            self.db_manager.cursor.execute('''
                SELECT title_template, message_template, type, priority, default_channels
                FROM notification_templates WHERE name = ?
            ''', (template_name,))
            
            template = self.db_manager.cursor.fetchone()
            if not template:
                raise ValueError(f"Template '{template_name}' not found")
            
            title_template, message_template, type_str, priority_str, default_channels_str = template
            
            # Fill templates
            title = title_template.format(**template_data)
            message = message_template.format(**template_data)
            
            # Parse channels
            if override_channels:
                channels = override_channels
            else:
                channel_names = json.loads(default_channels_str)
                channels = [NotificationChannel(ch) for ch in channel_names]
            
            return self.create_notification(
                title=title,
                message=message,
                notification_type=NotificationType(type_str),
                priority=NotificationPriority(priority_str),
                channels=channels,
                user_id=user_id,
                data=template_data
            )
            
        finally:
            self.db_manager.disconnect()
    
    def get_user_notifications(
        self,
        user_id: int,
        unread_only: bool = False,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get notifications for a user.
        
        Args:
            user_id: User ID
            unread_only: Only return unread notifications
            limit: Maximum number of notifications
            
        Returns:
            List of notifications
        """
        self.db_manager.connect()
        try:
            query = '''
                SELECT id, title, message, type, priority, data, created_at, read_at
                FROM notifications 
                WHERE (user_id = ? OR user_id IS NULL)
                  AND (expires_at IS NULL OR expires_at > datetime('now'))
            '''
            params = [user_id]
            
            if unread_only:
                query += ' AND read_at IS NULL'
            
            query += ' ORDER BY created_at DESC LIMIT ?'
            params.append(limit)
            
            self.db_manager.cursor.execute(query, params)
            
            notifications = []
            for row in self.db_manager.cursor.fetchall():
                notifications.append({
                    "id": row[0],
                    "title": row[1],
                    "message": row[2],
                    "type": row[3],
                    "priority": row[4],
                    "data": json.loads(row[5]) if row[5] else {},
                    "created_at": row[6],
                    "read_at": row[7],
                    "is_read": row[7] is not None
                })
            
            return notifications
            
        finally:
            self.db_manager.disconnect()
    
    def mark_as_read(self, notification_id: int, user_id: int = None):
        """Mark notification as read."""
        self.db_manager.connect()
        try:
            query = 'UPDATE notifications SET read_at = datetime("now") WHERE id = ?'
            params = [notification_id]
            
            if user_id:
                query += ' AND (user_id = ? OR user_id IS NULL)'
                params.append(user_id)
            
            self.db_manager.cursor.execute(query, params)
            self.db_manager.connection.commit()
            
        finally:
            self.db_manager.disconnect()
    
    def dismiss_notification(self, notification_id: int, user_id: int = None):
        """Dismiss notification."""
        self.db_manager.connect()
        try:
            query = 'UPDATE notifications SET dismissed_at = datetime("now") WHERE id = ?'
            params = [notification_id]
            
            if user_id:
                query += ' AND (user_id = ? OR user_id IS NULL)'
                params.append(user_id)
            
            self.db_manager.cursor.execute(query, params)
            self.db_manager.connection.commit()
            
        finally:
            self.db_manager.disconnect()
    
    def subscribe(self, channel: NotificationChannel, callback: Callable):
        """Subscribe to notifications on a channel."""
        if channel not in self.subscribers:
            self.subscribers[channel] = []
        self.subscribers[channel].append(callback)
    
    def unsubscribe(self, channel: NotificationChannel, callback: Callable):
        """Unsubscribe from notifications on a channel."""
        if channel in self.subscribers and callback in self.subscribers[channel]:
            self.subscribers[channel].remove(callback)
    
    def start_worker(self):
        """Start background notification worker."""
        if not self.is_running:
            self.is_running = True
            self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
            self.worker_thread.start()
    
    def stop_worker(self):
        """Stop background notification worker."""
        self.is_running = False
        if self.worker_thread:
            self.worker_thread.join()
    
    def _worker_loop(self):
        """Background worker loop for processing notifications."""
        while self.is_running:
            try:
                if self.notification_queue:
                    notification = self.notification_queue.pop(0)
                    self._process_notification(notification)
                else:
                    time.sleep(1)  # Wait before checking again
            except Exception as e:
                print(f"Error in notification worker: {e}")
                time.sleep(5)  # Wait longer on error
    
    def _process_notification(self, notification: Dict[str, Any]):
        """Process a notification through all channels."""
        channels = notification["channels"]
        
        for channel_name in channels:
            try:
                channel = NotificationChannel(channel_name)
                
                if channel == NotificationChannel.IN_APP:
                    self._send_in_app_notification(notification)
                elif channel == NotificationChannel.EMAIL:
                    self._send_email_notification(notification)
                elif channel == NotificationChannel.PUSH:
                    self._send_push_notification(notification)
                
            except Exception as e:
                print(f"Error sending notification via {channel_name}: {e}")
    
    def _send_in_app_notification(self, notification: Dict[str, Any]):
        """Send in-app notification."""
        if NotificationChannel.IN_APP in self.subscribers:
            for callback in self.subscribers[NotificationChannel.IN_APP]:
                try:
                    callback(notification)
                except Exception as e:
                    print(f"Error in in-app notification callback: {e}")
    
    def _send_email_notification(self, notification: Dict[str, Any]):
        """Send email notification."""
        if not self.config["email"]["enabled"]:
            return
        
        try:
            # Get user email if user_id is specified
            user_email = None
            if notification["user_id"]:
                self.db_manager.connect()
                try:
                    self.db_manager.cursor.execute(
                        "SELECT email FROM users WHERE id = ?",
                        (notification["user_id"],)
                    )
                    result = self.db_manager.cursor.fetchone()
                    if result:
                        user_email = result[0]
                finally:
                    self.db_manager.disconnect()
            
            if not user_email:
                return  # No email to send to
            
            # Create email
            msg = MIMEMultipart()
            msg['From'] = self.config["email"]["from_address"]
            msg['To'] = user_email
            msg['Subject'] = f"[Lab Management] {notification['title']}"
            
            # Email body
            body = f"""
            {notification['message']}
            
            ---
            This is an automated notification from the Science Laboratory Management System.
            Time: {notification['created_at']}
            Priority: {notification['priority'].upper()}
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            context = ssl.create_default_context()
            with smtplib.SMTP(self.config["email"]["smtp_server"], self.config["email"]["smtp_port"]) as server:
                if self.config["email"]["use_tls"]:
                    server.starttls(context=context)
                server.login(self.config["email"]["username"], self.config["email"]["password"])
                server.send_message(msg)
            
            print(f"Email notification sent to {user_email}")
            
        except Exception as e:
            print(f"Error sending email notification: {e}")
    
    def _send_push_notification(self, notification: Dict[str, Any]):
        """Send push notification."""
        if not self.config["push"]["enabled"]:
            return
        
        # Implementation would use web push protocol
        # For now, just log the notification
        print(f"Push notification: {notification['title']} - {notification['message']}")
    
    def check_system_alerts(self):
        """Check for system conditions that require notifications."""
        try:
            # Check for low stock items
            self._check_low_stock_alerts()
            
            # Check for expired items
            self._check_expired_items()
            
            # Check for overdue experiments
            self._check_overdue_experiments()
            
        except Exception as e:
            print(f"Error checking system alerts: {e}")
    
    def _check_low_stock_alerts(self):
        """Check for low stock items and create alerts."""
        self.db_manager.connect()
        try:
            self.db_manager.cursor.execute('''
                SELECT id, name, quantity, min_quantity, location
                FROM inventory_items 
                WHERE quantity <= min_quantity AND min_quantity > 0
            ''')
            
            for row in self.db_manager.cursor.fetchall():
                item_id, name, quantity, min_quantity, location = row
                
                # Check if we already sent an alert recently
                self.db_manager.cursor.execute('''
                    SELECT id FROM notifications 
                    WHERE title LIKE ? AND created_at > datetime('now', '-24 hours')
                ''', (f"Low Stock Alert: {name}",))
                
                if not self.db_manager.cursor.fetchone():
                    self.create_from_template(
                        "low_stock_alert",
                        {
                            "item_name": name,
                            "current_quantity": quantity,
                            "min_quantity": min_quantity,
                            "location": location or "Unknown"
                        }
                    )
                    
        finally:
            self.db_manager.disconnect()
    
    def _check_expired_items(self):
        """Check for expired items and create alerts."""
        self.db_manager.connect()
        try:
            self.db_manager.cursor.execute('''
                SELECT id, name, expiry_date, location
                FROM inventory_items 
                WHERE expiry_date <= date('now')
            ''')
            
            for row in self.db_manager.cursor.fetchall():
                item_id, name, expiry_date, location = row
                
                # Check if we already sent an alert recently
                self.db_manager.cursor.execute('''
                    SELECT id FROM notifications 
                    WHERE title LIKE ? AND created_at > datetime('now', '-24 hours')
                ''', (f"Expired Item: {name}",))
                
                if not self.db_manager.cursor.fetchone():
                    self.create_from_template(
                        "item_expired",
                        {
                            "item_name": name,
                            "expiry_date": expiry_date,
                            "location": location or "Unknown"
                        }
                    )
                    
        finally:
            self.db_manager.disconnect()
    
    def _check_overdue_experiments(self):
        """Check for overdue experiments and create alerts."""
        overdue_days = self.config["rules"]["experiment_overdue_days"]
        
        self.db_manager.connect()
        try:
            self.db_manager.cursor.execute('''
                SELECT e.id, e.title, e.start_date, u.full_name
                FROM experiments e
                JOIN users u ON e.created_by = u.id
                WHERE e.status = 'in_progress' 
                  AND e.start_date <= date('now', '-{} days')
            '''.format(overdue_days))
            
            for row in self.db_manager.cursor.fetchall():
                exp_id, title, start_date, user_name = row
                
                # Check if we already sent an alert recently
                self.db_manager.cursor.execute('''
                    SELECT id FROM notifications 
                    WHERE title LIKE ? AND created_at > datetime('now', '-7 days')
                ''', (f"Overdue Experiment: {title}",))
                
                if not self.db_manager.cursor.fetchone():
                    self.create_notification(
                        title=f"Overdue Experiment: {title}",
                        message=f"Experiment '{title}' started on {start_date} is overdue for review.",
                        notification_type=NotificationType.WARNING,
                        priority=NotificationPriority.HIGH,
                        channels=[NotificationChannel.IN_APP, NotificationChannel.EMAIL],
                        data={
                            "experiment_id": exp_id,
                            "experiment_title": title,
                            "start_date": start_date,
                            "researcher": user_name
                        }
                    )
                    
        finally:
            self.db_manager.disconnect()
