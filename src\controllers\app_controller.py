import flet as ft
from src.views.login_view import Lo<PERSON>View
from src.views.registration_view import RegistrationView
from src.views.password_reset_view import PasswordResetView
from src.views.user_profile_view import UserProfileView
from src.views.user_admin_view import UserAdminView
from src.views.dashboard_view import DashboardView
from src.views.inventory_view import InventoryView
from src.views.experiment_view_new import ExperimentView
from src.views.scheduling_view import SchedulingView
from src.views.safety_view import SafetyView
from src.views.reports_view import ReportsView
from src.models.user_model import UserModel
from src.models.inventory_model import InventoryModel
from src.utils.database import Database
from src.utils.security import Security
from src.utils.experiment_analyzer import ExperimentAnalyzer
from src.utils.report_generator import ReportGenerator
from datetime import timedelta
from src.models.experiment_model import ExperimentModel

class AppController:
    """
    Main controller for the Science Laboratory Management System application.
    Manages navigation, authentication, and view transitions.
    """
    
    def __init__(self, page: ft.Page):
        """
        Initialize the application controller.
        
        Args:
            page (ft.Page): The main Flet page object
        """
        self.page = page
        self.current_user = None
        self.session_token = None
        self.db = Database()
        self.security = Security()
        self.time_utils = timedelta
        self.user_model = UserModel(self.db, self.security)
        self.inventory_model = InventoryModel(self.db)
        self.experiment_model = ExperimentModel(self.db)
        
        # Initialize utilities
        self.experiment_analyzer = ExperimentAnalyzer(self.experiment_model)
        self.report_generator = ReportGenerator(self.experiment_model)
        
        # Import here to avoid circular imports
        from src.models.permission_model import PermissionModel
        self.permission_model = PermissionModel(self.db)
        
        # Initialize views
        self.login_view = LoginView(self)
        self.registration_view = RegistrationView(self)
        self.password_reset_view = PasswordResetView(self)
        self.user_profile_view = UserProfileView(self)
        self.user_admin_view = UserAdminView(self)
        self.dashboard_view = DashboardView(self)
        self.inventory_view = InventoryView(self)
        self.experiment_view = ExperimentView(self)
        self.scheduling_view = SchedulingView(self)
        self.safety_view = SafetyView(self)
        self.reports_view = ReportsView(self)
        
        # Set up navigation rail
        self.navigation_rail = ft.NavigationRail(
            selected_index=0,
            label_type=ft.NavigationRailLabelType.ALL,
            min_width=100,
            min_extended_width=300,
            destinations=[
                ft.NavigationRailDestination(
                    icon=ft.icons.DASHBOARD,
                    selected_icon=ft.icons.DASHBOARD,
                    label="Dashboard",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.INVENTORY,
                    selected_icon=ft.icons.INVENTORY,
                    label="Inventory",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.SCIENCE,
                    selected_icon=ft.icons.SCIENCE,
                    label="Experiments",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.CALENDAR_TODAY,
                    selected_icon=ft.icons.CALENDAR_TODAY,
                    label="Scheduling",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.PERSON,
                    selected_icon=ft.icons.PERSON,
                    label="Profile",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.HEALTH_AND_SAFETY,
                    selected_icon=ft.icons.HEALTH_AND_SAFETY,
                    label="Safety",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.ANALYTICS,
                    selected_icon=ft.icons.ANALYTICS,
                    label="Reports",
                ),
            ],
            on_change=self.navigation_change,
        )
        
        # Main content container
        self.content_container = ft.Container(
            content=ft.Text("Loading..."),
            expand=True,
            padding=20,
        )
        
        # Main layout
        self.main_layout = ft.Row(
            [
                self.navigation_rail,
                ft.VerticalDivider(width=1),
                self.content_container,
            ],
            expand=True,
            visible=False,
        )
        
    def initialize(self):
        """Initialize the application and show the login screen."""
        self.page.add(self.login_view.build())
        self.page.add(self.main_layout)
        self.page.update()
    
    def navigation_change(self, e):
        """
        Handle navigation rail selection changes.
        
        Args:
            e: The navigation change event
        """
        index = e.control.selected_index
        
        # Clear current content
        self.content_container.content = None
        
        # Set new content based on selection
        if index == 0:
            self.content_container.content = self.dashboard_view.build()
        elif index == 1:
            self.content_container.content = self.inventory_view.build()
        elif index == 2:
            self.content_container.content = self.experiment_view.build()
        elif index == 3:
            self.content_container.content = self.scheduling_view.build()
        elif index == 4:
            self.content_container.content = self.user_profile_view.build()
        elif index == 5:
            self.content_container.content = self.safety_view.build()
        elif index == 6:
            self.content_container.content = self.reports_view.build()
        
        self.page.update()
    
    def login(self, username, password):
        """
        Authenticate user and show main application if successful.
        
        Args:
            username (str): The username
            password (str): The password
            
        Returns:
            bool: True if login successful, False otherwise
        """
        user = self.user_model.authenticate(username, password)
        
        if user:
            self.current_user = user
            self.login_view.visible = False
            self.main_layout.visible = True
            self.registration_view.visible = False
            self.password_reset_view.visible = False
            self.navigation_rail.selected_index = 0
            self.content_container.content = self.dashboard_view.build()
            self.page.update()
            return True
        
        return False
    
    def show_login_view(self):
        """Show the login view."""
        self.login_view.visible = True
        self.registration_view.visible = False
        self.password_reset_view.visible = False
        self.page.update()
    
    def show_registration_view(self):
        """Show the registration view."""
        self.login_view.visible = False
        self.registration_view.visible = True
        self.password_reset_view.visible = False
        self.page.add(self.registration_view.build())
        self.page.update()
    
    def show_password_reset_view(self):
        """Show the password reset view."""
        self.login_view.visible = False
        self.registration_view.visible = False
        self.password_reset_view.visible = True
        self.password_reset_view.current_step = "request"  # Start at request step
        self.page.add(self.password_reset_view.build())
        self.page.update()
    
    def logout(self):
        """Log out the current user and return to login screen."""
        self.current_user = None
        self.main_layout.visible = False
        self.login_view.visible = True
        self.registration_view.visible = False
        self.password_reset_view.visible = False
        self.page.update()
        
    def analyze_experiment(self, experiment_id):
        """
        Analyze an experiment and generate insights.
        
        Args:
            experiment_id (int): The experiment ID
            
        Returns:
            dict: Analysis results
        """
        return self.experiment_analyzer.analyze_experiment(experiment_id)
    
    def generate_experiment_report(self, experiment_id, format="html"):
        """
        Generate a report for an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            format (str): The report format (html, pdf, csv, json)
            
        Returns:
            str: Path to the generated report file
        """
        return self.report_generator.generate_experiment_report(experiment_id, format)
    
    def generate_measurement_charts(self, experiment_id):
        """
        Generate charts for experiment measurements.
        
        Args:
            experiment_id (int): The experiment ID
            
        Returns:
            list: Paths to the generated chart files
        """
        return self.report_generator.generate_measurement_charts(experiment_id)
    
    def generate_summary_report(self, experiment_ids=None):
        """
        Generate a summary report for multiple experiments.
        
        Args:
            experiment_ids (list): List of experiment IDs, or None for all experiments
            
        Returns:
            str: Path to the generated report file
        """
        return self.report_generator.generate_summary_report(experiment_ids)
    
    def schedule_automatic_analysis(self, experiment_id, interval_hours=24):
        """
        Schedule automatic analysis for an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            interval_hours (int): Interval in hours between analyses
            
        Returns:
            bool: True if scheduled successfully, False otherwise
        """
        return self.experiment_analyzer.schedule_automatic_analysis(experiment_id, interval_hours)
    
    def schedule_automatic_reports(self, interval_hours=24):
        """
        Schedule automatic report generation.
        
        Args:
            interval_hours (int): Interval in hours between report generation
            
        Returns:
            bool: True if scheduled successfully, False otherwise
        """
        return self.report_generator.schedule_automatic_reports(interval_hours)