import flet as ft
from datetime import datetime, timedelta
import csv
import os

class AuditTrailView:
    """
    Audit trail view for the Science Laboratory Management System.
    Provides detailed activity logs and audit trail functionality.
    """
    
    def __init__(self, controller):
        """
        Initialize the audit trail view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        self.audit_logs = []
        
        # Create filter controls
        self.date_range_dropdown = ft.Dropdown(
            label="Date Range",
            options=[
                ft.dropdown.Option("today", "Today"),
                ft.dropdown.Option("yesterday", "Yesterday"),
                ft.dropdown.Option("last_7_days", "Last 7 Days"),
                ft.dropdown.Option("last_30_days", "Last 30 Days"),
                ft.dropdown.Option("custom", "Custom Range"),
            ],
            value="last_7_days",
            on_change=self.date_range_changed,
            width=200,
        )
        
        self.start_date_picker = ft.TextField(
            label="Start Date (YYYY-MM-DD)",
            value=self.get_default_start_date(),
            width=200,
            visible=False,
        )
        
        self.end_date_picker = ft.TextField(
            label="End Date (YYYY-MM-DD)",
            value=self.get_default_end_date(),
            width=200,
            visible=False,
        )
        
        self.user_dropdown = ft.Dropdown(
            label="User",
            options=[
                ft.dropdown.Option("all", "All Users"),
            ],
            value="all",
            width=200,
        )
        
        self.action_dropdown = ft.Dropdown(
            label="Action",
            options=[
                ft.dropdown.Option("all", "All Actions"),
                ft.dropdown.Option("create", "Create"),
                ft.dropdown.Option("update", "Update"),
                ft.dropdown.Option("delete", "Delete"),
                ft.dropdown.Option("login", "Login"),
                ft.dropdown.Option("logout", "Logout"),
                ft.dropdown.Option("export", "Export"),
                ft.dropdown.Option("import", "Import"),
                ft.dropdown.Option("view", "View"),
            ],
            value="all",
            width=200,
        )
        
        self.entity_dropdown = ft.Dropdown(
            label="Entity Type",
            options=[
                ft.dropdown.Option("all", "All Entities"),
                ft.dropdown.Option("user", "User"),
                ft.dropdown.Option("inventory", "Inventory"),
                ft.dropdown.Option("experiment", "Experiment"),
                ft.dropdown.Option("schedule", "Schedule"),
                ft.dropdown.Option("safety", "Safety"),
                ft.dropdown.Option("report", "Report"),
                ft.dropdown.Option("setting", "Setting"),
            ],
            value="all",
            width=200,
        )
        
        self.search_field = ft.TextField(
            label="Search",
            prefix_icon=ft.icons.SEARCH,
            on_change=self.search_changed,
            expand=True,
        )
        
        self.apply_filter_button = ft.ElevatedButton(
            text="Apply Filters",
            icon=ft.icons.FILTER_ALT,
            on_click=self.apply_filter_clicked,
        )
        
        self.clear_filter_button = ft.ElevatedButton(
            text="Clear Filters",
            icon=ft.icons.CLEAR,
            on_click=self.clear_filter_clicked,
        )
        
        # Create export controls
        self.export_format_dropdown = ft.Dropdown(
            label="Export Format",
            options=[
                ft.dropdown.Option("csv", "CSV"),
                ft.dropdown.Option("json", "JSON"),
            ],
            value="csv",
            width=150,
        )
        
        self.export_button = ft.ElevatedButton(
            text="Export",
            icon=ft.icons.DOWNLOAD,
            on_click=self.export_clicked,
        )
        
        # Create audit log table
        self.audit_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("Timestamp")),
                ft.DataColumn(ft.Text("User")),
                ft.DataColumn(ft.Text("Action")),
                ft.DataColumn(ft.Text("Entity Type")),
                ft.DataColumn(ft.Text("Entity ID")),
                ft.DataColumn(ft.Text("Details")),
                ft.DataColumn(ft.Text("IP Address")),
            ],
            rows=[],
        )
        
        # Create pagination controls
        self.page_size_dropdown = ft.Dropdown(
            label="Items per page",
            options=[
                ft.dropdown.Option("25", "25"),
                ft.dropdown.Option("50", "50"),
                ft.dropdown.Option("100", "100"),
                ft.dropdown.Option("250", "250"),
            ],
            value="50",
            width=150,
            on_change=self.page_size_changed,
        )
        
        self.pagination_text = ft.Text("Showing 0-0 of 0")
        
        self.prev_page_button = ft.IconButton(
            icon=ft.icons.ARROW_BACK,
            on_click=self.prev_page_clicked,
            disabled=True,
        )
        
        self.next_page_button = ft.IconButton(
            icon=ft.icons.ARROW_FORWARD,
            on_click=self.next_page_clicked,
            disabled=True,
        )
        
        # Pagination state
        self.current_page = 1
        self.total_pages = 1
        self.page_size = 50
        self.total_records = 0
    
    def build(self):
        """
        Build and return the audit trail view.
        
        Returns:
            ft.Container: The audit trail view container
        """
        # Load users for dropdown
        self.load_users()
        
        # Load initial data
        self.load_audit_logs()
        
        # Create filter row
        filter_row_1 = ft.Row(
            [
                self.date_range_dropdown,
                self.start_date_picker,
                self.end_date_picker,
                self.user_dropdown,
            ],
            alignment=ft.MainAxisAlignment.START,
            wrap=True,
        )
        
        filter_row_2 = ft.Row(
            [
                self.action_dropdown,
                self.entity_dropdown,
                self.search_field,
            ],
            alignment=ft.MainAxisAlignment.START,
            wrap=True,
        )
        
        filter_row_3 = ft.Row(
            [
                self.apply_filter_button,
                self.clear_filter_button,
                ft.Container(expand=True),
                self.export_format_dropdown,
                self.export_button,
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        )
        
        # Create pagination row
        pagination_row = ft.Row(
            [
                self.page_size_dropdown,
                ft.Container(expand=True),
                self.prev_page_button,
                self.pagination_text,
                self.next_page_button,
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        )
        
        # Return the complete view
        return ft.Container(
            content=ft.Column(
                [
                    ft.Text("Audit Trail", size=24, weight=ft.FontWeight.BOLD),
                    ft.Container(height=10),
                    ft.Card(
                        content=ft.Container(
                            content=ft.Column(
                                [
                                    ft.Text("Filters", size=16, weight=ft.FontWeight.BOLD),
                                    ft.Divider(),
                                    filter_row_1,
                                    filter_row_2,
                                    filter_row_3,
                                ],
                                spacing=10,
                            ),
                            padding=20,
                        ),
                    ),
                    ft.Container(height=20),
                    ft.Container(
                        content=self.audit_table,
                        border=ft.border.all(1, ft.colors.GREY_300),
                        border_radius=5,
                        padding=10,
                        expand=True,
                    ),
                    ft.Container(height=10),
                    pagination_row,
                ],
                spacing=10,
                expand=True,
            ),
            padding=20,
            expand=True,
        )
    
    def load_users(self):
        """Load users for the user dropdown."""
        # Query users
        users = self.controller.db.execute_query(
            "SELECT id, username, full_name FROM users ORDER BY username"
        )
        
        # Add options to dropdown
        self.user_dropdown.options = [ft.dropdown.Option("all", "All Users")]
        for user in users:
            self.user_dropdown.options.append(
                ft.dropdown.Option(str(user["id"]), f"{user['username']} ({user['full_name']})")
            )
    
    def load_audit_logs(self):
        """Load audit logs based on current filters and pagination."""
        # Get filter values
        start_date, end_date = self.get_date_range()
        user_id = self.user_dropdown.value
        action = self.action_dropdown.value
        entity_type = self.entity_dropdown.value
        search_text = self.search_field.value
        
        # Build query parts
        query_parts = ["timestamp >= ? AND timestamp <= ?"]
        params = [start_date, end_date]
        
        if user_id != "all":
            query_parts.append("user_id = ?")
            params.append(int(user_id))
        
        if action != "all":
            query_parts.append("action = ?")
            params.append(action)
        
        if entity_type != "all":
            query_parts.append("entity_type = ?")
            params.append(entity_type)
        
        if search_text:
            query_parts.append("(details LIKE ? OR entity_id LIKE ?)")
            params.append(f"%{search_text}%")
            params.append(f"%{search_text}%")
        
        # Calculate pagination
        offset = (self.current_page - 1) * self.page_size
        
        # Get total count
        count_query = f"""
            SELECT COUNT(*) as count
            FROM audit_logs a
            JOIN users u ON a.user_id = u.id
            WHERE {" AND ".join(query_parts)}
        """
        
        count_result = self.controller.db.execute_query(count_query, tuple(params))
        self.total_records = count_result[0]["count"] if count_result else 0
        self.total_pages = max(1, (self.total_records + self.page_size - 1) // self.page_size)
        
        # Adjust current page if needed
        if self.current_page > self.total_pages:
            self.current_page = self.total_pages
            offset = (self.current_page - 1) * self.page_size
        
        # Build the complete query
        query = f"""
            SELECT 
                a.id,
                a.timestamp,
                a.user_id,
                a.action,
                a.entity_type,
                a.entity_id,
                a.details,
                a.ip_address,
                u.username,
                u.full_name
            FROM 
                audit_logs a
                JOIN users u ON a.user_id = u.id
            WHERE 
                {" AND ".join(query_parts)}
            ORDER BY 
                a.timestamp DESC
            LIMIT ? OFFSET ?
        """
        
        params.append(self.page_size)
        params.append(offset)
        
        # Execute query
        self.audit_logs = self.controller.db.execute_query(query, tuple(params))
        
        # Update pagination text
        start_item = offset + 1 if self.total_records > 0 else 0
        end_item = min(offset + self.page_size, self.total_records)
        self.pagination_text.value = f"Showing {start_item}-{end_item} of {self.total_records}"
        
        # Update pagination buttons
        self.prev_page_button.disabled = self.current_page <= 1
        self.next_page_button.disabled = self.current_page >= self.total_pages
        
        # Clear existing rows
        self.audit_table.rows.clear()
        
        # Add rows for audit logs
        for log in self.audit_logs:
            # Format timestamp
            timestamp = self.format_timestamp(log["timestamp"])
            
            # Format action and entity type
            action = log["action"].capitalize()
            entity_type = log["entity_type"].replace("_", " ").capitalize()
            
            # Create row
            self.audit_table.rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(timestamp)),
                        ft.DataCell(ft.Text(f"{log['username']} ({log['full_name']})")),
                        ft.DataCell(ft.Text(action)),
                        ft.DataCell(ft.Text(entity_type)),
                        ft.DataCell(ft.Text(str(log["entity_id"] or ""))),
                        ft.DataCell(ft.Text(log["details"])),
                        ft.DataCell(ft.Text(log["ip_address"] or "")),
                    ]
                )
            )
        
        # Update the page
        self.controller.page.update()
    
    def date_range_changed(self, e):
        """
        Handle date range dropdown change.
        
        Args:
            e: The change event
        """
        # Show/hide custom date fields
        if self.date_range_dropdown.value == "custom":
            self.start_date_picker.visible = True
            self.end_date_picker.visible = True
        else:
            self.start_date_picker.visible = False
            self.end_date_picker.visible = False
            
            # Set default dates based on selection
            start_date, end_date = self.get_date_range()
            self.start_date_picker.value = start_date.split("T")[0]
            self.end_date_picker.value = end_date.split("T")[0]
        
        # Update the page
        self.controller.page.update()
    
    def search_changed(self, e):
        """
        Handle search field change.
        
        Args:
            e: The change event
        """
        # We'll apply the search when the filter button is clicked
        pass
    
    def apply_filter_clicked(self, e):
        """
        Handle apply filter button click.
        
        Args:
            e: The click event
        """
        # Reset to first page
        self.current_page = 1
        
        # Load data with new filters
        self.load_audit_logs()
    
    def clear_filter_clicked(self, e):
        """
        Handle clear filter button click.
        
        Args:
            e: The click event
        """
        # Reset filters
        self.date_range_dropdown.value = "last_7_days"
        self.start_date_picker.visible = False
        self.end_date_picker.visible = False
        self.user_dropdown.value = "all"
        self.action_dropdown.value = "all"
        self.entity_dropdown.value = "all"
        self.search_field.value = ""
        
        # Reset to first page
        self.current_page = 1
        
        # Load data with reset filters
        self.load_audit_logs()
    
    def export_clicked(self, e):
        """
        Handle export button click.
        
        Args:
            e: The click event
        """
        # Get export format
        export_format = self.export_format_dropdown.value
        
        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"audit_trail_{timestamp}.{export_format}"
        
        # Export data
        if export_format == "csv":
            self.export_to_csv(filename)
        elif export_format == "json":
            self.export_to_json(filename)
    
    def export_to_csv(self, filename):
        """
        Export audit logs to CSV file.
        
        Args:
            filename (str): Output filename
        """
        try:
            # Create export directory if it doesn't exist
            export_dir = os.path.join(os.getcwd(), "exports")
            os.makedirs(export_dir, exist_ok=True)
            
            # Get all data for export (not just current page)
            start_date, end_date = self.get_date_range()
            user_id = self.user_dropdown.value
            action = self.action_dropdown.value
            entity_type = self.entity_dropdown.value
            search_text = self.search_field.value
            
            # Build query parts
            query_parts = ["timestamp >= ? AND timestamp <= ?"]
            params = [start_date, end_date]
            
            if user_id != "all":
                query_parts.append("user_id = ?")
                params.append(int(user_id))
            
            if action != "all":
                query_parts.append("action = ?")
                params.append(action)
            
            if entity_type != "all":
                query_parts.append("entity_type = ?")
                params.append(entity_type)
            
            if search_text:
                query_parts.append("(details LIKE ? OR entity_id LIKE ?)")
                params.append(f"%{search_text}%")
                params.append(f"%{search_text}%")
            
            # Build the complete query
            query = f"""
                SELECT 
                    a.timestamp,
                    u.username,
                    u.full_name,
                    a.action,
                    a.entity_type,
                    a.entity_id,
                    a.details,
                    a.ip_address
                FROM 
                    audit_logs a
                    JOIN users u ON a.user_id = u.id
                WHERE 
                    {" AND ".join(query_parts)}
                ORDER BY 
                    a.timestamp DESC
            """
            
            # Execute query
            export_data = self.controller.db.execute_query(query, tuple(params))
            
            # Write to CSV file
            filepath = os.path.join(export_dir, filename)
            with open(filepath, 'w', newline='') as csvfile:
                # Define headers
                headers = ["Timestamp", "Username", "Full Name", "Action", "Entity Type", "Entity ID", "Details", "IP Address"]
                
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                
                # Write data rows
                for row in export_data:
                    writer.writerow([
                        row["timestamp"],
                        row["username"],
                        row["full_name"],
                        row["action"],
                        row["entity_type"],
                        row["entity_id"],
                        row["details"],
                        row["ip_address"] or "",
                    ])
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text(f"Exported {len(export_data)} records to {filepath}"),
                bgcolor=ft.colors.GREEN_400,
            )
            self.controller.page.snack_bar.open = True
            
            # Log the export
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "export",
                "audit_trail",
                None,
                f"Exported audit trail to CSV ({len(export_data)} records)"
            )
        except Exception as e:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text(f"Error exporting data: {str(e)}"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
        
        # Update the page
        self.controller.page.update()
    
    def export_to_json(self, filename):
        """
        Export audit logs to JSON file.
        
        Args:
            filename (str): Output filename
        """
        try:
            # Create export directory if it doesn't exist
            export_dir = os.path.join(os.getcwd(), "exports")
            os.makedirs(export_dir, exist_ok=True)
            
            # Get all data for export (not just current page)
            start_date, end_date = self.get_date_range()
            user_id = self.user_dropdown.value
            action = self.action_dropdown.value
            entity_type = self.entity_dropdown.value
            search_text = self.search_field.value
            
            # Build query parts
            query_parts = ["timestamp >= ? AND timestamp <= ?"]
            params = [start_date, end_date]
            
            if user_id != "all":
                query_parts.append("user_id = ?")
                params.append(int(user_id))
            
            if action != "all":
                query_parts.append("action = ?")
                params.append(action)
            
            if entity_type != "all":
                query_parts.append("entity_type = ?")
                params.append(entity_type)
            
            if search_text:
                query_parts.append("(details LIKE ? OR entity_id LIKE ?)")
                params.append(f"%{search_text}%")
                params.append(f"%{search_text}%")
            
            # Build the complete query
            query = f"""
                SELECT 
                    a.timestamp,
                    u.username,
                    u.full_name,
                    a.action,
                    a.entity_type,
                    a.entity_id,
                    a.details,
                    a.ip_address
                FROM 
                    audit_logs a
                    JOIN users u ON a.user_id = u.id
                WHERE 
                    {" AND ".join(query_parts)}
                ORDER BY 
                    a.timestamp DESC
            """
            
            # Execute query
            export_data = self.controller.db.execute_query(query, tuple(params))
            
            # Write to JSON file
            filepath = os.path.join(export_dir, filename)
            with open(filepath, 'w') as jsonfile:
                import json
                json.dump(export_data, jsonfile, indent=2, default=str)
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text(f"Exported {len(export_data)} records to {filepath}"),
                bgcolor=ft.colors.GREEN_400,
            )
            self.controller.page.snack_bar.open = True
            
            # Log the export
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "export",
                "audit_trail",
                None,
                f"Exported audit trail to JSON ({len(export_data)} records)"
            )
        except Exception as e:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text(f"Error exporting data: {str(e)}"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
        
        # Update the page
        self.controller.page.update()
    
    def page_size_changed(self, e):
        """
        Handle page size dropdown change.
        
        Args:
            e: The change event
        """
        # Update page size
        self.page_size = int(self.page_size_dropdown.value)
        
        # Reset to first page
        self.current_page = 1
        
        # Reload data
        self.load_audit_logs()
    
    def prev_page_clicked(self, e):
        """
        Handle previous page button click.
        
        Args:
            e: The click event
        """
        if self.current_page > 1:
            self.current_page -= 1
            self.load_audit_logs()
    
    def next_page_clicked(self, e):
        """
        Handle next page button click.
        
        Args:
            e: The click event
        """
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.load_audit_logs()
    
    def get_date_range(self):
        """
        Get start and end dates based on selected date range.
        
        Returns:
            tuple: (start_date, end_date)
        """
        if self.date_range_dropdown.value == "custom":
            start_date = f"{self.start_date_picker.value}T00:00:00"
            end_date = f"{self.end_date_picker.value}T23:59:59"
            return (start_date, end_date)
        
        now = datetime.now()
        end_date = now.replace(hour=23, minute=59, second=59).isoformat()
        
        if self.date_range_dropdown.value == "today":
            start_date = now.replace(hour=0, minute=0, second=0).isoformat()
        elif self.date_range_dropdown.value == "yesterday":
            yesterday = now - timedelta(days=1)
            start_date = yesterday.replace(hour=0, minute=0, second=0).isoformat()
            end_date = yesterday.replace(hour=23, minute=59, second=59).isoformat()
        elif self.date_range_dropdown.value == "last_7_days":
            start_date = (now - timedelta(days=7)).replace(hour=0, minute=0, second=0).isoformat()
        elif self.date_range_dropdown.value == "last_30_days":
            start_date = (now - timedelta(days=30)).replace(hour=0, minute=0, second=0).isoformat()
        else:
            # Default to last 7 days
            start_date = (now - timedelta(days=7)).replace(hour=0, minute=0, second=0).isoformat()
        
        return (start_date, end_date)
    
    def get_default_start_date(self):
        """
        Get default start date (7 days ago).
        
        Returns:
            str: Default start date
        """
        return (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
    
    def get_default_end_date(self):
        """
        Get default end date (today).
        
        Returns:
            str: Default end date
        """
        return datetime.now().strftime("%Y-%m-%d")
    
    def format_timestamp(self, timestamp):
        """
        Format a timestamp for display.
        
        Args:
            timestamp (str): The timestamp to format
            
        Returns:
            str: Formatted timestamp
        """
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return timestamp