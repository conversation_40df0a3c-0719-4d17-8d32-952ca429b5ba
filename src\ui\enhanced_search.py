"""
Enhanced Global Search Component
Provides autocomplete, suggestions, and smart search functionality
"""
import flet as ft
from typing import List, Dict, Any, Callable, Optional
import re
from datetime import datetime


class SearchResult:
    """Represents a search result item."""
    
    def __init__(self, title: str, subtitle: str, category: str, route: str, data: Dict[str, Any] = None):
        self.title = title
        self.subtitle = subtitle
        self.category = category
        self.route = route
        self.data = data or {}
        self.relevance_score = 0


class EnhancedSearchComponent:
    """
    Enhanced search component with autocomplete and smart suggestions.
    """
    
    def __init__(self, on_navigate: Callable[[str, Dict], None] = None):
        """
        Initialize the enhanced search component.
        
        Args:
            on_navigate: Callback function for navigation
        """
        self.on_navigate = on_navigate
        self.search_data = self._initialize_search_data()
        self.search_field = None
        self.results_container = None
        self.search_overlay = None
        self.is_open = False
        
    def _initialize_search_data(self) -> List[SearchResult]:
        """Initialize searchable data."""
        return [
            # Navigation items
            SearchResult("Dashboard", "System overview and quick actions", "Navigation", "dashboard"),
            SearchResult("Inventory", "Manage chemicals and equipment", "Navigation", "inventory"),
            SearchResult("Experiments", "Track research projects", "Navigation", "experiments"),
            SearchResult("Scheduling", "Book resources and facilities", "Navigation", "scheduling"),
            SearchResult("Reports", "Analytics and reporting", "Navigation", "reports"),
            SearchResult("Users", "User management and permissions", "Navigation", "users"),
            SearchResult("Settings", "System configuration", "Navigation", "settings"),
            
            # Quick actions
            SearchResult("Add Inventory Item", "Create new inventory entry", "Action", "inventory", {"action": "add"}),
            SearchResult("New Experiment", "Start a new research project", "Action", "experiments", {"action": "new"}),
            SearchResult("Book Equipment", "Schedule equipment usage", "Action", "scheduling", {"action": "book"}),
            SearchResult("Generate Report", "Create analytics report", "Action", "reports", {"action": "generate"}),
            SearchResult("Add User", "Create new user account", "Action", "users", {"action": "add"}),
            
            # Common searches
            SearchResult("Low Stock Items", "View items running low", "Filter", "inventory", {"filter": "low_stock"}),
            SearchResult("Active Experiments", "View ongoing research", "Filter", "experiments", {"filter": "active"}),
            SearchResult("Today's Schedule", "View today's bookings", "Filter", "scheduling", {"filter": "today"}),
            SearchResult("Recent Reports", "View latest reports", "Filter", "reports", {"filter": "recent"}),
            SearchResult("Online Users", "View active users", "Filter", "users", {"filter": "online"}),
            
            # Help topics
            SearchResult("How to add inventory", "Guide for adding new items", "Help", "help", {"topic": "inventory_add"}),
            SearchResult("Experiment workflow", "Guide for experiment management", "Help", "help", {"topic": "experiment_workflow"}),
            SearchResult("Booking equipment", "Guide for resource scheduling", "Help", "help", {"topic": "booking_guide"}),
            SearchResult("User permissions", "Guide for managing user roles", "Help", "help", {"topic": "user_permissions"}),
            SearchResult("System settings", "Guide for configuration", "Help", "help", {"topic": "system_settings"}),
        ]
    
    def create_search_component(self) -> ft.Container:
        """Create the main search component."""
        self.search_field = ft.TextField(
            hint_text="Search anything... (Ctrl+F)",
            prefix_icon=ft.icons.SEARCH,
            border_radius=25,
            filled=True,
            dense=True,
            on_change=self._on_search_change,
            on_focus=self._on_search_focus,
            on_blur=self._on_search_blur,
            on_submit=self._on_search_submit,
            width=300,
        )
        
        self.results_container = ft.Container(
            content=ft.Column([], spacing=0),
            visible=False,
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.colors.GREY_300),
            padding=ft.padding.symmetric(vertical=8),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.colors.with_opacity(0.15, ft.colors.BLACK),
                offset=ft.Offset(0, 4)
            )
        )
        
        return ft.Container(
            content=ft.Column([
                self.search_field,
                self.results_container,
            ], spacing=4),
            width=300,
        )
    
    def create_search_overlay(self, page: ft.Page) -> ft.Container:
        """Create full-screen search overlay."""
        search_field_large = ft.TextField(
            hint_text="Search the laboratory management system...",
            prefix_icon=ft.icons.SEARCH,
            suffix=ft.IconButton(
                icon=ft.icons.CLOSE,
                tooltip="Close search (Esc)",
                on_click=self._close_overlay
            ),
            border_radius=12,
            filled=True,
            text_size=18,
            height=60,
            on_change=self._on_overlay_search_change,
            on_submit=self._on_overlay_search_submit,
            autofocus=True,
        )
        
        results_large = ft.Container(
            content=ft.Column([], spacing=8),
            padding=20,
            expand=True,
        )
        
        self.search_overlay = ft.Container(
            content=ft.Column([
                ft.Container(
                    content=ft.Row([
                        ft.Icon(ft.icons.SEARCH, size=32, color=ft.colors.BLUE),
                        ft.Text("Global Search", size=24, weight=ft.FontWeight.BOLD),
                        ft.Container(expand=True),
                        ft.IconButton(
                            icon=ft.icons.CLOSE,
                            tooltip="Close (Esc)",
                            on_click=self._close_overlay,
                            icon_size=24,
                        ),
                    ]),
                    padding=ft.padding.symmetric(horizontal=20, vertical=10),
                ),
                ft.Divider(height=1),
                ft.Container(
                    content=search_field_large,
                    padding=ft.padding.symmetric(horizontal=20, vertical=10),
                ),
                results_large,
            ], spacing=0),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            margin=ft.margin.all(20),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=20,
                color=ft.colors.with_opacity(0.3, ft.colors.BLACK),
                offset=ft.Offset(0, 8)
            ),
            visible=False,
        )
        
        overlay_background = ft.Container(
            content=self.search_overlay,
            bgcolor=ft.colors.with_opacity(0.8, ft.colors.BLACK),
            expand=True,
            on_click=self._close_overlay,
            visible=False,
        )
        
        return overlay_background
    
    def _on_search_change(self, e):
        """Handle search input changes."""
        query = e.control.value.strip()
        
        if len(query) >= 2:
            results = self._search(query)
            self._update_results(results[:5])  # Show top 5 results
            self.results_container.visible = True
        else:
            self.results_container.visible = False
        
        e.page.update()
    
    def _on_search_focus(self, e):
        """Handle search field focus."""
        if self.search_field.value and len(self.search_field.value) >= 2:
            self.results_container.visible = True
            e.page.update()
    
    def _on_search_blur(self, e):
        """Handle search field blur."""
        # Delay hiding to allow clicking on results
        def hide_results():
            import time
            time.sleep(0.2)
            if hasattr(self, 'results_container'):
                self.results_container.visible = False
                e.page.update()
        
        import threading
        threading.Thread(target=hide_results, daemon=True).start()
    
    def _on_search_submit(self, e):
        """Handle search submission."""
        query = e.control.value.strip()
        if query:
            results = self._search(query)
            if results:
                self._navigate_to_result(results[0])
    
    def _on_overlay_search_change(self, e):
        """Handle overlay search changes."""
        query = e.control.value.strip()
        
        if len(query) >= 1:
            results = self._search(query)
            self._update_overlay_results(results[:10])  # Show top 10 results
    
    def _on_overlay_search_submit(self, e):
        """Handle overlay search submission."""
        query = e.control.value.strip()
        if query:
            results = self._search(query)
            if results:
                self._navigate_to_result(results[0])
                self._close_overlay(e)
    
    def _search(self, query: str) -> List[SearchResult]:
        """Perform search and return ranked results."""
        query_lower = query.lower()
        results = []
        
        for item in self.search_data:
            score = 0
            
            # Exact title match (highest priority)
            if item.title.lower() == query_lower:
                score += 100
            # Title starts with query
            elif item.title.lower().startswith(query_lower):
                score += 80
            # Title contains query
            elif query_lower in item.title.lower():
                score += 60
            # Subtitle contains query
            elif query_lower in item.subtitle.lower():
                score += 40
            # Category matches
            elif query_lower in item.category.lower():
                score += 20
            
            # Boost scores for certain categories
            if item.category == "Navigation":
                score += 10
            elif item.category == "Action":
                score += 5
            
            if score > 0:
                item.relevance_score = score
                results.append(item)
        
        # Sort by relevance score
        results.sort(key=lambda x: x.relevance_score, reverse=True)
        return results
    
    def _update_results(self, results: List[SearchResult]):
        """Update search results display."""
        result_controls = []
        
        for result in results:
            result_item = ft.Container(
                content=ft.Row([
                    self._get_category_icon(result.category),
                    ft.Column([
                        ft.Text(result.title, weight=ft.FontWeight.BOLD, size=14),
                        ft.Text(result.subtitle, size=12, color=ft.colors.GREY_600),
                    ], spacing=2, expand=True),
                    ft.Icon(ft.icons.ARROW_FORWARD_IOS, size=12, color=ft.colors.GREY_400),
                ], spacing=10),
                padding=ft.padding.symmetric(horizontal=12, vertical=8),
                on_click=lambda e, r=result: self._navigate_to_result(r),
                ink=True,
                border_radius=8,
            )
            result_controls.append(result_item)
        
        if not results:
            result_controls.append(
                ft.Container(
                    content=ft.Text("No results found", color=ft.colors.GREY_600),
                    padding=12,
                )
            )
        
        self.results_container.content.controls = result_controls
    
    def _update_overlay_results(self, results: List[SearchResult]):
        """Update overlay search results."""
        if not hasattr(self.search_overlay.content.controls[3], 'content'):
            return
            
        result_controls = []
        
        # Group results by category
        categories = {}
        for result in results:
            if result.category not in categories:
                categories[result.category] = []
            categories[result.category].append(result)
        
        for category, items in categories.items():
            # Category header
            result_controls.append(
                ft.Container(
                    content=ft.Text(category, weight=ft.FontWeight.BOLD, color=ft.colors.BLUE),
                    padding=ft.padding.only(bottom=8),
                )
            )
            
            # Category items
            for item in items:
                result_item = ft.Container(
                    content=ft.Row([
                        self._get_category_icon(item.category),
                        ft.Column([
                            ft.Text(item.title, weight=ft.FontWeight.BOLD),
                            ft.Text(item.subtitle, color=ft.colors.GREY_600),
                        ], spacing=4, expand=True),
                        ft.Icon(ft.icons.ARROW_FORWARD, color=ft.colors.BLUE),
                    ], spacing=15),
                    padding=15,
                    border_radius=8,
                    bgcolor=ft.colors.GREY_50,
                    on_click=lambda e, r=item: self._navigate_to_result(r),
                    ink=True,
                )
                result_controls.append(result_item)
            
            result_controls.append(ft.Container(height=10))  # Spacing between categories
        
        if not results:
            result_controls.append(
                ft.Container(
                    content=ft.Column([
                        ft.Icon(ft.icons.SEARCH_OFF, size=48, color=ft.colors.GREY_400),
                        ft.Text("No results found", size=18, color=ft.colors.GREY_600),
                        ft.Text("Try different keywords or browse categories", color=ft.colors.GREY_500),
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=10),
                    padding=40,
                )
            )
        
        self.search_overlay.content.controls[3].content.controls = result_controls
    
    def _get_category_icon(self, category: str) -> ft.Icon:
        """Get icon for category."""
        icons = {
            "Navigation": ft.icons.NAVIGATION,
            "Action": ft.icons.PLAY_ARROW,
            "Filter": ft.icons.FILTER_LIST,
            "Help": ft.icons.HELP_OUTLINE,
        }
        return ft.Icon(icons.get(category, ft.icons.SEARCH), size=20, color=ft.colors.BLUE)
    
    def _navigate_to_result(self, result: SearchResult):
        """Navigate to search result."""
        self.results_container.visible = False
        self.search_field.value = ""
        
        if self.on_navigate:
            self.on_navigate(result.route, result.data)
    
    def show_overlay(self, page: ft.Page):
        """Show the search overlay."""
        if hasattr(page, 'overlay') and self.search_overlay:
            overlay_bg = None
            for control in page.overlay:
                if hasattr(control, 'content') and control.content == self.search_overlay:
                    overlay_bg = control
                    break
            
            if overlay_bg:
                overlay_bg.visible = True
                self.search_overlay.visible = True
                self.is_open = True
                page.update()
    
    def _close_overlay(self, e):
        """Close the search overlay."""
        if hasattr(e, 'page'):
            page = e.page
            for control in page.overlay:
                if hasattr(control, 'content') and control.content == self.search_overlay:
                    control.visible = False
                    break
            
            self.search_overlay.visible = False
            self.is_open = False
            page.update()
    
    def handle_keyboard_shortcut(self, page: ft.Page):
        """Handle Ctrl+F keyboard shortcut."""
        self.show_overlay(page)
