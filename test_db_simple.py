#!/usr/bin/env python
"""
Test script for the DatabaseManager class.
"""
import os
import sys
import logging
import sqlite3

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(project_root, 'db_test.log'))
    ]
)

logger = logging.getLogger('db_test')

def main():
    """
    Test the database functionality directly.
    """
    logger.info("Starting database test")
    
    # Create a test database
    test_db_path = os.path.join(project_root, 'data', 'test_db.db')
    
    # Remove the test database if it exists
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
        logger.info(f"Removed existing test database: {test_db_path}")
    
    # Ensure the data directory exists
    os.makedirs(os.path.dirname(test_db_path), exist_ok=True)
    
    try:
        # Connect to the database
        conn = sqlite3.connect(test_db_path)
        cursor = conn.cursor()
        
        # Create a simple table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_table (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                value TEXT NOT NULL
            )
        ''')
        
        # Insert some data
        cursor.execute('''
            INSERT INTO test_table (name, value)
            VALUES (?, ?)
        ''', ("test1", "value1"))
        
        cursor.execute('''
            INSERT INTO test_table (name, value)
            VALUES (?, ?)
        ''', ("test2", "value2"))
        
        # Commit the changes
        conn.commit()
        
        # Query the data
        cursor.execute('''
            SELECT * FROM test_table
        ''')
        
        rows = cursor.fetchall()
        logger.info(f"Number of rows: {len(rows)}")
        
        for row in rows:
            logger.info(f"Row: {row}")
        
        logger.info("Database test completed successfully")
    except sqlite3.Error as e:
        logger.error(f"Database error: {str(e)}")
    finally:
        # Close the connection
        if conn:
            conn.close()

if __name__ == "__main__":
    main()