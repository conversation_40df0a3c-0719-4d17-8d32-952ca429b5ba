# Science Laboratory Management System
# API Documentation

## Table of Contents
1. [Introduction](#introduction)
2. [Authentication](#authentication)
3. [API Endpoints](#api-endpoints)
   - [User Management](#user-management)
   - [Inventory Management](#inventory-management)
   - [Equipment Management](#equipment-management)
   - [Lab Scheduling](#lab-scheduling)
   - [Experiment Tracking](#experiment-tracking)
   - [Reports](#reports)
4. [Data Models](#data-models)
5. [Error Handling](#error-handling)
6. [Rate Limiting](#rate-limiting)
7. [Webhooks](#webhooks)
8. [Examples](#examples)
9. [SDK and Client Libraries](#sdk-and-client-libraries)

---

## Introduction

The Science Laboratory Management System API provides programmatic access to the system's functionality, allowing developers to integrate with the system, build custom applications, or automate workflows.

### API Version

Current API version: v1

### Base URL

```
https://your-domain.com/api/v1
```

### Content Type

All requests and responses use JSON format. Requests should include the header:

```
Content-Type: application/json
```

### Response Format

All API responses follow a standard format:

```json
{
  "status": "success",
  "data": { ... },
  "message": "Operation successful"
}
```

Or in case of an error:

```json
{
  "status": "error",
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description"
  }
}
```

---

## Authentication

### API Keys

The API uses API keys for authentication. To obtain an API key:

1. Log in to the system as an administrator
2. Navigate to "Administration" > "API" > "API Keys"
3. Click "Generate New Key"
4. Specify the permissions and expiration for the key
5. Save the generated key securely

### Authentication Methods

#### API Key in Header

Include your API key in the `X-API-Key` header:

```
X-API-Key: your-api-key
```

#### API Key in Query Parameter

Alternatively, include your API key as a query parameter:

```
https://your-domain.com/api/v1/endpoint?api_key=your-api-key
```

### JWT Authentication

For user-specific operations, JWT (JSON Web Token) authentication is available:

1. Obtain a token by authenticating with username and password:

```
POST /api/v1/auth/login
{
  "username": "your-username",
  "password": "your-password"
}
```

2. Include the token in the `Authorization` header:

```
Authorization: Bearer your-jwt-token
```

### Token Expiration and Refresh

JWT tokens expire after 24 hours. To refresh a token:

```
POST /api/v1/auth/refresh
{
  "refresh_token": "your-refresh-token"
}
```

---

## API Endpoints

### User Management

#### Get All Users

```
GET /api/v1/users
```

Query Parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `role`: Filter by role
- `department`: Filter by department
- `search`: Search term for username or full name

Response:
```json
{
  "status": "success",
  "data": {
    "users": [
      {
        "id": 1,
        "username": "john.doe",
        "email": "<EMAIL>",
        "full_name": "John Doe",
        "role": "researcher",
        "department": "Chemistry",
        "created_at": "2023-01-15T10:30:00Z",
        "last_login": "2023-02-01T08:45:22Z",
        "is_active": true
      },
      ...
    ],
    "pagination": {
      "total": 45,
      "page": 1,
      "limit": 20,
      "pages": 3
    }
  }
}
```

#### Get User by ID

```
GET /api/v1/users/{user_id}
```

Response:
```json
{
  "status": "success",
  "data": {
    "user": {
      "id": 1,
      "username": "john.doe",
      "email": "<EMAIL>",
      "full_name": "John Doe",
      "role": "researcher",
      "department": "Chemistry",
      "created_at": "2023-01-15T10:30:00Z",
      "last_login": "2023-02-01T08:45:22Z",
      "is_active": true,
      "permissions": ["lab.access", "equipment.reserve", "inventory.view"]
    }
  }
}
```

#### Create User

```
POST /api/v1/users
```

Request:
```json
{
  "username": "jane.smith",
  "email": "<EMAIL>",
  "full_name": "Jane Smith",
  "role": "lab_assistant",
  "department": "Biology",
  "password": "secure-password",
  "is_active": true
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "user": {
      "id": 46,
      "username": "jane.smith",
      "email": "<EMAIL>",
      "full_name": "Jane Smith",
      "role": "lab_assistant",
      "department": "Biology",
      "created_at": "2023-02-10T14:22:30Z",
      "is_active": true
    }
  },
  "message": "User created successfully"
}
```

#### Update User

```
PUT /api/v1/users/{user_id}
```

Request:
```json
{
  "email": "<EMAIL>",
  "full_name": "Jane A. Smith",
  "department": "Molecular Biology"
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "user": {
      "id": 46,
      "username": "jane.smith",
      "email": "<EMAIL>",
      "full_name": "Jane A. Smith",
      "role": "lab_assistant",
      "department": "Molecular Biology",
      "created_at": "2023-02-10T14:22:30Z",
      "updated_at": "2023-02-10T15:30:45Z",
      "is_active": true
    }
  },
  "message": "User updated successfully"
}
```

#### Delete User

```
DELETE /api/v1/users/{user_id}
```

Response:
```json
{
  "status": "success",
  "message": "User deleted successfully"
}
```

### Inventory Management

#### Get All Inventory Items

```
GET /api/v1/inventory
```

Query Parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `category`: Filter by category
- `location`: Filter by storage location
- `status`: Filter by status (available, low, out_of_stock)
- `search`: Search term for item name or description

Response:
```json
{
  "status": "success",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "Sodium Chloride",
        "category": "Chemicals",
        "subcategory": "Salts",
        "quantity": 500,
        "unit": "g",
        "location": "Storage Room A, Shelf 2",
        "min_quantity": 100,
        "status": "available",
        "last_updated": "2023-01-20T09:15:30Z"
      },
      ...
    ],
    "pagination": {
      "total": 120,
      "page": 1,
      "limit": 20,
      "pages": 6
    }
  }
}
```

#### Get Inventory Item by ID

```
GET /api/v1/inventory/{item_id}
```

Response:
```json
{
  "status": "success",
  "data": {
    "item": {
      "id": 1,
      "name": "Sodium Chloride",
      "category": "Chemicals",
      "subcategory": "Salts",
      "description": "Pure sodium chloride (NaCl), fine powder",
      "quantity": 500,
      "unit": "g",
      "location": "Storage Room A, Shelf 2",
      "min_quantity": 100,
      "status": "available",
      "supplier": "Lab Supplies Inc.",
      "catalog_number": "SC-1234",
      "cost": 15.99,
      "currency": "USD",
      "expiry_date": "2025-12-31",
      "date_added": "2022-11-05T10:00:00Z",
      "last_updated": "2023-01-20T09:15:30Z",
      "updated_by": "john.doe",
      "barcode": "ITM-1234567890",
      "safety_info": "Avoid contact with eyes. Store in a cool, dry place.",
      "attachments": [
        {
          "id": 1,
          "name": "SDS_NaCl.pdf",
          "type": "application/pdf",
          "size": 256000,
          "url": "/api/v1/files/1"
        }
      ]
    }
  }
}
```

#### Create Inventory Item

```
POST /api/v1/inventory
```

Request:
```json
{
  "name": "Potassium Chloride",
  "category": "Chemicals",
  "subcategory": "Salts",
  "description": "Pure potassium chloride (KCl), fine powder",
  "quantity": 250,
  "unit": "g",
  "location": "Storage Room A, Shelf 2",
  "min_quantity": 50,
  "supplier": "Lab Supplies Inc.",
  "catalog_number": "PC-5678",
  "cost": 18.99,
  "currency": "USD",
  "expiry_date": "2025-12-31",
  "safety_info": "Avoid contact with eyes. Store in a cool, dry place."
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "item": {
      "id": 121,
      "name": "Potassium Chloride",
      "category": "Chemicals",
      "subcategory": "Salts",
      "description": "Pure potassium chloride (KCl), fine powder",
      "quantity": 250,
      "unit": "g",
      "location": "Storage Room A, Shelf 2",
      "min_quantity": 50,
      "status": "available",
      "supplier": "Lab Supplies Inc.",
      "catalog_number": "PC-5678",
      "cost": 18.99,
      "currency": "USD",
      "expiry_date": "2025-12-31",
      "date_added": "2023-02-10T16:00:00Z",
      "last_updated": "2023-02-10T16:00:00Z",
      "updated_by": "jane.smith",
      "barcode": "ITM-1234567891",
      "safety_info": "Avoid contact with eyes. Store in a cool, dry place."
    }
  },
  "message": "Inventory item created successfully"
}
```

#### Update Inventory Item

```
PUT /api/v1/inventory/{item_id}
```

Request:
```json
{
  "quantity": 200,
  "location": "Storage Room B, Shelf 1",
  "min_quantity": 75
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "item": {
      "id": 121,
      "name": "Potassium Chloride",
      "category": "Chemicals",
      "subcategory": "Salts",
      "quantity": 200,
      "unit": "g",
      "location": "Storage Room B, Shelf 1",
      "min_quantity": 75,
      "status": "available",
      "last_updated": "2023-02-10T16:30:00Z",
      "updated_by": "jane.smith"
    }
  },
  "message": "Inventory item updated successfully"
}
```

#### Update Inventory Quantity

```
POST /api/v1/inventory/{item_id}/quantity
```

Request:
```json
{
  "change": -50,
  "reason": "Used in experiment E-1234",
  "notes": "Used by John Doe"
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "item": {
      "id": 121,
      "name": "Potassium Chloride",
      "previous_quantity": 200,
      "new_quantity": 150,
      "unit": "g",
      "status": "available",
      "last_updated": "2023-02-10T17:00:00Z",
      "updated_by": "jane.smith"
    },
    "transaction": {
      "id": 456,
      "item_id": 121,
      "change": -50,
      "reason": "Used in experiment E-1234",
      "notes": "Used by John Doe",
      "timestamp": "2023-02-10T17:00:00Z",
      "user": "jane.smith"
    }
  },
  "message": "Inventory quantity updated successfully"
}
```

#### Delete Inventory Item

```
DELETE /api/v1/inventory/{item_id}
```

Response:
```json
{
  "status": "success",
  "message": "Inventory item deleted successfully"
}
```

### Equipment Management

#### Get All Equipment

```
GET /api/v1/equipment
```

Query Parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `category`: Filter by category
- `location`: Filter by location
- `status`: Filter by status (available, in_use, maintenance, out_of_order)
- `search`: Search term for equipment name or description

Response:
```json
{
  "status": "success",
  "data": {
    "equipment": [
      {
        "id": 1,
        "name": "Microscope XYZ-100",
        "category": "Optical",
        "location": "Lab Room 101",
        "status": "available",
        "condition": "good",
        "last_maintenance": "2023-01-05T10:00:00Z",
        "next_maintenance": "2023-04-05T10:00:00Z"
      },
      ...
    ],
    "pagination": {
      "total": 35,
      "page": 1,
      "limit": 20,
      "pages": 2
    }
  }
}
```

#### Get Equipment by ID

```
GET /api/v1/equipment/{equipment_id}
```

Response:
```json
{
  "status": "success",
  "data": {
    "equipment": {
      "id": 1,
      "name": "Microscope XYZ-100",
      "model": "XYZ-100",
      "serial_number": "MSC-12345678",
      "manufacturer": "Lab Instruments Inc.",
      "category": "Optical",
      "subcategory": "Microscopes",
      "description": "High-resolution optical microscope with digital imaging capabilities",
      "location": "Lab Room 101",
      "status": "available",
      "condition": "good",
      "purchase_date": "2022-03-15",
      "purchase_cost": 5000.00,
      "warranty_expiry": "2025-03-15",
      "last_maintenance": "2023-01-05T10:00:00Z",
      "next_maintenance": "2023-04-05T10:00:00Z",
      "last_calibration": "2023-01-05T10:00:00Z",
      "next_calibration": "2023-07-05T10:00:00Z",
      "maintenance_interval": 90,
      "calibration_interval": 180,
      "notes": "Handle with care. Requires special cleaning procedures.",
      "attachments": [
        {
          "id": 5,
          "name": "Microscope_Manual.pdf",
          "type": "application/pdf",
          "size": 3500000,
          "url": "/api/v1/files/5"
        }
      ],
      "qr_code": "/api/v1/equipment/1/qrcode"
    }
  }
}
```

#### Create Equipment

```
POST /api/v1/equipment
```

Request:
```json
{
  "name": "Centrifuge ABC-200",
  "model": "ABC-200",
  "serial_number": "CTF-87654321",
  "manufacturer": "Lab Instruments Inc.",
  "category": "Separation",
  "subcategory": "Centrifuges",
  "description": "High-speed refrigerated centrifuge",
  "location": "Lab Room 102",
  "status": "available",
  "condition": "excellent",
  "purchase_date": "2023-01-10",
  "purchase_cost": 8500.00,
  "warranty_expiry": "2026-01-10",
  "maintenance_interval": 120,
  "calibration_interval": 180,
  "notes": "Maximum speed: 15,000 RPM. Temperature range: -10°C to +40°C."
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "equipment": {
      "id": 36,
      "name": "Centrifuge ABC-200",
      "model": "ABC-200",
      "serial_number": "CTF-87654321",
      "manufacturer": "Lab Instruments Inc.",
      "category": "Separation",
      "subcategory": "Centrifuges",
      "description": "High-speed refrigerated centrifuge",
      "location": "Lab Room 102",
      "status": "available",
      "condition": "excellent",
      "purchase_date": "2023-01-10",
      "purchase_cost": 8500.00,
      "warranty_expiry": "2026-01-10",
      "last_maintenance": null,
      "next_maintenance": "2023-06-10T00:00:00Z",
      "last_calibration": null,
      "next_calibration": "2023-07-10T00:00:00Z",
      "maintenance_interval": 120,
      "calibration_interval": 180,
      "notes": "Maximum speed: 15,000 RPM. Temperature range: -10°C to +40°C.",
      "qr_code": "/api/v1/equipment/36/qrcode"
    }
  },
  "message": "Equipment created successfully"
}
```

#### Update Equipment

```
PUT /api/v1/equipment/{equipment_id}
```

Request:
```json
{
  "location": "Lab Room 103",
  "status": "maintenance",
  "condition": "needs_repair",
  "notes": "Unusual noise during operation. Scheduled for maintenance."
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "equipment": {
      "id": 36,
      "name": "Centrifuge ABC-200",
      "location": "Lab Room 103",
      "status": "maintenance",
      "condition": "needs_repair",
      "notes": "Unusual noise during operation. Scheduled for maintenance.",
      "last_updated": "2023-02-10T18:30:00Z",
      "updated_by": "jane.smith"
    }
  },
  "message": "Equipment updated successfully"
}
```

#### Add Maintenance Record

```
POST /api/v1/equipment/{equipment_id}/maintenance
```

Request:
```json
{
  "maintenance_type": "repair",
  "maintenance_date": "2023-02-15T10:00:00Z",
  "performed_by": "John Technician",
  "cost": 350.00,
  "description": "Replaced worn bearing. Cleaned and lubricated moving parts.",
  "next_maintenance_date": "2023-06-15T10:00:00Z"
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "maintenance": {
      "id": 123,
      "equipment_id": 36,
      "maintenance_type": "repair",
      "maintenance_date": "2023-02-15T10:00:00Z",
      "performed_by": "John Technician",
      "cost": 350.00,
      "description": "Replaced worn bearing. Cleaned and lubricated moving parts.",
      "status": "completed",
      "next_maintenance_date": "2023-06-15T10:00:00Z",
      "created_at": "2023-02-10T18:45:00Z",
      "created_by": "jane.smith"
    },
    "equipment": {
      "id": 36,
      "name": "Centrifuge ABC-200",
      "status": "available",
      "condition": "good",
      "last_maintenance": "2023-02-15T10:00:00Z",
      "next_maintenance": "2023-06-15T10:00:00Z"
    }
  },
  "message": "Maintenance record added successfully"
}
```

#### Reserve Equipment

```
POST /api/v1/equipment/{equipment_id}/reserve
```

Request:
```json
{
  "user_id": 1,
  "start_time": "2023-02-20T09:00:00Z",
  "end_time": "2023-02-20T12:00:00Z",
  "purpose": "Experiment E-5678"
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "reservation": {
      "id": 789,
      "equipment_id": 36,
      "equipment_name": "Centrifuge ABC-200",
      "user_id": 1,
      "user_name": "John Doe",
      "start_time": "2023-02-20T09:00:00Z",
      "end_time": "2023-02-20T12:00:00Z",
      "purpose": "Experiment E-5678",
      "status": "approved",
      "created_at": "2023-02-10T19:00:00Z"
    }
  },
  "message": "Equipment reserved successfully"
}
```

#### Get Equipment QR Code

```
GET /api/v1/equipment/{equipment_id}/qrcode
```

Query Parameters:
- `size`: QR code size in pixels (default: 200)
- `format`: Image format (png, jpg, svg) (default: png)

Response:
Binary image data with appropriate content type header.

#### Delete Equipment

```
DELETE /api/v1/equipment/{equipment_id}
```

Response:
```json
{
  "status": "success",
  "message": "Equipment deleted successfully"
}
```

### Lab Scheduling

#### Get Lab Schedule

```
GET /api/v1/labs/schedule
```

Query Parameters:
- `lab_id`: Filter by lab ID
- `start_date`: Start date (YYYY-MM-DD)
- `end_date`: End date (YYYY-MM-DD)
- `user_id`: Filter by user ID

Response:
```json
{
  "status": "success",
  "data": {
    "schedule": [
      {
        "id": 101,
        "lab_id": 1,
        "lab_name": "Chemistry Lab 101",
        "user_id": 1,
        "user_name": "John Doe",
        "start_time": "2023-02-15T09:00:00Z",
        "end_time": "2023-02-15T11:00:00Z",
        "purpose": "Organic synthesis experiment",
        "status": "approved",
        "created_at": "2023-02-01T10:30:00Z"
      },
      ...
    ]
  }
}
```

#### Get Lab Rooms

```
GET /api/v1/labs/rooms
```

Query Parameters:
- `capacity`: Filter by minimum capacity
- `equipment`: Filter by available equipment
- `available`: Filter by availability (true/false)
- `search`: Search term for room name or number

Response:
```json
{
  "status": "success",
  "data": {
    "rooms": [
      {
        "id": 1,
        "name": "Chemistry Lab",
        "room_number": "101",
        "building": "Science Building",
        "floor": 1,
        "capacity": 24,
        "description": "General chemistry laboratory with fume hoods",
        "equipment": ["Fume Hoods", "Analytical Balances", "pH Meters"],
        "status": "available"
      },
      ...
    ]
  }
}
```

#### Book Lab

```
POST /api/v1/labs/book
```

Request:
```json
{
  "lab_id": 1,
  "user_id": 1,
  "start_time": "2023-02-22T13:00:00Z",
  "end_time": "2023-02-22T16:00:00Z",
  "purpose": "Advanced organic synthesis lab",
  "participants": [2, 3, 4],
  "equipment_needed": ["Rotary Evaporator", "HPLC"]
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "booking": {
      "id": 102,
      "lab_id": 1,
      "lab_name": "Chemistry Lab 101",
      "user_id": 1,
      "user_name": "John Doe",
      "start_time": "2023-02-22T13:00:00Z",
      "end_time": "2023-02-22T16:00:00Z",
      "purpose": "Advanced organic synthesis lab",
      "participants": [
        {"id": 2, "name": "Jane Smith"},
        {"id": 3, "name": "Bob Johnson"},
        {"id": 4, "name": "Alice Williams"}
      ],
      "equipment_needed": ["Rotary Evaporator", "HPLC"],
      "status": "pending",
      "created_at": "2023-02-10T20:00:00Z"
    }
  },
  "message": "Lab booking request submitted successfully"
}
```

#### Update Booking Status

```
PUT /api/v1/labs/bookings/{booking_id}/status
```

Request:
```json
{
  "status": "approved",
  "notes": "Approved by lab manager"
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "booking": {
      "id": 102,
      "status": "approved",
      "notes": "Approved by lab manager",
      "updated_at": "2023-02-10T20:15:00Z",
      "updated_by": "lab.manager"
    }
  },
  "message": "Booking status updated successfully"
}
```

#### Cancel Booking

```
DELETE /api/v1/labs/bookings/{booking_id}
```

Response:
```json
{
  "status": "success",
  "message": "Booking cancelled successfully"
}
```

### Experiment Tracking

#### Get All Experiments

```
GET /api/v1/experiments
```

Query Parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `user_id`: Filter by user ID
- `status`: Filter by status (planned, in_progress, completed, archived)
- `search`: Search term for experiment title or description

Response:
```json
{
  "status": "success",
  "data": {
    "experiments": [
      {
        "id": "E-1234",
        "title": "Effect of Temperature on Enzyme Activity",
        "user_id": 1,
        "user_name": "John Doe",
        "status": "in_progress",
        "start_date": "2023-02-01T09:00:00Z",
        "end_date": null,
        "last_updated": "2023-02-10T11:30:00Z"
      },
      ...
    ],
    "pagination": {
      "total": 28,
      "page": 1,
      "limit": 20,
      "pages": 2
    }
  }
}
```

#### Get Experiment by ID

```
GET /api/v1/experiments/{experiment_id}
```

Response:
```json
{
  "status": "success",
  "data": {
    "experiment": {
      "id": "E-1234",
      "title": "Effect of Temperature on Enzyme Activity",
      "description": "Investigating how different temperatures affect the rate of enzyme-catalyzed reactions",
      "user_id": 1,
      "user_name": "John Doe",
      "collaborators": [
        {"id": 2, "name": "Jane Smith"},
        {"id": 3, "name": "Bob Johnson"}
      ],
      "status": "in_progress",
      "start_date": "2023-02-01T09:00:00Z",
      "end_date": null,
      "expected_end_date": "2023-02-28T17:00:00Z",
      "current_stage": "Data Collection",
      "stages": [
        {"name": "Planning", "status": "completed", "completed_at": "2023-02-01T12:00:00Z"},
        {"name": "Setup", "status": "completed", "completed_at": "2023-02-05T15:30:00Z"},
        {"name": "Data Collection", "status": "in_progress", "completed_at": null},
        {"name": "Analysis", "status": "pending", "completed_at": null},
        {"name": "Conclusion", "status": "pending", "completed_at": null}
      ],
      "materials": [
        {"id": 1, "name": "Hydrogen Peroxide", "quantity": "100", "unit": "ml"},
        {"id": 5, "name": "Catalase", "quantity": "10", "unit": "g"}
      ],
      "equipment": [
        {"id": 3, "name": "Water Bath", "reservation_id": 456},
        {"id": 8, "name": "Spectrophotometer", "reservation_id": 457}
      ],
      "notes": "Temperature range: 10°C to 50°C in 10°C increments",
      "attachments": [
        {
          "id": 10,
          "name": "Protocol.pdf",
          "type": "application/pdf",
          "size": 1500000,
          "url": "/api/v1/files/10"
        }
      ],
      "created_at": "2023-01-25T14:00:00Z",
      "last_updated": "2023-02-10T11:30:00Z"
    }
  }
}
```

#### Create Experiment

```
POST /api/v1/experiments
```

Request:
```json
{
  "title": "Antibiotic Resistance in Soil Bacteria",
  "description": "Investigating the prevalence of antibiotic resistance in soil bacteria from different environments",
  "user_id": 1,
  "collaborators": [2, 4],
  "expected_end_date": "2023-03-15T17:00:00Z",
  "stages": [
    {"name": "Sample Collection", "status": "pending"},
    {"name": "Isolation", "status": "pending"},
    {"name": "Antibiotic Testing", "status": "pending"},
    {"name": "Data Analysis", "status": "pending"},
    {"name": "Report Writing", "status": "pending"}
  ],
  "materials": [
    {"id": 15, "name": "Nutrient Agar", "quantity": "500", "unit": "g"},
    {"id": 22, "name": "Antibiotic Discs", "quantity": "100", "unit": "pcs"}
  ],
  "notes": "Collect soil samples from urban, agricultural, and forest environments"
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "experiment": {
      "id": "E-1235",
      "title": "Antibiotic Resistance in Soil Bacteria",
      "user_id": 1,
      "user_name": "John Doe",
      "status": "planned",
      "start_date": "2023-02-10T20:30:00Z",
      "expected_end_date": "2023-03-15T17:00:00Z",
      "created_at": "2023-02-10T20:30:00Z"
    }
  },
  "message": "Experiment created successfully"
}
```

#### Update Experiment

```
PUT /api/v1/experiments/{experiment_id}
```

Request:
```json
{
  "status": "in_progress",
  "current_stage": "Sample Collection",
  "notes": "Collected samples from 3 urban, 3 agricultural, and 3 forest locations"
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "experiment": {
      "id": "E-1235",
      "title": "Antibiotic Resistance in Soil Bacteria",
      "status": "in_progress",
      "current_stage": "Sample Collection",
      "notes": "Collected samples from 3 urban, 3 agricultural, and 3 forest locations",
      "last_updated": "2023-02-10T21:00:00Z",
      "updated_by": "john.doe"
    }
  },
  "message": "Experiment updated successfully"
}
```

#### Add Experiment Entry

```
POST /api/v1/experiments/{experiment_id}/entries
```

Request:
```json
{
  "type": "observation",
  "title": "Initial Sample Observations",
  "content": "Soil samples vary in color and texture. Urban samples are darker and more compact.",
  "date": "2023-02-11T10:00:00Z",
  "attachments": [
    {
      "name": "soil_samples.jpg",
      "content": "base64_encoded_image_data",
      "type": "image/jpeg"
    }
  ]
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "entry": {
      "id": 567,
      "experiment_id": "E-1235",
      "type": "observation",
      "title": "Initial Sample Observations",
      "content": "Soil samples vary in color and texture. Urban samples are darker and more compact.",
      "date": "2023-02-11T10:00:00Z",
      "created_at": "2023-02-10T21:15:00Z",
      "created_by": "john.doe",
      "attachments": [
        {
          "id": 25,
          "name": "soil_samples.jpg",
          "type": "image/jpeg",
          "size": 2500000,
          "url": "/api/v1/files/25"
        }
      ]
    }
  },
  "message": "Experiment entry added successfully"
}
```

#### Get Experiment Entries

```
GET /api/v1/experiments/{experiment_id}/entries
```

Query Parameters:
- `type`: Filter by entry type (observation, measurement, procedure, result)
- `start_date`: Filter by start date
- `end_date`: Filter by end date

Response:
```json
{
  "status": "success",
  "data": {
    "entries": [
      {
        "id": 567,
        "experiment_id": "E-1235",
        "type": "observation",
        "title": "Initial Sample Observations",
        "content": "Soil samples vary in color and texture. Urban samples are darker and more compact.",
        "date": "2023-02-11T10:00:00Z",
        "created_at": "2023-02-10T21:15:00Z",
        "created_by": "john.doe",
        "attachments": [
          {
            "id": 25,
            "name": "soil_samples.jpg",
            "type": "image/jpeg",
            "url": "/api/v1/files/25"
          }
        ]
      },
      ...
    ]
  }
}
```

#### Generate Experiment Report

```
GET /api/v1/experiments/{experiment_id}/report
```

Query Parameters:
- `format`: Report format (pdf, docx, html) (default: pdf)
- `template`: Report template (summary, detailed, custom) (default: detailed)

Response:
Binary file data with appropriate content type header.

### Reports

#### Generate Inventory Report

```
GET /api/v1/reports/inventory
```

Query Parameters:
- `category`: Filter by category
- `status`: Filter by status (available, low, out_of_stock)
- `location`: Filter by location
- `format`: Report format (pdf, csv, xlsx) (default: pdf)

Response:
Binary file data with appropriate content type header.

#### Generate Equipment Usage Report

```
GET /api/v1/reports/equipment/usage
```

Query Parameters:
- `equipment_id`: Filter by equipment ID
- `start_date`: Start date (YYYY-MM-DD)
- `end_date`: End date (YYYY-MM-DD)
- `format`: Report format (pdf, csv, xlsx) (default: pdf)

Response:
Binary file data with appropriate content type header.

#### Generate Lab Usage Report

```
GET /api/v1/reports/labs/usage
```

Query Parameters:
- `lab_id`: Filter by lab ID
- `start_date`: Start date (YYYY-MM-DD)
- `end_date`: End date (YYYY-MM-DD)
- `format`: Report format (pdf, csv, xlsx) (default: pdf)

Response:
Binary file data with appropriate content type header.

#### Generate Custom Report

```
POST /api/v1/reports/custom
```

Request:
```json
{
  "title": "Monthly Equipment Maintenance Report",
  "description": "Report of all equipment maintenance activities for the current month",
  "data_source": "equipment_maintenance",
  "filters": {
    "start_date": "2023-02-01",
    "end_date": "2023-02-28",
    "maintenance_type": ["repair", "calibration"]
  },
  "fields": [
    "equipment_name",
    "maintenance_type",
    "maintenance_date",
    "performed_by",
    "cost",
    "description"
  ],
  "sort_by": "maintenance_date",
  "sort_order": "desc",
  "format": "xlsx"
}
```

Response:
Binary file data with appropriate content type header.

---

## Data Models

### User

| Field | Type | Description |
|-------|------|-------------|
| id | Integer | Unique identifier |
| username | String | Unique username |
| email | String | User's email address |
| full_name | String | User's full name |
| role | String | User's role (admin, lab_manager, lab_assistant, researcher, student, guest) |
| department | String | User's department or group |
| created_at | DateTime | Account creation timestamp |
| last_login | DateTime | Last login timestamp |
| is_active | Boolean | Whether the user account is active |

### Inventory Item

| Field | Type | Description |
|-------|------|-------------|
| id | Integer | Unique identifier |
| name | String | Item name |
| category | String | Primary category |
| subcategory | String | Subcategory |
| description | String | Detailed description |
| quantity | Number | Current quantity |
| unit | String | Unit of measurement |
| location | String | Storage location |
| min_quantity | Number | Minimum stock level |
| status | String | Status (available, low, out_of_stock) |
| supplier | String | Supplier name |
| catalog_number | String | Supplier catalog number |
| cost | Number | Cost per unit |
| currency | String | Currency code |
| expiry_date | Date | Expiration date |
| date_added | DateTime | Date added to inventory |
| last_updated | DateTime | Last update timestamp |
| updated_by | String | Username of last updater |
| barcode | String | Barcode or QR code value |
| safety_info | String | Safety information |

### Equipment

| Field | Type | Description |
|-------|------|-------------|
| id | Integer | Unique identifier |
| name | String | Equipment name |
| model | String | Model number |
| serial_number | String | Serial number |
| manufacturer | String | Manufacturer name |
| category | String | Primary category |
| subcategory | String | Subcategory |
| description | String | Detailed description |
| location | String | Current location |
| status | String | Status (available, in_use, maintenance, out_of_order) |
| condition | String | Condition (excellent, good, fair, poor, needs_repair) |
| purchase_date | Date | Purchase date |
| purchase_cost | Number | Purchase cost |
| warranty_expiry | Date | Warranty expiration date |
| last_maintenance | DateTime | Last maintenance date |
| next_maintenance | DateTime | Next scheduled maintenance |
| last_calibration | DateTime | Last calibration date |
| next_calibration | DateTime | Next scheduled calibration |
| maintenance_interval | Integer | Days between maintenance |
| calibration_interval | Integer | Days between calibrations |
| notes | String | Additional notes |

### Lab Room

| Field | Type | Description |
|-------|------|-------------|
| id | Integer | Unique identifier |
| name | String | Lab name |
| room_number | String | Room number |
| building | String | Building name |
| floor | Integer | Floor number |
| capacity | Integer | Maximum capacity |
| description | String | Detailed description |
| equipment | Array | List of available equipment |
| status | String | Status (available, in_use, maintenance, closed) |

### Lab Booking

| Field | Type | Description |
|-------|------|-------------|
| id | Integer | Unique identifier |
| lab_id | Integer | Lab room ID |
| user_id | Integer | User ID |
| start_time | DateTime | Start time |
| end_time | DateTime | End time |
| purpose | String | Purpose of booking |
| participants | Array | List of participant user IDs |
| equipment_needed | Array | List of required equipment |
| status | String | Status (pending, approved, rejected, cancelled, completed) |
| notes | String | Additional notes |
| created_at | DateTime | Creation timestamp |
| updated_at | DateTime | Last update timestamp |

### Experiment

| Field | Type | Description |
|-------|------|-------------|
| id | String | Unique identifier |
| title | String | Experiment title |
| description | String | Detailed description |
| user_id | Integer | Primary researcher ID |
| collaborators | Array | List of collaborator user IDs |
| status | String | Status (planned, in_progress, completed, archived) |
| start_date | DateTime | Start date |
| end_date | DateTime | End date |
| expected_end_date | DateTime | Expected end date |
| current_stage | String | Current stage name |
| stages | Array | List of experiment stages |
| materials | Array | List of required materials |
| equipment | Array | List of required equipment |
| notes | String | Additional notes |
| created_at | DateTime | Creation timestamp |
| last_updated | DateTime | Last update timestamp |

### Experiment Entry

| Field | Type | Description |
|-------|------|-------------|
| id | Integer | Unique identifier |
| experiment_id | String | Experiment ID |
| type | String | Entry type (observation, measurement, procedure, result) |
| title | String | Entry title |
| content | String | Entry content |
| date | DateTime | Entry date |
| created_at | DateTime | Creation timestamp |
| created_by | String | Username of creator |
| attachments | Array | List of attached files |

---

## Error Handling

### Error Codes

| Code | Description |
|------|-------------|
| AUTH_REQUIRED | Authentication is required |
| INVALID_CREDENTIALS | Invalid username or password |
| TOKEN_EXPIRED | Authentication token has expired |
| PERMISSION_DENIED | User does not have permission for this action |
| RESOURCE_NOT_FOUND | Requested resource was not found |
| VALIDATION_ERROR | Request data failed validation |
| DUPLICATE_ENTRY | Resource already exists |
| DATABASE_ERROR | Database operation failed |
| INTEGRATION_ERROR | External service integration failed |
| RATE_LIMIT_EXCEEDED | API rate limit exceeded |
| SERVER_ERROR | Internal server error |

### Error Response Format

```json
{
  "status": "error",
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      "field1": "Error details for field1",
      "field2": "Error details for field2"
    }
  }
}
```

### HTTP Status Codes

| Status Code | Description |
|-------------|-------------|
| 200 | OK - Request succeeded |
| 201 | Created - Resource created successfully |
| 400 | Bad Request - Invalid request data |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Permission denied |
| 404 | Not Found - Resource not found |
| 409 | Conflict - Resource already exists |
| 422 | Unprocessable Entity - Validation failed |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server error |

---

## Rate Limiting

The API implements rate limiting to prevent abuse and ensure fair usage.

### Rate Limit Headers

The following headers are included in API responses:

- `X-RateLimit-Limit`: Maximum number of requests allowed in the current period
- `X-RateLimit-Remaining`: Number of requests remaining in the current period
- `X-RateLimit-Reset`: Time (in seconds) until the rate limit resets

### Rate Limit Tiers

| Tier | Requests per Minute | Description |
|------|---------------------|-------------|
| Standard | 60 | Default rate limit for authenticated users |
| Premium | 300 | Higher rate limit for premium users |
| Admin | 600 | Highest rate limit for administrators |

### Rate Limit Exceeded Response

When the rate limit is exceeded, the API returns a 429 Too Many Requests response:

```json
{
  "status": "error",
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "API rate limit exceeded",
    "details": {
      "limit": 60,
      "reset": 45
    }
  }
}
```

---

## Webhooks

The API supports webhooks to notify external systems about events in real-time.

### Webhook Events

| Event | Description |
|-------|-------------|
| inventory.updated | Inventory item updated |
| inventory.low_stock | Inventory item reached low stock level |
| equipment.status_changed | Equipment status changed |
| equipment.maintenance_due | Equipment maintenance due soon |
| lab.booked | Lab room booked |
| lab.booking_approved | Lab booking approved |
| experiment.created | New experiment created |
| experiment.updated | Experiment updated |
| experiment.completed | Experiment marked as completed |

### Webhook Payload

```json
{
  "event": "inventory.low_stock",
  "timestamp": "2023-02-10T15:30:00Z",
  "data": {
    "item_id": 1,
    "name": "Sodium Chloride",
    "category": "Chemicals",
    "current_quantity": 90,
    "min_quantity": 100,
    "unit": "g",
    "location": "Storage Room A, Shelf 2"
  }
}
```

### Managing Webhooks

#### Create Webhook Subscription

```
POST /api/v1/webhooks
```

Request:
```json
{
  "url": "https://your-server.com/webhook-endpoint",
  "events": ["inventory.low_stock", "equipment.maintenance_due"],
  "description": "Inventory and maintenance alerts",
  "secret": "your-webhook-secret"
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "webhook": {
      "id": "wh-123456",
      "url": "https://your-server.com/webhook-endpoint",
      "events": ["inventory.low_stock", "equipment.maintenance_due"],
      "description": "Inventory and maintenance alerts",
      "created_at": "2023-02-10T22:00:00Z"
    }
  },
  "message": "Webhook subscription created successfully"
}
```

#### List Webhook Subscriptions

```
GET /api/v1/webhooks
```

Response:
```json
{
  "status": "success",
  "data": {
    "webhooks": [
      {
        "id": "wh-123456",
        "url": "https://your-server.com/webhook-endpoint",
        "events": ["inventory.low_stock", "equipment.maintenance_due"],
        "description": "Inventory and maintenance alerts",
        "created_at": "2023-02-10T22:00:00Z",
        "last_triggered": "2023-02-10T23:15:00Z",
        "status": "active"
      },
      ...
    ]
  }
}
```

#### Delete Webhook Subscription

```
DELETE /api/v1/webhooks/{webhook_id}
```

Response:
```json
{
  "status": "success",
  "message": "Webhook subscription deleted successfully"
}
```

---

## Examples

### Authentication Example

```python
import requests
import json

# Base URL
base_url = "https://your-domain.com/api/v1"

# Login and get token
login_data = {
    "username": "john.doe",
    "password": "your-password"
}

response = requests.post(f"{base_url}/auth/login", json=login_data)
token_data = response.json()

# Extract token
token = token_data["data"]["token"]

# Use token in subsequent requests
headers = {
    "Authorization": f"Bearer {token}",
    "Content-Type": "application/json"
}

# Get user profile
response = requests.get(f"{base_url}/users/me", headers=headers)
user_data = response.json()

print(json.dumps(user_data, indent=2))
```

### Inventory Management Example

```python
import requests
import json

# Base URL and headers
base_url = "https://your-domain.com/api/v1"
headers = {
    "Authorization": "Bearer your-token",
    "Content-Type": "application/json"
}

# Get low stock items
response = requests.get(
    f"{base_url}/inventory",
    params={"status": "low"},
    headers=headers
)
low_stock_items = response.json()

# Print low stock items
print("Low Stock Items:")
for item in low_stock_items["data"]["items"]:
    print(f"{item['name']}: {item['quantity']} {item['unit']} (Min: {item['min_quantity']})")

# Update inventory quantity
item_id = 1
update_data = {
    "change": 100,
    "reason": "Restocked from supplier",
    "notes": "Order #12345"
}

response = requests.post(
    f"{base_url}/inventory/{item_id}/quantity",
    json=update_data,
    headers=headers
)
result = response.json()

print(f"\nUpdated {result['data']['item']['name']} quantity to {result['data']['item']['new_quantity']} {result['data']['item']['unit']}")
```

### Equipment Reservation Example

```javascript
// Base URL and headers
const baseUrl = "https://your-domain.com/api/v1";
const headers = {
  "Authorization": "Bearer your-token",
  "Content-Type": "application/json"
};

// Get available equipment
async function getAvailableEquipment() {
  const response = await fetch(`${baseUrl}/equipment?status=available`, {
    method: "GET",
    headers: headers
  });
  
  const data = await response.json();
  return data.data.equipment;
}

// Reserve equipment
async function reserveEquipment(equipmentId, startTime, endTime, purpose) {
  const reservationData = {
    user_id: 1, // Current user ID
    start_time: startTime,
    end_time: endTime,
    purpose: purpose
  };
  
  const response = await fetch(`${baseUrl}/equipment/${equipmentId}/reserve`, {
    method: "POST",
    headers: headers,
    body: JSON.stringify(reservationData)
  });
  
  return await response.json();
}

// Example usage
async function main() {
  try {
    // Get available equipment
    const equipment = await getAvailableEquipment();
    console.log("Available Equipment:", equipment);
    
    if (equipment.length > 0) {
      // Reserve the first available equipment
      const equipmentId = equipment[0].id;
      const startTime = "2023-03-01T09:00:00Z";
      const endTime = "2023-03-01T12:00:00Z";
      const purpose = "Sample analysis for Project X";
      
      const result = await reserveEquipment(equipmentId, startTime, endTime, purpose);
      console.log("Reservation Result:", result);
    }
  } catch (error) {
    console.error("Error:", error);
  }
}

main();
```

---

## SDK and Client Libraries

### Official Libraries

The following official client libraries are available:

- **Python**: [GitHub Repository](https://github.com/example/slms-python-client)
- **JavaScript**: [GitHub Repository](https://github.com/example/slms-js-client)
- **PHP**: [GitHub Repository](https://github.com/example/slms-php-client)

### Python Client Example

```python
from slms_client import SLMSClient

# Initialize client
client = SLMSClient(
    base_url="https://your-domain.com/api/v1",
    api_key="your-api-key"
)

# Get inventory items
inventory = client.inventory.list(category="Chemicals", status="available")

# Get equipment details
equipment = client.equipment.get(equipment_id=1)

# Book a lab
booking = client.labs.book(
    lab_id=1,
    start_time="2023-03-10T09:00:00Z",
    end_time="2023-03-10T12:00:00Z",
    purpose="Chemistry lab session"
)

# Create an experiment
experiment = client.experiments.create(
    title="Effect of pH on Enzyme Activity",
    description="Investigating how different pH levels affect enzyme kinetics",
    expected_end_date="2023-04-01T17:00:00Z"
)
```

### JavaScript Client Example

```javascript
import { SLMSClient } from 'slms-client';

// Initialize client
const client = new SLMSClient({
  baseUrl: 'https://your-domain.com/api/v1',
  apiKey: 'your-api-key'
});

// Get inventory items
client.inventory.list({ category: 'Chemicals', status: 'available' })
  .then(inventory => {
    console.log('Inventory Items:', inventory);
  })
  .catch(error => {
    console.error('Error:', error);
  });

// Get equipment details
client.equipment.get(1)
  .then(equipment => {
    console.log('Equipment Details:', equipment);
  })
  .catch(error => {
    console.error('Error:', error);
  });

// Book a lab
client.labs.book({
  lab_id: 1,
  start_time: '2023-03-10T09:00:00Z',
  end_time: '2023-03-10T12:00:00Z',
  purpose: 'Chemistry lab session'
})
  .then(booking => {
    console.log('Lab Booking:', booking);
  })
  .catch(error => {
    console.error('Error:', error);
  });
```

### PHP Client Example

```php
<?php
require_once 'vendor/autoload.php';

use SLMS\Client\SLMSClient;

// Initialize client
$client = new SLMSClient([
    'base_url' => 'https://your-domain.com/api/v1',
    'api_key' => 'your-api-key'
]);

try {
    // Get inventory items
    $inventory = $client->inventory->list([
        'category' => 'Chemicals',
        'status' => 'available'
    ]);
    
    // Get equipment details
    $equipment = $client->equipment->get(1);
    
    // Book a lab
    $booking = $client->labs->book([
        'lab_id' => 1,
        'start_time' => '2023-03-10T09:00:00Z',
        'end_time' => '2023-03-10T12:00:00Z',
        'purpose' => 'Chemistry lab session'
    ]);
    
    // Output results
    echo "Inventory Items: " . count($inventory->items) . "\n";
    echo "Equipment: " . $equipment->name . "\n";
    echo "Lab Booking ID: " . $booking->id . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
```