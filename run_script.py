#!/usr/bin/env python
"""
Run script for any Python file in the Science Laboratory Management System.
This script ensures the proper Python path is set up.

Usage:
    python run_script.py <path_to_script>

Example:
    python run_script.py src/app_controller.py
"""
import sys
import os
import importlib.util
import traceback

def run_script(script_path):
    """
    Run a Python script with the correct Python path.
    
    Args:
        script_path (str): Path to the script to run
    """
    try:
        # Get the absolute path to the project root directory
        project_root = os.path.dirname(os.path.abspath(__file__))
        
        # Add the project root directory to the Python path
        sys.path.insert(0, project_root)
        
        # Get the absolute path to the script
        script_path = os.path.join(project_root, script_path)
        
        # Check if the script exists
        if not os.path.isfile(script_path):
            print(f"Error: Script not found: {script_path}")
            return
        
        print(f"Running script: {script_path}")
        print(f"Project root: {project_root}")
        print("-" * 50)
        
        # Load the script as a module
        spec = importlib.util.spec_from_file_location("script", script_path)
        if spec is None:
            print(f"Error: Could not load spec from file: {script_path}")
            return
            
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # If the script has a main function, call it
        if hasattr(module, "main"):
            module.main()
        
    except Exception as e:
        print(f"Error running script: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    # Check if a script path was provided
    if len(sys.argv) < 2:
        print("Error: No script path provided")
        print("Usage: python run_script.py <path_to_script>")
        sys.exit(1)
    
    # Run the script
    run_script(sys.argv[1])