import flet as ft
from datetime import datetime, timedelta
import calendar

class SchedulingView:
    """
    Laboratory scheduling view for the Science Laboratory Management System.
    Allows users to view and manage lab schedules.
    """
    
    def __init__(self, controller):
        """
        Initialize the scheduling view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        self.schedules = []
        self.filtered_schedules = []
        self.lab_rooms = []
        self.current_date = datetime.now()
        self.current_view = "month"  # "month", "week", or "day"
        
        # Create filter controls
        self.lab_room_dropdown = ft.Dropdown(
            label="Lab Room",
            on_change=self.filter_schedules,
            options=[],
            width=200,
        )
        
        self.status_dropdown = ft.Dropdown(
            label="Status",
            on_change=self.filter_schedules,
            options=[
                ft.dropdown.Option("All"),
                ft.dropdown.Option("Pending"),
                ft.dropdown.Option("Approved"),
                ft.dropdown.Option("Rejected"),
                ft.dropdown.Option("Completed"),
            ],
            value="All",
            width=200,
        )
        
        # Create calendar navigation controls
        self.prev_button = ft.IconButton(
            icon=ft.icons.ARROW_BACK,
            on_click=self.prev_period,
            tooltip="Previous",
        )
        
        self.next_button = ft.IconButton(
            icon=ft.icons.ARROW_FORWARD,
            on_click=self.next_period,
            tooltip="Next",
        )
        
        self.today_button = ft.TextButton(
            text="Today",
            on_click=self.go_to_today,
        )
        
        self.period_label = ft.Text(
            size=16,
            weight=ft.FontWeight.BOLD,
        )
        
        self.view_dropdown = ft.Dropdown(
            options=[
                ft.dropdown.Option("Month"),
                ft.dropdown.Option("Week"),
                ft.dropdown.Option("Day"),
            ],
            value="Month",
            on_change=self.change_view,
            width=120,
        )
        
        # Create calendar grid
        self.calendar_grid = ft.GridView(
            expand=True,
            max_extent=150,
            child_aspect_ratio=1.0,
            spacing=2,
            run_spacing=2,
            padding=10,
        )
        
        # Create form fields for adding/editing schedules
        self.schedule_id_field = ft.TextField(visible=False)
        self.title_field = ft.TextField(label="Title", required=True)
        self.description_field = ft.TextField(
            label="Description",
            multiline=True,
            min_lines=2,
            max_lines=4,
        )
        self.lab_room_field = ft.Dropdown(
            label="Lab Room",
            required=True,
        )
        self.start_date_field = ft.TextField(
            label="Start Date (YYYY-MM-DD)",
            required=True,
        )
        self.start_time_field = ft.TextField(
            label="Start Time (HH:MM)",
            required=True,
        )
        self.end_date_field = ft.TextField(
            label="End Date (YYYY-MM-DD)",
            required=True,
        )
        self.end_time_field = ft.TextField(
            label="End Time (HH:MM)",
            required=True,
        )
        self.status_field = ft.Dropdown(
            label="Status",
            required=True,
            options=[
                ft.dropdown.Option("Pending"),
                ft.dropdown.Option("Approved"),
                ft.dropdown.Option("Rejected"),
                ft.dropdown.Option("Completed"),
            ],
        )
        
        # Create dialog for adding/editing schedules
        self.schedule_dialog = ft.AlertDialog(
            title=ft.Text("Add Schedule"),
            content=ft.Column(
                [
                    self.schedule_id_field,
                    self.title_field,
                    self.description_field,
                    self.lab_room_field,
                    ft.Row([
                        self.start_date_field,
                        self.start_time_field,
                    ]),
                    ft.Row([
                        self.end_date_field,
                        self.end_time_field,
                    ]),
                    self.status_field,
                ],
                tight=True,
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                width=500,
                height=400,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=self.close_dialog),
                ft.TextButton("Save", on_click=self.save_schedule),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Create dialog for viewing schedule details
        self.view_dialog = ft.AlertDialog(
            title=ft.Text("Schedule Details"),
            content=ft.Column(
                [
                    ft.Text("Loading..."),
                ],
                scroll=ft.ScrollMode.AUTO,
                width=500,
                height=300,
            ),
            actions=[
                ft.TextButton("Close", on_click=self.close_view_dialog),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
    
    def build(self):
        """
        Build and return the scheduling view.
        
        Returns:
            ft.Container: The scheduling view container
        """
        # Load schedule data
        self.load_schedule_data()
        
        # Update period label
        self.update_period_label()
        
        # Create add button
        add_button = ft.ElevatedButton(
            "Add Schedule",
            icon=ft.icons.ADD,
            on_click=self.show_add_dialog,
        )
        
        # Create header row
        header_row = ft.Row(
            [
                ft.Text("Laboratory Scheduling", size=24, weight=ft.FontWeight.BOLD),
                add_button,
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        )
        
        # Create filters row
        filters_row = ft.Row(
            [
                self.lab_room_dropdown,
                self.status_dropdown,
            ],
            alignment=ft.MainAxisAlignment.START,
            wrap=True,
        )
        
        # Create calendar navigation row
        calendar_nav_row = ft.Row(
            [
                ft.Row([
                    self.prev_button,
                    self.period_label,
                    self.next_button,
                ]),
                ft.Row([
                    self.today_button,
                    self.view_dropdown,
                ]),
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        )
        
        # Create calendar container
        calendar_container = ft.Container(
            content=self.calendar_grid,
            border=ft.border.all(1, ft.colors.GREY_300),
            border_radius=5,
            padding=5,
            expand=True,
        )
        
        # Return the complete view
        return ft.Container(
            content=ft.Column(
                [
                    header_row,
                    ft.Divider(),
                    filters_row,
                    calendar_nav_row,
                    calendar_container,
                ],
                spacing=10,
                expand=True,
            ),
            padding=20,
            expand=True,
        )
    
    def load_schedule_data(self):
        """Load schedule data from the database."""
        # Query the database
        self.schedules = self.controller.db.execute_query("""
            SELECT s.*, u.full_name as creator_name
            FROM lab_schedules s
            JOIN users u ON s.created_by = u.id
            ORDER BY s.start_time
        """)
        self.filtered_schedules = self.schedules.copy()
        
        # Extract unique lab rooms
        self.lab_rooms = sorted(list(set(schedule["lab_room"] for schedule in self.schedules if schedule["lab_room"])))
        
        # Update dropdowns
        self.lab_room_dropdown.options = [ft.dropdown.Option("All")] + [
            ft.dropdown.Option(room) for room in self.lab_rooms
        ]
        self.lab_room_dropdown.value = "All"
        
        # Update form dropdown
        self.lab_room_field.options = [
            ft.dropdown.Option(room) for room in self.lab_rooms
        ]
        self.lab_room_field.options.append(ft.dropdown.Option("Add New..."))
        
        # Update the calendar
        self.update_calendar()
    
    def filter_schedules(self, e=None):
        """
        Filter schedules based on dropdown values.
        
        Args:
            e: The change event (optional)
        """
        lab_room = self.lab_room_dropdown.value
        status = self.status_dropdown.value
        
        # Filter schedules
        self.filtered_schedules = []
        for schedule in self.schedules:
            # Apply lab room filter
            if lab_room != "All" and schedule["lab_room"] != lab_room:
                continue
            
            # Apply status filter
            if status != "All" and schedule["status"] != status:
                continue
            
            # Add schedule to filtered list
            self.filtered_schedules.append(schedule)
        
        # Update the calendar
        self.update_calendar()
    
    def update_period_label(self):
        """Update the period label based on the current view and date."""
        if self.current_view == "month":
            self.period_label.value = self.current_date.strftime("%B %Y")
        elif self.current_view == "week":
            # Get the start and end of the week
            start_of_week = self.current_date - timedelta(days=self.current_date.weekday())
            end_of_week = start_of_week + timedelta(days=6)
            
            # Format the label
            if start_of_week.month == end_of_week.month:
                self.period_label.value = f"{start_of_week.strftime('%b %d')} - {end_of_week.strftime('%d, %Y')}"
            else:
                self.period_label.value = f"{start_of_week.strftime('%b %d')} - {end_of_week.strftime('%b %d, %Y')}"
        else:  # day view
            self.period_label.value = self.current_date.strftime("%A, %B %d, %Y")
    
    def update_calendar(self):
        """Update the calendar grid based on the current view."""
        # Clear the grid
        self.calendar_grid.controls.clear()
        
        if self.current_view == "month":
            self.build_month_view()
        elif self.current_view == "week":
            self.build_week_view()
        else:  # day view
            self.build_day_view()
        
        # Update the page
        self.controller.page.update()
    
    def build_month_view(self):
        """Build the month view calendar."""
        # Get the first day of the month
        first_day = datetime(self.current_date.year, self.current_date.month, 1)
        
        # Get the number of days in the month
        _, num_days = calendar.monthrange(self.current_date.year, self.current_date.month)
        
        # Get the weekday of the first day (0 is Monday, 6 is Sunday)
        first_weekday = first_day.weekday()
        
        # Add day headers
        day_names = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
        for day_name in day_names:
            self.calendar_grid.controls.append(
                ft.Container(
                    content=ft.Text(day_name, weight=ft.FontWeight.BOLD, text_align=ft.TextAlign.CENTER),
                    alignment=ft.alignment.center,
                    bgcolor=ft.colors.BLUE_GREY_100,
                    border=ft.border.all(1, ft.colors.GREY_400),
                    border_radius=5,
                    padding=5,
                )
            )
        
        # Add empty cells for days before the first day of the month
        for _ in range(first_weekday):
            self.calendar_grid.controls.append(
                ft.Container(
                    content=None,
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                )
            )
        
        # Add cells for each day of the month
        for day in range(1, num_days + 1):
            date = datetime(self.current_date.year, self.current_date.month, day)
            
            # Get schedules for this day
            day_schedules = self.get_schedules_for_day(date)
            
            # Create day cell
            day_cell = self.create_day_cell(day, date, day_schedules)
            
            # Add the cell to the grid
            self.calendar_grid.controls.append(day_cell)
        
        # Add empty cells for days after the last day of the month
        last_weekday = (first_weekday + num_days) % 7
        if last_weekday != 0:
            for _ in range(7 - last_weekday):
                self.calendar_grid.controls.append(
                    ft.Container(
                        content=None,
                        border=ft.border.all(1, ft.colors.GREY_300),
                        border_radius=5,
                    )
                )
    
    def build_week_view(self):
        """Build the week view calendar."""
        # Get the start of the week (Monday)
        start_of_week = self.current_date - timedelta(days=self.current_date.weekday())
        
        # Add day headers
        day_names = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
        for i, day_name in enumerate(day_names):
            date = start_of_week + timedelta(days=i)
            self.calendar_grid.controls.append(
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Text(day_name, weight=ft.FontWeight.BOLD, text_align=ft.TextAlign.CENTER),
                            ft.Text(date.strftime("%b %d"), text_align=ft.TextAlign.CENTER),
                        ],
                        alignment=ft.MainAxisAlignment.CENTER,
                        spacing=2,
                    ),
                    alignment=ft.alignment.center,
                    bgcolor=ft.colors.BLUE_GREY_100,
                    border=ft.border.all(1, ft.colors.GREY_400),
                    border_radius=5,
                    padding=5,
                )
            )
        
        # Add schedule containers for each day
        for i in range(7):
            date = start_of_week + timedelta(days=i)
            
            # Get schedules for this day
            day_schedules = self.get_schedules_for_day(date)
            
            # Create schedule list
            schedule_list = ft.ListView(
                spacing=2,
                padding=5,
                auto_scroll=True,
            )
            
            # Add schedules to the list
            for schedule in day_schedules:
                schedule_list.controls.append(
                    self.create_schedule_item(schedule)
                )
            
            # Add the list to the grid
            self.calendar_grid.controls.append(
                ft.Container(
                    content=schedule_list,
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                    padding=5,
                    height=300,
                )
            )
    
    def build_day_view(self):
        """Build the day view calendar."""
        # Set grid to single column
        self.calendar_grid.max_extent = 1000
        
        # Add hour headers
        for hour in range(7, 22):  # 7 AM to 9 PM
            time_str = f"{hour:02d}:00"
            if hour > 12:
                time_str = f"{hour-12:02d}:00 PM"
            elif hour == 12:
                time_str = "12:00 PM"
            else:
                time_str += " AM"
                
            self.calendar_grid.controls.append(
                ft.Container(
                    content=ft.Row(
                        [
                            ft.Text(time_str, weight=ft.FontWeight.BOLD, width=100),
                            ft.VerticalDivider(width=1),
                            self.create_hour_schedule_list(hour),
                        ],
                    ),
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                    padding=5,
                )
            )
    
    def create_hour_schedule_list(self, hour):
        """
        Create a list of schedules for a specific hour.
        
        Args:
            hour (int): The hour (0-23)
            
        Returns:
            ft.Column: Column containing schedule items
        """
        # Get schedules for this day and hour
        hour_schedules = self.get_schedules_for_hour(self.current_date, hour)
        
        # Create schedule list
        schedule_list = ft.Column(
            spacing=2,
            expand=True,
        )
        
        # Add schedules to the list
        for schedule in hour_schedules:
            schedule_list.controls.append(
                self.create_schedule_item(schedule)
            )
        
        return schedule_list
    
    def create_day_cell(self, day, date, schedules):
        """
        Create a cell for a day in the month view.
        
        Args:
            day (int): The day number
            date (datetime): The date
            schedules (list): List of schedules for the day
            
        Returns:
            ft.Container: The day cell
        """
        # Check if this is today
        is_today = date.date() == datetime.now().date()
        
        # Create day number text
        day_text = ft.Text(
            str(day),
            weight=ft.FontWeight.BOLD if is_today else ft.FontWeight.NORMAL,
            color=ft.colors.WHITE if is_today else ft.colors.BLACK,
        )
        
        # Create day header
        day_header = ft.Container(
            content=day_text,
            bgcolor=ft.colors.BLUE if is_today else ft.colors.GREY_200,
            padding=5,
            border_radius=ft.border_radius.only(top_left=5, top_right=5),
        )
        
        # Create schedule list
        schedule_list = ft.ListView(
            spacing=2,
            height=100,
            auto_scroll=True,
        )
        
        # Add schedules to the list (limit to 3 for space)
        for schedule in schedules[:3]:
            schedule_list.controls.append(
                self.create_schedule_item(schedule)
            )
        
        # Add "more" indicator if there are more schedules
        if len(schedules) > 3:
            schedule_list.controls.append(
                ft.Text(f"+ {len(schedules) - 3} more", size=12, color=ft.colors.GREY_700)
            )
        
        # Create the cell
        return ft.Container(
            content=ft.Column(
                [
                    day_header,
                    schedule_list,
                ],
                spacing=0,
                tight=True,
            ),
            border=ft.border.all(1, ft.colors.GREY_400),
            border_radius=5,
            padding=0,
            on_click=lambda e, d=date: self.day_clicked(e, d),
        )
    
    def create_schedule_item(self, schedule):
        """
        Create a schedule item for display in the calendar.
        
        Args:
            schedule (dict): The schedule data
            
        Returns:
            ft.Container: The schedule item container
        """
        # Get status color
        status_color = self.get_status_color(schedule["status"])
        
        # Format time
        start_time = self.format_time(schedule["start_time"])
        
        # Create the item
        return ft.Container(
            content=ft.Text(
                f"{start_time} - {schedule['title']}",
                size=12,
                overflow=ft.TextOverflow.ELLIPSIS,
            ),
            bgcolor=status_color,
            border_radius=3,
            padding=ft.padding.symmetric(horizontal=5, vertical=2),
            on_click=lambda e, s=schedule: self.show_view_dialog(e, s["id"]),
        )
    
    def get_schedules_for_day(self, date):
        """
        Get schedules for a specific day.
        
        Args:
            date (datetime): The date
            
        Returns:
            list: List of schedules for the day
        """
        day_start = date.replace(hour=0, minute=0, second=0, microsecond=0).isoformat()
        day_end = date.replace(hour=23, minute=59, second=59, microsecond=999999).isoformat()
        
        return [
            schedule for schedule in self.filtered_schedules
            if schedule["start_time"] <= day_end and schedule["end_time"] >= day_start
        ]
    
    def get_schedules_for_hour(self, date, hour):
        """
        Get schedules for a specific hour.
        
        Args:
            date (datetime): The date
            hour (int): The hour (0-23)
            
        Returns:
            list: List of schedules for the hour
        """
        hour_start = date.replace(hour=hour, minute=0, second=0, microsecond=0).isoformat()
        hour_end = date.replace(hour=hour, minute=59, second=59, microsecond=999999).isoformat()
        
        return [
            schedule for schedule in self.filtered_schedules
            if schedule["start_time"] <= hour_end and schedule["end_time"] >= hour_start
        ]
    
    def day_clicked(self, e, date):
        """
        Handle day cell click.
        
        Args:
            e: The click event
            date (datetime): The clicked date
        """
        # Set the current date to the clicked date
        self.current_date = date
        
        # Switch to day view
        self.view_dropdown.value = "Day"
        self.current_view = "day"
        
        # Update the calendar
        self.update_period_label()
        self.update_calendar()
    
    def prev_period(self, e):
        """
        Navigate to the previous period.
        
        Args:
            e: The click event
        """
        if self.current_view == "month":
            # Go to previous month
            if self.current_date.month == 1:
                self.current_date = self.current_date.replace(year=self.current_date.year - 1, month=12, day=1)
            else:
                self.current_date = self.current_date.replace(month=self.current_date.month - 1, day=1)
        elif self.current_view == "week":
            # Go to previous week
            self.current_date = self.current_date - timedelta(days=7)
        else:  # day view
            # Go to previous day
            self.current_date = self.current_date - timedelta(days=1)
        
        # Update the calendar
        self.update_period_label()
        self.update_calendar()
    
    def next_period(self, e):
        """
        Navigate to the next period.
        
        Args:
            e: The click event
        """
        if self.current_view == "month":
            # Go to next month
            if self.current_date.month == 12:
                self.current_date = self.current_date.replace(year=self.current_date.year + 1, month=1, day=1)
            else:
                self.current_date = self.current_date.replace(month=self.current_date.month + 1, day=1)
        elif self.current_view == "week":
            # Go to next week
            self.current_date = self.current_date + timedelta(days=7)
        else:  # day view
            # Go to next day
            self.current_date = self.current_date + timedelta(days=1)
        
        # Update the calendar
        self.update_period_label()
        self.update_calendar()
    
    def go_to_today(self, e):
        """
        Navigate to today.
        
        Args:
            e: The click event
        """
        self.current_date = datetime.now()
        
        # Update the calendar
        self.update_period_label()
        self.update_calendar()
    
    def change_view(self, e):
        """
        Change the calendar view.
        
        Args:
            e: The change event
        """
        self.current_view = e.control.value.lower()
        
        # Update the calendar
        self.update_period_label()
        self.update_calendar()
    
    def show_add_dialog(self, e):
        """
        Show dialog for adding a new schedule.
        
        Args:
            e: The click event
        """
        # Clear form fields
        self.schedule_id_field.value = ""
        self.title_field.value = ""
        self.description_field.value = ""
        self.lab_room_field.value = None
        
        # Set default dates and times
        now = datetime.now()
        self.start_date_field.value = now.strftime("%Y-%m-%d")
        self.start_time_field.value = now.strftime("%H:%M")
        
        end_time = now + timedelta(hours=1)
        self.end_date_field.value = end_time.strftime("%Y-%m-%d")
        self.end_time_field.value = end_time.strftime("%H:%M")
        
        self.status_field.value = "Pending"
        
        # Set dialog title
        self.schedule_dialog.title = ft.Text("Add Schedule")
        
        # Show the dialog
        self.controller.page.dialog = self.schedule_dialog
        self.schedule_dialog.open = True
        self.controller.page.update()
    
    def show_edit_dialog(self, e, schedule_id):
        """
        Show dialog for editing a schedule.
        
        Args:
            e: The click event
            schedule_id: ID of the schedule to edit
        """
        # Find the schedule
        schedule = next((s for s in self.schedules if s["id"] == schedule_id), None)
        
        if not schedule:
            return
        
        # Set form fields
        self.schedule_id_field.value = str(schedule["id"])
        self.title_field.value = schedule["title"]
        self.description_field.value = schedule["description"] or ""
        self.lab_room_field.value = schedule["lab_room"]
        
        # Parse start and end times
        try:
            start_time = datetime.fromisoformat(schedule["start_time"].replace('Z', '+00:00'))
            end_time = datetime.fromisoformat(schedule["end_time"].replace('Z', '+00:00'))
            
            self.start_date_field.value = start_time.strftime("%Y-%m-%d")
            self.start_time_field.value = start_time.strftime("%H:%M")
            self.end_date_field.value = end_time.strftime("%Y-%m-%d")
            self.end_time_field.value = end_time.strftime("%H:%M")
        except:
            # Use current time if parsing fails
            now = datetime.now()
            self.start_date_field.value = now.strftime("%Y-%m-%d")
            self.start_time_field.value = now.strftime("%H:%M")
            self.end_date_field.value = now.strftime("%Y-%m-%d")
            self.end_time_field.value = (now + timedelta(hours=1)).strftime("%H:%M")
        
        self.status_field.value = schedule["status"]
        
        # Set dialog title
        self.schedule_dialog.title = ft.Text("Edit Schedule")
        
        # Show the dialog
        self.controller.page.dialog = self.schedule_dialog
        self.schedule_dialog.open = True
        self.controller.page.update()
    
    def show_view_dialog(self, e, schedule_id):
        """
        Show dialog for viewing schedule details.
        
        Args:
            e: The click event
            schedule_id: ID of the schedule to view
        """
        # Find the schedule
        schedule = next((s for s in self.schedules if s["id"] == schedule_id), None)
        
        if not schedule:
            return
        
        # Format times
        start_time = self.format_datetime(schedule["start_time"])
        end_time = self.format_datetime(schedule["end_time"])
        created_at = self.format_datetime(schedule["created_at"])
        
        # Create view content
        content = ft.Column(
            [
                ft.Text(schedule["title"], size=20, weight=ft.FontWeight.BOLD),
                ft.Text(f"Status: {schedule['status']}", color=self.get_status_color(schedule["status"])),
                ft.Text(f"Lab Room: {schedule['lab_room']}"),
                ft.Text(f"Start: {start_time}"),
                ft.Text(f"End: {end_time}"),
                ft.Text(f"Created by: {schedule['creator_name']}"),
                ft.Text(f"Created: {created_at}"),
                ft.Divider(),
                ft.Text("Description", weight=ft.FontWeight.BOLD),
                ft.Text(schedule["description"] or "No description provided"),
            ],
            scroll=ft.ScrollMode.AUTO,
            spacing=5,
        )
        
        # Add edit and delete buttons if user is the creator or an administrator
        actions = [
            ft.TextButton("Close", on_click=self.close_view_dialog),
        ]
        
        if schedule["created_by"] == self.controller.current_user["id"] or self.controller.current_user["role"] == "administrator":
            actions = [
                ft.TextButton("Delete", on_click=lambda e, s_id=schedule_id: self.delete_schedule_from_view(e, s_id)),
                ft.TextButton("Edit", on_click=lambda e, s_id=schedule_id: self.edit_from_view(e, s_id)),
                ft.TextButton("Close", on_click=self.close_view_dialog),
            ]
        
        # Update dialog content
        self.view_dialog.title = ft.Text("Schedule Details")
        self.view_dialog.content = content
        self.view_dialog.actions = actions
        
        # Show the dialog
        self.controller.page.dialog = self.view_dialog
        self.view_dialog.open = True
        self.controller.page.update()
    
    def close_dialog(self, e):
        """
        Close the schedule dialog.
        
        Args:
            e: The click event
        """
        self.schedule_dialog.open = False
        self.controller.page.update()
    
    def close_view_dialog(self, e):
        """
        Close the view dialog.
        
        Args:
            e: The click event
        """
        self.view_dialog.open = False
        self.controller.page.update()
    
    def edit_from_view(self, e, schedule_id):
        """
        Edit a schedule from the view dialog.
        
        Args:
            e: The click event
            schedule_id: ID of the schedule to edit
        """
        # Close the view dialog
        self.view_dialog.open = False
        
        # Show the edit dialog
        self.show_edit_dialog(e, schedule_id)
    
    def delete_schedule_from_view(self, e, schedule_id):
        """
        Delete a schedule from the view dialog.
        
        Args:
            e: The click event
            schedule_id: ID of the schedule to delete
        """
        # Close the view dialog
        self.view_dialog.open = False
        
        # Delete the schedule
        self.delete_schedule(e, schedule_id)
    
    def save_schedule(self, e):
        """
        Save the schedule.
        
        Args:
            e: The click event
        """
        # Validate required fields
        if not self.title_field.value or not self.lab_room_field.value or not self.status_field.value:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Please fill in all required fields"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
            return
        
        # Validate dates and times
        try:
            start_date = self.start_date_field.value
            start_time = self.start_time_field.value
            end_date = self.end_date_field.value
            end_time = self.end_time_field.value
            
            start_datetime = datetime.strptime(f"{start_date} {start_time}", "%Y-%m-%d %H:%M")
            end_datetime = datetime.strptime(f"{end_date} {end_time}", "%Y-%m-%d %H:%M")
            
            if end_datetime <= start_datetime:
                raise ValueError("End time must be after start time")
        except ValueError as e:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text(f"Invalid date or time: {str(e)}"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
            return
        
        # Handle "Add New..." option for lab room
        if self.lab_room_field.value == "Add New...":
            # Show dialog to add new lab room
            self.show_add_lab_room_dialog()
            return
        
        # Prepare schedule data
        schedule_data = {
            "title": self.title_field.value,
            "description": self.description_field.value if self.description_field.value else None,
            "lab_room": self.lab_room_field.value,
            "start_time": start_datetime.isoformat(),
            "end_time": end_datetime.isoformat(),
            "status": self.status_field.value,
        }
        
        # Check for scheduling conflicts
        conflicts = self.check_scheduling_conflicts(
            schedule_data["lab_room"],
            schedule_data["start_time"],
            schedule_data["end_time"],
            int(self.schedule_id_field.value) if self.schedule_id_field.value else None
        )
        
        if conflicts:
            # Show conflict warning
            self.show_conflict_warning(conflicts, schedule_data)
            return
        
        # Add or update schedule
        if self.schedule_id_field.value:
            # Update existing schedule
            schedule_id = int(self.schedule_id_field.value)
            
            # Build the update query
            query_parts = []
            params = []
            
            for key, value in schedule_data.items():
                query_parts.append(f"{key} = ?")
                params.append(value)
            
            query = f"UPDATE lab_schedules SET {', '.join(query_parts)} WHERE id = ?"
            params.append(schedule_id)
            
            # Execute the update
            self.controller.db.execute_query(query, tuple(params))
            
            # Log the update
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "update",
                "lab_schedule",
                schedule_id,
                f"Updated lab schedule: {schedule_data['title']}"
            )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Schedule updated successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
        else:
            # Add new schedule
            schedule_data["created_by"] = self.controller.current_user["id"]
            
            # Build the insert query
            keys = list(schedule_data.keys())
            placeholders = ["?"] * len(keys)
            
            query = f"INSERT INTO lab_schedules ({', '.join(keys)}) VALUES ({', '.join(placeholders)})"
            params = tuple(schedule_data.values())
            
            # Execute the insert
            schedule_id = self.controller.db.execute_insert(query, params)
            
            # Log the insert
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "create",
                "lab_schedule",
                schedule_id,
                f"Added new lab schedule: {schedule_data['title']}"
            )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Schedule added successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
        
        # Close the dialog
        self.schedule_dialog.open = False
        
        # Reload schedule data
        self.load_schedule_data()
        
        # Update the page
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def delete_schedule(self, e, schedule_id):
        """
        Delete a schedule.
        
        Args:
            e: The click event
            schedule_id: ID of the schedule to delete
        """
        # Find the schedule
        schedule = next((s for s in self.schedules if s["id"] == schedule_id), None)
        
        if not schedule:
            return
        
        # Show confirmation dialog
        def confirm_delete(e):
            # Delete the schedule
            self.controller.db.execute_query(
                "DELETE FROM lab_schedules WHERE id = ?",
                (schedule_id,)
            )
            
            # Log the delete
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "delete",
                "lab_schedule",
                schedule_id,
                f"Deleted lab schedule: {schedule['title']}"
            )
            
            # Close the dialog
            confirm_dialog.open = False
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Schedule deleted successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
            self.controller.page.snack_bar.open = True
            
            # Reload schedule data
            self.load_schedule_data()
            
            # Update the page
            self.controller.page.update()
        
        # Create confirmation dialog
        confirm_dialog = ft.AlertDialog(
            title=ft.Text("Confirm Delete"),
            content=ft.Text(f"Are you sure you want to delete '{schedule['title']}'?"),
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(confirm_dialog, "open", False)),
                ft.TextButton("Delete", on_click=confirm_delete),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = confirm_dialog
        confirm_dialog.open = True
        self.controller.page.update()
    
    def show_add_lab_room_dialog(self):
        """Show dialog for adding a new lab room."""
        # Create text field for new lab room
        new_lab_room_field = ft.TextField(
            label="New Lab Room",
            autofocus=True,
        )
        
        # Create dialog
        def add_lab_room(e):
            if not new_lab_room_field.value:
                return
            
            # Add the new lab room to the list
            new_lab_room = new_lab_room_field.value
            self.lab_rooms.append(new_lab_room)
            self.lab_rooms.sort()
            
            # Update dropdowns
            self.lab_room_dropdown.options = [ft.dropdown.Option("All")] + [
                ft.dropdown.Option(room) for room in self.lab_rooms
            ]
            
            self.lab_room_field.options = [
                ft.dropdown.Option(room) for room in self.lab_rooms
            ]
            self.lab_room_field.options.append(ft.dropdown.Option("Add New..."))
            
            # Set the new lab room as selected
            self.lab_room_field.value = new_lab_room
            
            # Close the dialog
            lab_room_dialog.open = False
            
            # Update the page
            self.controller.page.update()
        
        # Create dialog
        lab_room_dialog = ft.AlertDialog(
            title=ft.Text("Add New Lab Room"),
            content=new_lab_room_field,
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(lab_room_dialog, "open", False)),
                ft.TextButton("Add", on_click=add_lab_room),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = lab_room_dialog
        lab_room_dialog.open = True
        self.controller.page.update()
    
    def check_scheduling_conflicts(self, lab_room, start_time, end_time, exclude_id=None):
        """
        Check for scheduling conflicts.
        
        Args:
            lab_room (str): The lab room
            start_time (str): The start time
            end_time (str): The end time
            exclude_id (int, optional): ID of schedule to exclude from conflict check
            
        Returns:
            list: List of conflicting schedules
        """
        conflicts = []
        
        for schedule in self.schedules:
            # Skip the schedule being edited
            if exclude_id and schedule["id"] == exclude_id:
                continue
            
            # Skip schedules for different lab rooms
            if schedule["lab_room"] != lab_room:
                continue
            
            # Check for time overlap
            if schedule["start_time"] < end_time and schedule["end_time"] > start_time:
                conflicts.append(schedule)
        
        return conflicts
    
    def show_conflict_warning(self, conflicts, schedule_data):
        """
        Show warning about scheduling conflicts.
        
        Args:
            conflicts (list): List of conflicting schedules
            schedule_data (dict): The schedule data being saved
        """
        # Create conflict list
        conflict_list = ft.Column(
            spacing=5,
            scroll=ft.ScrollMode.AUTO,
            height=200,
        )
        
        for conflict in conflicts:
            conflict_list.controls.append(
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Text(conflict["title"], weight=ft.FontWeight.BOLD),
                            ft.Text(f"Time: {self.format_datetime(conflict['start_time'])} - {self.format_datetime(conflict['end_time'])}"),
                            ft.Text(f"Created by: {conflict['creator_name']}"),
                        ],
                        spacing=2,
                    ),
                    padding=10,
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                )
            )
        
        # Create dialog
        def force_save(e):
            # Close the conflict dialog
            conflict_dialog.open = False
            
            # Force save with "Pending" status
            schedule_data["status"] = "Pending"
            
            if self.schedule_id_field.value:
                # Update existing schedule
                schedule_id = int(self.schedule_id_field.value)
                
                # Build the update query
                query_parts = []
                params = []
                
                for key, value in schedule_data.items():
                    query_parts.append(f"{key} = ?")
                    params.append(value)
                
                query = f"UPDATE lab_schedules SET {', '.join(query_parts)} WHERE id = ?"
                params.append(schedule_id)
                
                # Execute the update
                self.controller.db.execute_query(query, tuple(params))
                
                # Log the update
                self.controller.db.log_audit(
                    self.controller.current_user["id"],
                    "update",
                    "lab_schedule",
                    schedule_id,
                    f"Updated lab schedule with conflicts: {schedule_data['title']}"
                )
            else:
                # Add new schedule
                schedule_data["created_by"] = self.controller.current_user["id"]
                
                # Build the insert query
                keys = list(schedule_data.keys())
                placeholders = ["?"] * len(keys)
                
                query = f"INSERT INTO lab_schedules ({', '.join(keys)}) VALUES ({', '.join(placeholders)})"
                params = tuple(schedule_data.values())
                
                # Execute the insert
                schedule_id = self.controller.db.execute_insert(query, params)
                
                # Log the insert
                self.controller.db.log_audit(
                    self.controller.current_user["id"],
                    "create",
                    "lab_schedule",
                    schedule_id,
                    f"Added new lab schedule with conflicts: {schedule_data['title']}"
                )
            
            # Close the schedule dialog
            self.schedule_dialog.open = False
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Schedule saved with pending status due to conflicts"),
                bgcolor=ft.colors.ORANGE_400,
            )
            self.controller.page.snack_bar.open = True
            
            # Reload schedule data
            self.load_schedule_data()
            
            # Update the page
            self.controller.page.update()
        
        # Create conflict dialog
        conflict_dialog = ft.AlertDialog(
            title=ft.Text("Scheduling Conflict"),
            content=ft.Column(
                [
                    ft.Text("The following schedules conflict with your booking:"),
                    conflict_list,
                    ft.Text("You can either go back and change the time/date or save as pending for administrator approval."),
                ],
                tight=True,
                spacing=10,
            ),
            actions=[
                ft.TextButton("Go Back", on_click=lambda e: setattr(conflict_dialog, "open", False)),
                ft.TextButton("Save as Pending", on_click=force_save),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = conflict_dialog
        conflict_dialog.open = True
        self.controller.page.update()
    
    def format_datetime(self, timestamp):
        """
        Format a datetime for display.
        
        Args:
            timestamp (str): The timestamp to format
            
        Returns:
            str: Formatted datetime
        """
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime("%m/%d/%Y %I:%M %p")
        except:
            return timestamp
    
    def format_time(self, timestamp):
        """
        Format a time for display.
        
        Args:
            timestamp (str): The timestamp to format
            
        Returns:
            str: Formatted time
        """
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime("%I:%M %p")
        except:
            return timestamp
    
    def get_status_color(self, status):
        """
        Get the color for a status.
        
        Args:
            status (str): The status
            
        Returns:
            str: The color for the status
        """
        if status == "Pending":
            return ft.colors.AMBER_100
        elif status == "Approved":
            return ft.colors.GREEN_100
        elif status == "Rejected":
            return ft.colors.RED_100
        elif status == "Completed":
            return ft.colors.BLUE_100
        else:
            return ft.colors.GREY_100