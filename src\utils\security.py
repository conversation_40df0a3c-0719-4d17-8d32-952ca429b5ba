import hashlib
import secrets
import string
import re
import time
import jwt
import bcrypt
import pyotp
from datetime import datetime, timedelta

class Security:
    """
    Security utility class for the Science Laboratory Management System.
    Handles password hashing, token generation, and other security functions.
    """
    
    def __init__(self, secret_key=None):
        """
        Initialize the security utility.
        
        Args:
            secret_key (str, optional): Secret key for JWT token signing
        """
        self.secret_key = secret_key or secrets.token_hex(32)
        self.token_expiry = 24  # Token expiry in hours
        self.password_reset_expiry = 1  # Password reset token expiry in hours
        self.max_failed_attempts = 5  # Max failed login attempts before lockout
        self.lockout_duration = 15  # Lockout duration in minutes
    
    def hash_password(self, password):
        """
        Hash a password using bcrypt.
        
        Args:
            password (str): The plain text password
            
        Returns:
            str: The hashed password
        """
        # Generate a salt and hash the password
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode(), salt)
        return hashed.decode()
    
    def verify_password(self, password, hashed_password):
        """
        Verify a password against a hash.
        
        Args:
            password (str): The plain text password to verify
            hashed_password (str): The stored hash to check against
            
        Returns:
            bool: True if the password matches, False otherwise
        """
        # Check if the hash is in the old SHA-256 format
        if len(hashed_password) == 64 and all(c in string.hexdigits for c in hashed_password):
            # Old SHA-256 hash
            return hashlib.sha256(password.encode()).hexdigest() == hashed_password
        
        # Bcrypt hash
        try:
            return bcrypt.checkpw(password.encode(), hashed_password.encode())
        except:
            return False
    
    def generate_token(self, length=32):
        """
        Generate a random token for things like password resets.
        
        Args:
            length (int): The length of the token
            
        Returns:
            str: A random token
        """
        return secrets.token_urlsafe(length)
    
    def generate_session_token(self, user_id, username, role):
        """
        Generate a JWT session token.
        
        Args:
            user_id (int): The user ID
            username (str): The username
            role (str): The user's role
            
        Returns:
            str: A JWT token
        """
        payload = {
            'user_id': user_id,
            'username': username,
            'role': role,
            'exp': datetime.utcnow() + timedelta(hours=self.token_expiry),
            'iat': datetime.utcnow()
        }
        
        return jwt.encode(payload, self.secret_key, algorithm='HS256')
    
    def verify_session_token(self, token):
        """
        Verify a JWT session token.
        
        Args:
            token (str): The JWT token
            
        Returns:
            dict: The decoded token payload if valid, None otherwise
        """
        try:
            return jwt.decode(token, self.secret_key, algorithms=['HS256'])
        except:
            return None
    
    def generate_password_reset_token(self, user_id):
        """
        Generate a password reset token.
        
        Args:
            user_id (int): The user ID
            
        Returns:
            tuple: (token, expiry timestamp)
        """
        token = self.generate_token()
        expiry = datetime.utcnow() + timedelta(hours=self.password_reset_expiry)
        
        return token, expiry.isoformat()
    
    def check_password_complexity(self, password):
        """
        Check if a password meets complexity requirements.
        
        Args:
            password (str): The password to check
            
        Returns:
            tuple: (bool, str) - (is_valid, error_message)
        """
        if len(password) < 8:
            return False, "Password must be at least 8 characters long"
        
        if not re.search(r'[A-Z]', password):
            return False, "Password must contain at least one uppercase letter"
        
        if not re.search(r'[a-z]', password):
            return False, "Password must contain at least one lowercase letter"
        
        if not re.search(r'[0-9]', password):
            return False, "Password must contain at least one number"
        
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            return False, "Password must contain at least one special character"
        
        return True, ""
    
    def generate_totp_secret(self):
        """
        Generate a secret for TOTP-based two-factor authentication.
        
        Returns:
            str: A base32 encoded secret
        """
        return pyotp.random_base32()
    
    def verify_totp(self, secret, token):
        """
        Verify a TOTP token.
        
        Args:
            secret (str): The TOTP secret
            token (str): The token to verify
            
        Returns:
            bool: True if the token is valid, False otherwise
        """
        totp = pyotp.TOTP(secret)
        return totp.verify(token)
    
    def get_totp_uri(self, secret, username, issuer="Science Lab"):
        """
        Get a TOTP URI for QR code generation.
        
        Args:
            secret (str): The TOTP secret
            username (str): The username
            issuer (str): The issuer name
            
        Returns:
            str: A TOTP URI
        """
        totp = pyotp.TOTP(secret)
        return totp.provisioning_uri(username, issuer_name=issuer)