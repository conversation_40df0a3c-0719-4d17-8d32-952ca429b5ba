import flet as ft
import json
from datetime import datetime

class NotificationView:
    """
    Notification settings view for the Science Laboratory Management System.
    Allows users to configure notification preferences and view notifications.
    """
    
    def __init__(self, controller):
        """
        Initialize the notification view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        self.notifications = []
        self.current_tab = "notifications"  # "notifications" or "settings"
        
        # Create tabs
        self.tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            tabs=[
                ft.Tab(
                    text="Notifications",
                    icon=ft.icons.NOTIFICATIONS,
                    content=ft.Container(padding=10),
                ),
                ft.Tab(
                    text="Settings",
                    icon=ft.icons.SETTINGS,
                    content=ft.Container(padding=10),
                ),
            ],
            on_change=self.tab_changed,
        )
        
        # Create notifications list
        self.notifications_list = ft.ListView(
            spacing=2,
            padding=10,
            auto_scroll=True,
            expand=True,
        )
        
        # Create notification settings controls
        self.email_notifications_switch = ft.Switch(
            label="Email Notifications",
            value=True,
        )
        
        self.email_address_field = ft.TextField(
            label="Email Address",
            expand=True,
        )
        
        self.inventory_alerts_switch = ft.Switch(
            label="Inventory Alerts",
            value=True,
            tooltip="Receive alerts for low stock and expiring items",
        )
        
        self.experiment_reminders_switch = ft.Switch(
            label="Experiment Reminders",
            value=True,
            tooltip="Receive reminders about ongoing experiments",
        )
        
        self.schedule_reminders_switch = ft.Switch(
            label="Schedule Reminders",
            value=True,
            tooltip="Receive reminders about upcoming lab schedules",
        )
        
        self.safety_alerts_switch = ft.Switch(
            label="Safety Alerts",
            value=True,
            tooltip="Receive alerts about safety incidents and checklist reminders",
        )
        
        self.system_notifications_switch = ft.Switch(
            label="System Notifications",
            value=True,
            tooltip="Receive notifications about system updates and maintenance",
        )
        
        self.notification_frequency_dropdown = ft.Dropdown(
            label="Notification Frequency",
            options=[
                ft.dropdown.Option("immediate"),
                ft.dropdown.Option("hourly"),
                ft.dropdown.Option("daily"),
                ft.dropdown.Option("weekly"),
            ],
            value="immediate",
            width=200,
        )
        
        # Create save button
        self.save_button = ft.ElevatedButton(
            text="Save Settings",
            icon=ft.icons.SAVE,
            on_click=self.save_settings_clicked,
        )
        
        # Create mark all read button
        self.mark_all_read_button = ft.ElevatedButton(
            text="Mark All as Read",
            icon=ft.icons.DONE_ALL,
            on_click=self.mark_all_read_clicked,
        )
    
    def build(self):
        """
        Build and return the notification view.
        
        Returns:
            ft.Container: The notification view container
        """
        # Load data
        self.load_data()
        
        # Build notifications tab
        notifications_content = ft.Column(
            [
                ft.Row(
                    [
                        ft.Text("Notifications", size=20, weight=ft.FontWeight.BOLD),
                        self.mark_all_read_button,
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Divider(),
                ft.Container(
                    content=self.notifications_list,
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                    expand=True,
                ),
            ],
            spacing=10,
            expand=True,
        )
        
        # Build settings tab
        settings_content = ft.Column(
            [
                ft.Text("Notification Settings", size=20, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                ft.Text("Email Settings", size=16, weight=ft.FontWeight.BOLD),
                self.email_notifications_switch,
                self.email_address_field,
                ft.Container(height=10),
                ft.Text("Alert Types", size=16, weight=ft.FontWeight.BOLD),
                self.inventory_alerts_switch,
                self.experiment_reminders_switch,
                self.schedule_reminders_switch,
                self.safety_alerts_switch,
                self.system_notifications_switch,
                ft.Container(height=10),
                ft.Text("Delivery Settings", size=16, weight=ft.FontWeight.BOLD),
                self.notification_frequency_dropdown,
                ft.Container(height=20),
                ft.Row(
                    [
                        self.save_button,
                    ],
                    alignment=ft.MainAxisAlignment.END,
                ),
            ],
            spacing=10,
            expand=True,
        )
        
        # Set tab contents
        self.tabs.tabs[0].content = notifications_content
        self.tabs.tabs[1].content = settings_content
        
        # Return the complete view
        return ft.Container(
            content=ft.Column(
                [
                    ft.Text("Notifications", size=24, weight=ft.FontWeight.BOLD),
                    ft.Container(height=10),
                    self.tabs,
                ],
                spacing=10,
                expand=True,
            ),
            padding=20,
            expand=True,
        )
    
    def load_data(self):
        """Load notification data and settings."""
        # Load notifications
        self.load_notifications()
        
        # Load notification settings
        self.load_notification_settings()
    
    def load_notifications(self):
        """Load notifications from the database."""
        # Clear existing items
        self.notifications_list.controls.clear()
        
        # Query notifications
        self.notifications = self.controller.db.execute_query("""
            SELECT * FROM notifications 
            WHERE user_id = ? 
            ORDER BY created_at DESC
            LIMIT 50
        """, (self.controller.current_user["id"],))
        
        if not self.notifications:
            # Show empty state
            self.notifications_list.controls.append(
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Icon(ft.icons.NOTIFICATIONS_OFF, size=50, color=ft.colors.GREY_400),
                            ft.Text("No notifications", size=16, color=ft.colors.GREY_400),
                        ],
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        spacing=10,
                    ),
                    alignment=ft.alignment.center,
                    expand=True,
                )
            )
        else:
            # Add notifications to the list
            for notification in self.notifications:
                self.notifications_list.controls.append(
                    self.create_notification_item(notification)
                )
        
        # Update the page
        self.controller.page.update()
    
    def load_notification_settings(self):
        """Load notification settings from user preferences."""
        if self.controller.current_user and "preferences" in self.controller.current_user:
            try:
                prefs = json.loads(self.controller.current_user["preferences"])
                
                # Set email settings
                self.email_notifications_switch.value = prefs.get("email_notifications", True)
                self.email_address_field.value = prefs.get("email_address", self.controller.current_user["email"])
                
                # Set alert types
                self.inventory_alerts_switch.value = prefs.get("inventory_alerts", True)
                self.experiment_reminders_switch.value = prefs.get("experiment_reminders", True)
                self.schedule_reminders_switch.value = prefs.get("schedule_reminders", True)
                self.safety_alerts_switch.value = prefs.get("safety_alerts", True)
                self.system_notifications_switch.value = prefs.get("system_notifications", True)
                
                # Set delivery settings
                self.notification_frequency_dropdown.value = prefs.get("notification_frequency", "immediate")
            except:
                # Set defaults if preferences can't be parsed
                self.set_default_settings()
        else:
            # Set defaults if no preferences found
            self.set_default_settings()
    
    def set_default_settings(self):
        """Set default notification settings."""
        self.email_notifications_switch.value = True
        self.email_address_field.value = self.controller.current_user["email"] if self.controller.current_user else ""
        self.inventory_alerts_switch.value = True
        self.experiment_reminders_switch.value = True
        self.schedule_reminders_switch.value = True
        self.safety_alerts_switch.value = True
        self.system_notifications_switch.value = True
        self.notification_frequency_dropdown.value = "immediate"
    
    def create_notification_item(self, notification):
        """
        Create a notification item for the list.
        
        Args:
            notification (dict): The notification data
            
        Returns:
            ft.Container: The notification item container
        """
        # Format time
        time_str = self.format_time(notification["created_at"])
        
        # Get icon and color based on type
        icon, color = self.get_notification_icon_and_color(notification["type"])
        
        # Create read/unread indicator
        read_indicator = ft.Container(
            width=10,
            height=10,
            border_radius=5,
            bgcolor=ft.colors.BLUE if not notification["read"] else ft.colors.TRANSPARENT,
        )
        
        # Create notification item
        return ft.Container(
            content=ft.Row(
                [
                    read_indicator,
                    ft.Icon(icon, color=color),
                    ft.Column(
                        [
                            ft.Text(notification["title"], weight=ft.FontWeight.BOLD if not notification["read"] else ft.FontWeight.NORMAL),
                            ft.Text(notification["message"], size=12),
                            ft.Text(time_str, size=10, color=ft.colors.GREY_600),
                        ],
                        spacing=2,
                        expand=True,
                    ),
                    ft.IconButton(
                        icon=ft.icons.DELETE,
                        tooltip="Delete",
                        on_click=lambda e, n_id=notification["id"]: self.delete_notification(e, n_id),
                    ),
                ],
                alignment=ft.MainAxisAlignment.START,
                spacing=10,
            ),
            padding=10,
            border=ft.border.all(1, ft.colors.GREY_300),
            border_radius=5,
            bgcolor=ft.colors.BLUE_50 if not notification["read"] else ft.colors.WHITE,
            margin=ft.margin.only(bottom=5),
            on_click=lambda e, n_id=notification["id"]: self.mark_notification_read(e, n_id),
        )
    
    def tab_changed(self, e):
        """
        Handle tab change.
        
        Args:
            e: The change event
        """
        self.current_tab = "notifications" if e.control.selected_index == 0 else "settings"
    
    def mark_notification_read(self, e, notification_id):
        """
        Mark a notification as read.
        
        Args:
            e: The click event
            notification_id: ID of the notification to mark as read
        """
        # Update the notification in the database
        self.controller.db.execute_query(
            "UPDATE notifications SET read = 1 WHERE id = ?",
            (notification_id,)
        )
        
        # Reload notifications
        self.load_notifications()
    
    def delete_notification(self, e, notification_id):
        """
        Delete a notification.
        
        Args:
            e: The click event
            notification_id: ID of the notification to delete
        """
        # Delete the notification from the database
        self.controller.db.execute_query(
            "DELETE FROM notifications WHERE id = ?",
            (notification_id,)
        )
        
        # Reload notifications
        self.load_notifications()
    
    def mark_all_read_clicked(self, e):
        """
        Handle mark all as read button click.
        
        Args:
            e: The click event
        """
        # Update all notifications in the database
        self.controller.db.execute_query(
            "UPDATE notifications SET read = 1 WHERE user_id = ?",
            (self.controller.current_user["id"],)
        )
        
        # Reload notifications
        self.load_notifications()
        
        # Show success message
        self.controller.page.snack_bar = ft.SnackBar(
            content=ft.Text("All notifications marked as read"),
            bgcolor=ft.colors.GREEN_400,
        )
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def save_settings_clicked(self, e):
        """
        Handle save settings button click.
        
        Args:
            e: The click event
        """
        # Prepare settings data
        settings = {
            "email_notifications": self.email_notifications_switch.value,
            "email_address": self.email_address_field.value,
            "inventory_alerts": self.inventory_alerts_switch.value,
            "experiment_reminders": self.experiment_reminders_switch.value,
            "schedule_reminders": self.schedule_reminders_switch.value,
            "safety_alerts": self.safety_alerts_switch.value,
            "system_notifications": self.system_notifications_switch.value,
            "notification_frequency": self.notification_frequency_dropdown.value,
        }
        
        # Get current preferences
        current_prefs = {}
        if self.controller.current_user and "preferences" in self.controller.current_user and self.controller.current_user["preferences"]:
            try:
                current_prefs = json.loads(self.controller.current_user["preferences"])
            except:
                current_prefs = {}
        
        # Update preferences with new settings
        current_prefs.update(settings)
        
        # Save to database
        self.controller.db.execute_query(
            "UPDATE users SET preferences = ? WHERE id = ?",
            (json.dumps(current_prefs), self.controller.current_user["id"])
        )
        
        # Update current user in memory
        self.controller.current_user["preferences"] = json.dumps(current_prefs)
        
        # Show success message
        self.controller.page.snack_bar = ft.SnackBar(
            content=ft.Text("Notification settings saved successfully"),
            bgcolor=ft.colors.GREEN_400,
        )
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def format_time(self, timestamp):
        """
        Format a timestamp for display.
        
        Args:
            timestamp (str): The timestamp to format
            
        Returns:
            str: Formatted timestamp
        """
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            now = datetime.now()
            
            # Calculate time difference
            diff = now - dt
            
            if diff.days > 7:
                # More than a week ago, show date
                return dt.strftime("%b %d, %Y")
            elif diff.days > 0:
                # Days ago
                return f"{diff.days} {'day' if diff.days == 1 else 'days'} ago"
            elif diff.seconds >= 3600:
                # Hours ago
                hours = diff.seconds // 3600
                return f"{hours} {'hour' if hours == 1 else 'hours'} ago"
            elif diff.seconds >= 60:
                # Minutes ago
                minutes = diff.seconds // 60
                return f"{minutes} {'minute' if minutes == 1 else 'minutes'} ago"
            else:
                # Just now
                return "Just now"
        except:
            return timestamp
    
    def get_notification_icon_and_color(self, notification_type):
        """
        Get the icon and color for a notification type.
        
        Args:
            notification_type (str): The notification type
            
        Returns:
            tuple: (icon, color)
        """
        if notification_type == "inventory":
            return ft.icons.INVENTORY, ft.colors.BLUE
        elif notification_type == "experiment":
            return ft.icons.SCIENCE, ft.colors.PURPLE
        elif notification_type == "schedule":
            return ft.icons.CALENDAR_TODAY, ft.colors.GREEN
        elif notification_type == "safety":
            return ft.icons.WARNING, ft.colors.ORANGE
        elif notification_type == "system":
            return ft.icons.SETTINGS, ft.colors.GREY
        else:
            return ft.icons.NOTIFICATIONS, ft.colors.BLUE