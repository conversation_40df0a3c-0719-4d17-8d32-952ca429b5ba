#!/usr/bin/env python
"""
Backend Testing Script for Science Laboratory Management System
Tests database connectivity, API endpoints, and backend functionality
"""
import requests
import json
import sys
import os
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from src.database.db_manager import DatabaseManager
    from src.auth.auth_manager import AuthManager
    from src.models.user_model import UserModel
    from src.models.inventory_model import InventoryModel
    from src.models.experiment_model import ExperimentModel
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running from the project root directory")
    sys.exit(1)


class BackendTester:
    """Backend testing utility."""
    
    def __init__(self):
        """Initialize the backend tester."""
        self.api_base_url = "http://localhost:8000"
        self.test_results = []
        self.access_token = None
        
    def log_test(self, test_name, success, message=""):
        """Log test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.now().isoformat()
        })
        print(f"{status} {test_name}: {message}")
    
    def test_database_connectivity(self):
        """Test database connectivity and basic operations."""
        print("\n🗄️ Testing Database Connectivity...")
        
        try:
            # Test database connection
            db_manager = DatabaseManager()
            db_manager.connect()
            self.log_test("Database Connection", True, "Connected successfully")
            
            # Test table existence
            db_manager.cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in db_manager.cursor.fetchall()]
            
            expected_tables = ['users', 'inventory_items', 'experiments', 'bookings', 'audit_logs']
            missing_tables = [table for table in expected_tables if table not in tables]
            
            if missing_tables:
                self.log_test("Database Schema", False, f"Missing tables: {missing_tables}")
            else:
                self.log_test("Database Schema", True, f"Found {len(tables)} tables")
            
            # Test sample data
            db_manager.cursor.execute("SELECT COUNT(*) FROM users")
            user_count = db_manager.cursor.fetchone()[0]
            self.log_test("Sample Data", user_count > 0, f"Found {user_count} users")
            
            db_manager.disconnect()
            
        except Exception as e:
            self.log_test("Database Connection", False, str(e))
    
    def test_api_health(self):
        """Test API health endpoint."""
        print("\n🌐 Testing API Health...")
        
        try:
            response = requests.get(f"{self.api_base_url}/api/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                self.log_test("API Health Check", True, f"Status: {data.get('status', 'unknown')}")
            else:
                self.log_test("API Health Check", False, f"HTTP {response.status_code}")
        except requests.exceptions.RequestException as e:
            self.log_test("API Health Check", False, f"Connection error: {e}")
    
    def test_authentication(self):
        """Test authentication endpoints."""
        print("\n🔐 Testing Authentication...")
        
        # Test login endpoint
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        try:
            response = requests.post(
                f"{self.api_base_url}/api/auth/login",
                json=login_data,
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get("access_token")
                self.log_test("User Login", True, f"Token received: {self.access_token[:20]}...")
            else:
                self.log_test("User Login", False, f"HTTP {response.status_code}: {response.text}")
                
        except requests.exceptions.RequestException as e:
            self.log_test("User Login", False, f"Connection error: {e}")
    
    def test_inventory_api(self):
        """Test inventory API endpoints."""
        print("\n📦 Testing Inventory API...")
        
        if not self.access_token:
            self.log_test("Inventory API", False, "No access token available")
            return
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        try:
            # Test get inventory
            response = requests.get(
                f"{self.api_base_url}/api/inventory",
                headers=headers,
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                self.log_test("Get Inventory", True, f"Found {len(data)} items")
            else:
                self.log_test("Get Inventory", False, f"HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            self.log_test("Get Inventory", False, f"Connection error: {e}")
    
    def test_models(self):
        """Test model classes."""
        print("\n🏗️ Testing Model Classes...")
        
        try:
            # Test UserModel
            db_manager = DatabaseManager()
            user_model = UserModel(db_manager)
            
            users = user_model.get_all_users()
            self.log_test("UserModel", len(users) > 0, f"Retrieved {len(users)} users")
            
            # Test InventoryModel
            inventory_model = InventoryModel(db_manager)
            items = inventory_model.get_all_items()
            self.log_test("InventoryModel", len(items) > 0, f"Retrieved {len(items)} items")
            
            # Test ExperimentModel
            experiment_model = ExperimentModel(db_manager)
            experiments = experiment_model.get_all_experiments()
            self.log_test("ExperimentModel", True, f"Retrieved {len(experiments)} experiments")
            
        except Exception as e:
            self.log_test("Model Classes", False, str(e))
    
    def test_authentication_manager(self):
        """Test authentication manager."""
        print("\n🔑 Testing Authentication Manager...")
        
        try:
            db_manager = DatabaseManager()
            auth_manager = AuthManager(db_manager)
            
            # Test login
            success, user = auth_manager.login("admin", "admin123")
            self.log_test("Auth Manager Login", success, f"User: {user.get('username') if user else 'None'}")
            
            # Test current user
            if success:
                current_user = auth_manager.get_current_user()
                self.log_test("Get Current User", current_user is not None, 
                            f"User: {current_user.get('username') if current_user else 'None'}")
            
            # Test logout
            auth_manager.logout()
            self.log_test("Auth Manager Logout", True, "Logged out successfully")
            
        except Exception as e:
            self.log_test("Authentication Manager", False, str(e))
    
    def test_api_documentation(self):
        """Test API documentation endpoints."""
        print("\n📚 Testing API Documentation...")
        
        try:
            # Test Swagger UI
            response = requests.get(f"{self.api_base_url}/api/docs", timeout=5)
            self.log_test("Swagger UI", response.status_code == 200, 
                         f"HTTP {response.status_code}")
            
            # Test ReDoc
            response = requests.get(f"{self.api_base_url}/api/redoc", timeout=5)
            self.log_test("ReDoc", response.status_code == 200, 
                         f"HTTP {response.status_code}")
            
        except requests.exceptions.RequestException as e:
            self.log_test("API Documentation", False, f"Connection error: {e}")
    
    def generate_report(self):
        """Generate test report."""
        print("\n" + "="*60)
        print("🧪 BACKEND TEST REPORT")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"📊 Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   • {result['test']}: {result['message']}")
        
        print(f"\n🕒 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Save detailed report
        report_data = {
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": (passed_tests/total_tests)*100,
                "timestamp": datetime.now().isoformat()
            },
            "results": self.test_results
        }
        
        with open("backend_test_report.json", "w") as f:
            json.dump(report_data, f, indent=2)
        
        print(f"📄 Detailed report saved to: backend_test_report.json")
        
        return failed_tests == 0


def main():
    """Main testing function."""
    print("🔧 Science Laboratory Management System - Backend Testing")
    print("="*60)
    
    tester = BackendTester()
    
    # Run all tests
    tester.test_database_connectivity()
    tester.test_api_health()
    tester.test_authentication()
    tester.test_inventory_api()
    tester.test_models()
    tester.test_authentication_manager()
    tester.test_api_documentation()
    
    # Generate report
    success = tester.generate_report()
    
    if success:
        print("\n🎉 All backend tests passed!")
        sys.exit(0)
    else:
        print("\n⚠️  Some backend tests failed. Check the report for details.")
        sys.exit(1)


if __name__ == "__main__":
    main()
