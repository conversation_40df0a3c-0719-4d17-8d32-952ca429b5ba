import flet as ft
from datetime import datetime, timedelta
import csv
import json
import os
import sqlite3

class DataExportView:
    """
    Data export view for the Science Laboratory Management System.
    Provides functionality to export data in various formats.
    """
    
    def __init__(self, controller):
        """
        Initialize the data export view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        
        # Create export type controls
        self.export_type_dropdown = ft.Dropdown(
            label="Export Type",
            options=[
                ft.dropdown.Option("lab_utilization", "Lab Utilization"),
                ft.dropdown.Option("inventory", "Inventory"),
                ft.dropdown.Option("inventory_transactions", "Inventory Transactions"),
                ft.dropdown.Option("experiments", "Experiments"),
                ft.dropdown.Option("schedules", "Lab Schedules"),
                ft.dropdown.Option("safety_incidents", "Safety Incidents"),
                ft.dropdown.Option("safety_checklists", "Safety Checklists"),
                ft.dropdown.Option("users", "Users"),
                ft.dropdown.Option("audit_trail", "Audit Trail"),
            ],
            value="lab_utilization",
            width=300,
            on_change=self.export_type_changed,
        )
        
        # Create date range controls
        self.date_range_dropdown = ft.Dropdown(
            label="Date Range",
            options=[
                ft.dropdown.Option("all_time", "All Time"),
                ft.dropdown.Option("this_year", "This Year"),
                ft.dropdown.Option("last_30_days", "Last 30 Days"),
                ft.dropdown.Option("last_90_days", "Last 90 Days"),
                ft.dropdown.Option("custom", "Custom Range"),
            ],
            value="last_30_days",
            on_change=self.date_range_changed,
            width=200,
        )
        
        self.start_date_picker = ft.TextField(
            label="Start Date (YYYY-MM-DD)",
            value=self.get_default_start_date(),
            width=200,
            visible=False,
        )
        
        self.end_date_picker = ft.TextField(
            label="End Date (YYYY-MM-DD)",
            value=self.get_default_end_date(),
            width=200,
            visible=False,
        )
        
        # Create format controls
        self.export_format_dropdown = ft.Dropdown(
            label="Export Format",
            options=[
                ft.dropdown.Option("csv", "CSV"),
                ft.dropdown.Option("json", "JSON"),
                ft.dropdown.Option("excel", "Excel"),
                ft.dropdown.Option("pdf", "PDF"),
            ],
            value="csv",
            width=200,
        )
        
        # Create options controls
        self.include_headers_checkbox = ft.Checkbox(
            label="Include Headers",
            value=True,
        )
        
        self.include_metadata_checkbox = ft.Checkbox(
            label="Include Metadata",
            value=True,
        )
        
        self.compress_checkbox = ft.Checkbox(
            label="Compress Output",
            value=False,
        )
        
        # Create export button
        self.export_button = ft.ElevatedButton(
            text="Export Data",
            icon=ft.icons.DOWNLOAD,
            on_click=self.export_data_clicked,
        )
        
        # Create status text
        self.status_text = ft.Text(
            visible=False,
        )
        
        # Create export history table
        self.export_history_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("Date")),
                ft.DataColumn(ft.Text("Type")),
                ft.DataColumn(ft.Text("Format")),
                ft.DataColumn(ft.Text("Records")),
                ft.DataColumn(ft.Text("File")),
                ft.DataColumn(ft.Text("Actions")),
            ],
            rows=[],
        )
        
        # Create export preview
        self.preview_text = ft.Text("Select an export type and click Export to generate data.")
        
        self.preview_container = ft.Container(
            content=self.preview_text,
            border=ft.border.all(1, ft.colors.GREY_300),
            border_radius=5,
            padding=10,
            height=200,
            expand=True,
        )
    
    def build(self):
        """
        Build and return the data export view.
        
        Returns:
            ft.Container: The data export view container
        """
        # Load export history
        self.load_export_history()
        
        # Create export settings card
        export_settings_card = ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Text("Export Settings", size=16, weight=ft.FontWeight.BOLD),
                        ft.Divider(),
                        self.export_type_dropdown,
                        ft.Row(
                            [
                                self.date_range_dropdown,
                                self.start_date_picker,
                                self.end_date_picker,
                            ],
                            alignment=ft.MainAxisAlignment.START,
                            wrap=True,
                        ),
                        ft.Row(
                            [
                                self.export_format_dropdown,
                                ft.Column(
                                    [
                                        self.include_headers_checkbox,
                                        self.include_metadata_checkbox,
                                        self.compress_checkbox,
                                    ],
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.START,
                            wrap=True,
                        ),
                        ft.Row(
                            [
                                self.export_button,
                                self.status_text,
                            ],
                            alignment=ft.MainAxisAlignment.START,
                        ),
                    ],
                    spacing=10,
                ),
                padding=20,
            ),
        )
        
        # Create export preview card
        export_preview_card = ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Text("Export Preview", size=16, weight=ft.FontWeight.BOLD),
                        ft.Divider(),
                        self.preview_container,
                    ],
                    spacing=10,
                ),
                padding=20,
            ),
        )
        
        # Create export history card
        export_history_card = ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Text("Export History", size=16, weight=ft.FontWeight.BOLD),
                        ft.Divider(),
                        ft.Container(
                            content=self.export_history_table,
                            height=200,
                        ),
                    ],
                    spacing=10,
                ),
                padding=20,
            ),
        )
        
        # Return the complete view
        return ft.Container(
            content=ft.Column(
                [
                    ft.Text("Data Export", size=24, weight=ft.FontWeight.BOLD),
                    ft.Container(height=10),
                    export_settings_card,
                    ft.Container(height=20),
                    export_preview_card,
                    ft.Container(height=20),
                    export_history_card,
                ],
                spacing=10,
                expand=True,
            ),
            padding=20,
            expand=True,
        )
    
    def load_export_history(self):
        """Load export history from the database."""
        # Query export history
        history = self.controller.db.execute_query("""
            SELECT 
                e.*,
                u.username,
                u.full_name
            FROM 
                export_history e
                JOIN users u ON e.user_id = u.id
            ORDER BY 
                e.timestamp DESC
            LIMIT 10
        """)
        
        # Clear existing rows
        self.export_history_table.rows.clear()
        
        # Add rows for export history
        for item in history:
            # Format timestamp
            timestamp = self.format_timestamp(item["timestamp"])
            
            # Format export type
            export_type = item["export_type"].replace("_", " ").capitalize()
            
            # Create row
            self.export_history_table.rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(timestamp)),
                        ft.DataCell(ft.Text(export_type)),
                        ft.DataCell(ft.Text(item["format"].upper())),
                        ft.DataCell(ft.Text(str(item["record_count"]))),
                        ft.DataCell(ft.Text(item["filename"])),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.icons.DOWNLOAD,
                                        tooltip="Download",
                                        on_click=lambda e, path=item["file_path"]: self.download_file(e, path),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.DELETE,
                                        tooltip="Delete",
                                        on_click=lambda e, id=item["id"]: self.delete_export(e, id),
                                    ),
                                ]
                            )
                        ),
                    ]
                )
            )
        
        # Update the page
        self.controller.page.update()
    
    def export_type_changed(self, e):
        """
        Handle export type dropdown change.
        
        Args:
            e: The change event
        """
        # Update preview based on selected export type
        self.update_preview()
    
    def date_range_changed(self, e):
        """
        Handle date range dropdown change.
        
        Args:
            e: The change event
        """
        # Show/hide custom date fields
        if self.date_range_dropdown.value == "custom":
            self.start_date_picker.visible = True
            self.end_date_picker.visible = True
        else:
            self.start_date_picker.visible = False
            self.end_date_picker.visible = False
            
            # Set default dates based on selection
            start_date, end_date = self.get_date_range()
            self.start_date_picker.value = start_date.split("T")[0] if "T" in start_date else start_date
            self.end_date_picker.value = end_date.split("T")[0] if "T" in end_date else end_date
        
        # Update preview
        self.update_preview()
        
        # Update the page
        self.controller.page.update()
    
    def update_preview(self):
        """Update the export preview based on current settings."""
        export_type = self.export_type_dropdown.value
        start_date, end_date = self.get_date_range()
        
        # Get preview data based on export type
        if export_type == "lab_utilization":
            # Query for lab utilization
            query = """
                SELECT 
                    s.start_time, 
                    s.end_time, 
                    r.room_number,
                    r.name as room_name,
                    u.username
                FROM 
                    lab_schedules s
                    JOIN lab_rooms r ON s.room_id = r.id
                    JOIN users u ON s.user_id = u.id
                WHERE 
                    s.start_time >= ? AND s.end_time <= ?
                ORDER BY 
                    s.start_time
                LIMIT 5
            """
            data = self.controller.db.execute_query(query, (start_date, end_date))
            
            # Create preview text
            if data:
                preview_text = f"Preview of Lab Utilization Export ({len(data)} of ? records):\n\n"
                for row in data:
                    preview_text += f"Room: {row['room_number']} - {row['room_name']}\n"
                    preview_text += f"Time: {row['start_time']} to {row['end_time']}\n"
                    preview_text += f"User: {row['username']}\n\n"
            else:
                preview_text = "No data found for the selected date range."
        
        elif export_type == "inventory":
            # Query for inventory
            query = """
                SELECT 
                    name,
                    category,
                    current_quantity,
                    unit,
                    location
                FROM 
                    inventory_items
                ORDER BY 
                    name
                LIMIT 5
            """
            data = self.controller.db.execute_query(query)
            
            # Create preview text
            if data:
                preview_text = f"Preview of Inventory Export ({len(data)} of ? records):\n\n"
                for row in data:
                    preview_text += f"Item: {row['name']}\n"
                    preview_text += f"Category: {row['category']}\n"
                    preview_text += f"Quantity: {row['current_quantity']} {row['unit']}\n"
                    preview_text += f"Location: {row['location']}\n\n"
            else:
                preview_text = "No inventory data found."
        
        elif export_type == "audit_trail":
            # Query for audit trail
            query = """
                SELECT 
                    a.timestamp,
                    u.username,
                    a.action,
                    a.entity_type,
                    a.details
                FROM 
                    audit_logs a
                    JOIN users u ON a.user_id = u.id
                WHERE 
                    a.timestamp >= ? AND a.timestamp <= ?
                ORDER BY 
                    a.timestamp DESC
                LIMIT 5
            """
            data = self.controller.db.execute_query(query, (start_date, end_date))
            
            # Create preview text
            if data:
                preview_text = f"Preview of Audit Trail Export ({len(data)} of ? records):\n\n"
                for row in data:
                    preview_text += f"Time: {row['timestamp']}\n"
                    preview_text += f"User: {row['username']}\n"
                    preview_text += f"Action: {row['action']} {row['entity_type']}\n"
                    preview_text += f"Details: {row['details']}\n\n"
            else:
                preview_text = "No audit data found for the selected date range."
        
        else:
            preview_text = f"Preview for {export_type} export will be shown here."
        
        # Update preview text
        self.preview_text.value = preview_text
        self.preview_container.content = self.preview_text
        
        # Update the page
        self.controller.page.update()
    
    def export_data_clicked(self, e):
        """
        Handle export data button click.
        
        Args:
            e: The click event
        """
        # Get export settings
        export_type = self.export_type_dropdown.value
        export_format = self.export_format_dropdown.value
        include_headers = self.include_headers_checkbox.value
        include_metadata = self.include_metadata_checkbox.value
        compress = self.compress_checkbox.value
        start_date, end_date = self.get_date_range()
        
        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{export_type}_{timestamp}.{export_format}"
        
        # Show status
        self.status_text.value = "Exporting data..."
        self.status_text.color = ft.colors.BLUE
        self.status_text.visible = True
        self.controller.page.update()
        
        # Export data based on format
        try:
            if export_format == "csv":
                record_count = self.export_to_csv(export_type, filename, include_headers, include_metadata, start_date, end_date)
            elif export_format == "json":
                record_count = self.export_to_json(export_type, filename, include_metadata, start_date, end_date)
            elif export_format == "excel":
                # Excel export would be implemented in a real application
                self.status_text.value = "Excel export not implemented in this example"
                self.status_text.color = ft.colors.ORANGE
                self.controller.page.update()
                return
            elif export_format == "pdf":
                # PDF export would be implemented in a real application
                self.status_text.value = "PDF export not implemented in this example"
                self.status_text.color = ft.colors.ORANGE
                self.controller.page.update()
                return
            
            # Create export directory if it doesn't exist
            export_dir = os.path.join(os.getcwd(), "exports")
            file_path = os.path.join(export_dir, filename)
            
            # Log the export in the database
            self.controller.db.execute_query("""
                INSERT INTO export_history (
                    user_id, export_type, format, record_count, 
                    filename, file_path, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                self.controller.current_user["id"],
                export_type,
                export_format,
                record_count,
                filename,
                file_path,
                datetime.now().isoformat()
            ))
            
            # Show success message
            self.status_text.value = f"Export completed: {record_count} records exported to {filename}"
            self.status_text.color = ft.colors.GREEN
            
            # Log the export in audit trail
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "export",
                export_type,
                None,
                f"Exported {record_count} records to {export_format}"
            )
            
            # Reload export history
            self.load_export_history()
        
        except Exception as e:
            # Show error message
            self.status_text.value = f"Error exporting data: {str(e)}"
            self.status_text.color = ft.colors.RED
        
        # Update the page
        self.controller.page.update()
    
    def export_to_csv(self, export_type, filename, include_headers, include_metadata, start_date, end_date):
        """
        Export data to CSV file.
        
        Args:
            export_type (str): Type of data to export
            filename (str): Output filename
            include_headers (bool): Whether to include headers
            include_metadata (bool): Whether to include metadata
            start_date (str): Start date for filtering
            end_date (str): End date for filtering
            
        Returns:
            int: Number of records exported
        """
        # Get query and headers based on export type
        query, headers, params = self.get_export_query(export_type, start_date, end_date)
        
        # Execute query
        data = self.controller.db.execute_query(query, params)
        
        # Create export directory if it doesn't exist
        export_dir = os.path.join(os.getcwd(), "exports")
        os.makedirs(export_dir, exist_ok=True)
        
        # Write to CSV file
        filepath = os.path.join(export_dir, filename)
        with open(filepath, 'w', newline='') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write metadata if requested
            if include_metadata:
                writer.writerow(["# Export Type", export_type])
                writer.writerow(["# Date Range", f"{start_date} to {end_date}"])
                writer.writerow(["# Export Date", datetime.now().isoformat()])
                writer.writerow(["# User", f"{self.controller.current_user['username']} ({self.controller.current_user['full_name']})"])
                writer.writerow(["# Record Count", str(len(data))])
                writer.writerow([])  # Empty row after metadata
            
            # Write headers if requested
            if include_headers:
                writer.writerow(headers)
            
            # Write data rows
            for row in data:
                writer.writerow([row[header] if header in row else "" for header in headers])
        
        return len(data)
    
    def export_to_json(self, export_type, filename, include_metadata, start_date, end_date):
        """
        Export data to JSON file.
        
        Args:
            export_type (str): Type of data to export
            filename (str): Output filename
            include_metadata (bool): Whether to include metadata
            start_date (str): Start date for filtering
            end_date (str): End date for filtering
            
        Returns:
            int: Number of records exported
        """
        # Get query based on export type
        query, _, params = self.get_export_query(export_type, start_date, end_date)
        
        # Execute query
        data = self.controller.db.execute_query(query, params)
        
        # Create export directory if it doesn't exist
        export_dir = os.path.join(os.getcwd(), "exports")
        os.makedirs(export_dir, exist_ok=True)
        
        # Prepare JSON data
        json_data = {
            "data": data
        }
        
        # Add metadata if requested
        if include_metadata:
            json_data["metadata"] = {
                "export_type": export_type,
                "date_range": {
                    "start_date": start_date,
                    "end_date": end_date
                },
                "export_date": datetime.now().isoformat(),
                "user": {
                    "username": self.controller.current_user["username"],
                    "full_name": self.controller.current_user["full_name"]
                },
                "record_count": len(data)
            }
        
        # Write to JSON file
        filepath = os.path.join(export_dir, filename)
        with open(filepath, 'w') as jsonfile:
            json.dump(json_data, jsonfile, indent=2, default=str)
        
        return len(data)
    
    def get_export_query(self, export_type, start_date, end_date):
        """
        Get the query and headers for an export type.
        
        Args:
            export_type (str): Type of data to export
            start_date (str): Start date for filtering
            end_date (str): End date for filtering
            
        Returns:
            tuple: (query, headers, params)
        """
        if export_type == "lab_utilization":
            query = """
                SELECT 
                    s.id,
                    s.start_time, 
                    s.end_time, 
                    s.title,
                    s.description,
                    r.room_number,
                    r.name as room_name,
                    u.username,
                    u.full_name
                FROM 
                    lab_schedules s
                    JOIN lab_rooms r ON s.room_id = r.id
                    JOIN users u ON s.user_id = u.id
                WHERE 
                    s.start_time >= ? AND s.end_time <= ?
                ORDER BY 
                    s.start_time
            """
            headers = ["id", "start_time", "end_time", "title", "description", "room_number", "room_name", "username", "full_name"]
            params = (start_date, end_date)
        
        elif export_type == "inventory":
            query = """
                SELECT 
                    *
                FROM 
                    inventory_items
                ORDER BY 
                    name
            """
            headers = ["id", "name", "category", "description", "current_quantity", "unit", "location", 
                      "reorder_level", "price_per_unit", "expiration_date", "last_updated"]
            params = ()
        
        elif export_type == "inventory_transactions":
            query = """
                SELECT 
                    t.*,
                    i.name as item_name,
                    i.category,
                    u.username,
                    u.full_name
                FROM 
                    inventory_transactions t
                    JOIN inventory_items i ON t.item_id = i.id
                    JOIN users u ON t.user_id = u.id
                WHERE 
                    t.timestamp >= ? AND t.timestamp <= ?
                ORDER BY 
                    t.timestamp DESC
            """
            headers = ["id", "timestamp", "item_id", "item_name", "category", "transaction_type", 
                      "quantity", "username", "full_name", "notes"]
            params = (start_date, end_date)
        
        elif export_type == "experiments":
            query = """
                SELECT 
                    e.*,
                    u.username,
                    u.full_name
                FROM 
                    experiments e
                    JOIN users u ON e.user_id = u.id
                WHERE 
                    e.start_date >= ? AND (e.end_date <= ? OR e.end_date IS NULL)
                ORDER BY 
                    e.start_date DESC
            """
            headers = ["id", "title", "objective", "start_date", "end_date", "status", "username", "full_name", 
                      "materials", "procedure", "observations", "results", "conclusion"]
            params = (start_date, end_date)
        
        elif export_type == "schedules":
            query = """
                SELECT 
                    s.*,
                    r.room_number,
                    r.name as room_name,
                    u.username,
                    u.full_name
                FROM 
                    lab_schedules s
                    JOIN lab_rooms r ON s.room_id = r.id
                    JOIN users u ON s.user_id = u.id
                WHERE 
                    s.start_time >= ? AND s.end_time <= ?
                ORDER BY 
                    s.start_time
            """
            headers = ["id", "title", "start_time", "end_time", "room_number", "room_name", 
                      "username", "full_name", "description", "status"]
            params = (start_date, end_date)
        
        elif export_type == "safety_incidents":
            query = """
                SELECT 
                    i.*,
                    u.username,
                    u.full_name
                FROM 
                    safety_incidents i
                    JOIN users u ON i.reported_by_id = u.id
                WHERE 
                    i.date_reported >= ? AND i.date_reported <= ?
                ORDER BY 
                    i.date_reported DESC
            """
            headers = ["id", "incident_type", "severity", "date_reported", "date_occurred", "location", 
                      "description", "username", "full_name", "status", "resolution", "date_resolved"]
            params = (start_date, end_date)
        
        elif export_type == "safety_checklists":
            query = """
                SELECT 
                    c.*,
                    u.username,
                    u.full_name
                FROM 
                    safety_checklists c
                    JOIN users u ON c.completed_by_id = u.id
                WHERE 
                    c.date_performed >= ? AND c.date_performed <= ?
                ORDER BY 
                    c.date_performed DESC
            """
            headers = ["id", "checklist_type", "date_performed", "status", "issues_found", 
                      "username", "full_name", "notes"]
            params = (start_date, end_date)
        
        elif export_type == "users":
            query = """
                SELECT 
                    id, username, full_name, email, department, role, status, 
                    created_at, last_login
                FROM 
                    users
                ORDER BY 
                    username
            """
            headers = ["id", "username", "full_name", "email", "department", "role", 
                      "status", "created_at", "last_login"]
            params = ()
        
        elif export_type == "audit_trail":
            query = """
                SELECT 
                    a.*,
                    u.username,
                    u.full_name
                FROM 
                    audit_logs a
                    JOIN users u ON a.user_id = u.id
                WHERE 
                    a.timestamp >= ? AND a.timestamp <= ?
                ORDER BY 
                    a.timestamp DESC
            """
            headers = ["id", "timestamp", "username", "full_name", "action", "entity_type", 
                      "entity_id", "details", "ip_address"]
            params = (start_date, end_date)
        
        else:
            # Default empty query
            query = "SELECT 1 WHERE 1=0"
            headers = []
            params = ()
        
        return query, headers, params
    
    def download_file(self, e, file_path):
        """
        Handle download file button click.
        
        Args:
            e: The click event
            file_path (str): Path to the file to download
        """
        # In a real application, this would trigger a file download
        # For this example, we'll just show a message
        
        if os.path.exists(file_path):
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text(f"File would be downloaded from: {file_path}"),
                bgcolor=ft.colors.GREEN_400,
            )
        else:
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text(f"File not found: {file_path}"),
                bgcolor=ft.colors.RED_400,
            )
        
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def delete_export(self, e, export_id):
        """
        Handle delete export button click.
        
        Args:
            e: The click event
            export_id (int): ID of the export to delete
        """
        # Get file path
        result = self.controller.db.execute_query(
            "SELECT file_path FROM export_history WHERE id = ?",
            (export_id,)
        )
        
        if not result:
            return
        
        file_path = result[0]["file_path"]
        
        # Show confirmation dialog
        def confirm_delete(e):
            # Delete file if it exists
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except:
                    pass
            
            # Delete from database
            self.controller.db.execute_query(
                "DELETE FROM export_history WHERE id = ?",
                (export_id,)
            )
            
            # Log the deletion
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "delete",
                "export",
                export_id,
                f"Deleted export record and file: {os.path.basename(file_path)}"
            )
            
            # Close dialog
            dialog.open = False
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Export record deleted"),
                bgcolor=ft.colors.GREEN_400,
            )
            self.controller.page.snack_bar.open = True
            
            # Reload export history
            self.load_export_history()
            
            # Update the page
            self.controller.page.update()
        
        # Create confirmation dialog
        dialog = ft.AlertDialog(
            title=ft.Text("Confirm Delete"),
            content=ft.Text(f"Are you sure you want to delete this export record and file?"),
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(dialog, "open", False)),
                ft.TextButton("Delete", on_click=confirm_delete),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = dialog
        dialog.open = True
        self.controller.page.update()
    
    def get_date_range(self):
        """
        Get start and end dates based on selected date range.
        
        Returns:
            tuple: (start_date, end_date)
        """
        if self.date_range_dropdown.value == "custom":
            start_date = f"{self.start_date_picker.value}T00:00:00"
            end_date = f"{self.end_date_picker.value}T23:59:59"
            return (start_date, end_date)
        
        now = datetime.now()
        end_date = now.replace(hour=23, minute=59, second=59).isoformat()
        
        if self.date_range_dropdown.value == "all_time":
            # Use a very old start date
            start_date = "2000-01-01T00:00:00"
        elif self.date_range_dropdown.value == "this_year":
            start_date = datetime(now.year, 1, 1).isoformat()
        elif self.date_range_dropdown.value == "last_30_days":
            start_date = (now - timedelta(days=30)).isoformat()
        elif self.date_range_dropdown.value == "last_90_days":
            start_date = (now - timedelta(days=90)).isoformat()
        else:
            # Default to last 30 days
            start_date = (now - timedelta(days=30)).isoformat()
        
        return (start_date, end_date)
    
    def get_default_start_date(self):
        """
        Get default start date (30 days ago).
        
        Returns:
            str: Default start date
        """
        return (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
    
    def get_default_end_date(self):
        """
        Get default end date (today).
        
        Returns:
            str: Default end date
        """
        return datetime.now().strftime("%Y-%m-%d")
    
    def format_timestamp(self, timestamp):
        """
        Format a timestamp for display.
        
        Args:
            timestamp (str): The timestamp to format
            
        Returns:
            str: Formatted timestamp
        """
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M")
        except:
            return timestamp