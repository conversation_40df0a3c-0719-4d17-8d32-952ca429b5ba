import flet as ft
from datetime import datetime

class UserAdminView:
    """
    User administration view for the Science Laboratory Management System.
    Allows administrators to manage users, roles, and permissions.
    """
    
    def __init__(self, controller):
        """
        Initialize the user administration view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        self.users = []
        self.filtered_users = []
        
        # Create filter controls
        self.search_field = ft.TextField(
            label="Search",
            prefix_icon=ft.icons.SEARCH,
            on_change=self.filter_users,
            expand=True,
        )
        
        self.role_dropdown = ft.Dropdown(
            label="Role",
            on_change=self.filter_users,
            options=[
                ft.dropdown.Option("All"),
                ft.dropdown.Option("administrator"),
                ft.dropdown.Option("lab_manager"),
                ft.dropdown.Option("researcher"),
                ft.dropdown.Option("user"),
            ],
            value="All",
            width=200,
        )
        
        self.status_dropdown = ft.Dropdown(
            label="Status",
            on_change=self.filter_users,
            options=[
                ft.dropdown.Option("All"),
                ft.dropdown.Option("active"),
                ft.dropdown.Option("inactive"),
                ft.dropdown.Option("pending"),
                ft.dropdown.Option("suspended"),
            ],
            value="All",
            width=200,
        )
        
        # Create data table
        self.data_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("Username")),
                ft.DataColumn(ft.Text("Full Name")),
                ft.DataColumn(ft.Text("Email")),
                ft.DataColumn(ft.Text("Department")),
                ft.DataColumn(ft.Text("Role")),
                ft.DataColumn(ft.Text("Status")),
                ft.DataColumn(ft.Text("Last Login")),
                ft.DataColumn(ft.Text("Actions")),
            ],
            rows=[],
        )
        
        # Create form fields for editing users
        self.user_id_field = ft.TextField(visible=False)
        self.username_field = ft.TextField(label="Username", read_only=True)
        self.full_name_field = ft.TextField(label="Full Name", required=True)
        self.email_field = ft.TextField(label="Email", required=True)
        self.department_field = ft.TextField(label="Department")
        
        self.role_field = ft.Dropdown(
            label="Role",
            required=True,
            options=[
                ft.dropdown.Option("administrator"),
                ft.dropdown.Option("lab_manager"),
                ft.dropdown.Option("researcher"),
                ft.dropdown.Option("user"),
            ],
        )
        
        self.status_field = ft.Dropdown(
            label="Status",
            required=True,
            options=[
                ft.dropdown.Option("active"),
                ft.dropdown.Option("inactive"),
                ft.dropdown.Option("pending"),
                ft.dropdown.Option("suspended"),
            ],
        )
        
        self.reset_password_checkbox = ft.Checkbox(label="Reset Password")
        
        # Create dialog for editing users
        self.user_dialog = ft.AlertDialog(
            title=ft.Text("Edit User"),
            content=ft.Column(
                [
                    self.user_id_field,
                    self.username_field,
                    self.full_name_field,
                    self.email_field,
                    self.department_field,
                    self.role_field,
                    self.status_field,
                    self.reset_password_checkbox,
                ],
                tight=True,
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                width=400,
                height=400,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=self.close_dialog),
                ft.TextButton("Save", on_click=self.save_user),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
    
    def build(self):
        """
        Build and return the user administration view.
        
        Returns:
            ft.Container: The user administration view container
        """
        # Load user data
        self.load_user_data()
        
        # Create filters row
        filters_row = ft.Row(
            [
                self.search_field,
                self.role_dropdown,
                self.status_dropdown,
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            wrap=True,
        )
        
        # Create data table container
        table_container = ft.Container(
            content=self.data_table,
            border=ft.border.all(1, ft.colors.GREY_300),
            border_radius=5,
            padding=10,
            expand=True,
        )
        
        # Return the complete view
        return ft.Container(
            content=ft.Column(
                [
                    ft.Text("User Administration", size=24, weight=ft.FontWeight.BOLD),
                    ft.Divider(),
                    filters_row,
                    ft.Container(height=10),
                    table_container,
                ],
                spacing=10,
                expand=True,
            ),
            padding=20,
            expand=True,
        )
    
    def load_user_data(self):
        """Load user data from the database."""
        # Query the database
        self.users = self.controller.db.execute_query("SELECT * FROM users ORDER BY username")
        self.filtered_users = self.users.copy()
        
        # Update the data table
        self.update_data_table()
    
    def update_data_table(self):
        """Update the data table with filtered users."""
        # Clear existing rows
        self.data_table.rows.clear()
        
        # Add rows for filtered users
        for user in self.filtered_users:
            # Format last login
            last_login = self.format_timestamp(user["last_login"]) if user["last_login"] else "Never"
            
            # Get status color
            status_color = self.get_status_color(user["status"])
            
            # Create row
            self.data_table.rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(user["username"])),
                        ft.DataCell(ft.Text(user["full_name"])),
                        ft.DataCell(ft.Text(user["email"])),
                        ft.DataCell(ft.Text(user["department"] or "")),
                        ft.DataCell(ft.Text(user["role"])),
                        ft.DataCell(ft.Text(user["status"], color=status_color)),
                        ft.DataCell(ft.Text(last_login)),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.icons.EDIT,
                                        tooltip="Edit",
                                        on_click=lambda e, user_id=user["id"]: self.show_edit_dialog(e, user_id),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.DELETE,
                                        tooltip="Delete",
                                        on_click=lambda e, user_id=user["id"]: self.delete_user(e, user_id),
                                    ),
                                ]
                            )
                        ),
                    ]
                )
            )
        
        # Update the page
        self.controller.page.update()
    
    def filter_users(self, e=None):
        """
        Filter users based on search and dropdown values.
        
        Args:
            e: The change event (optional)
        """
        search_text = self.search_field.value.lower() if self.search_field.value else ""
        role = self.role_dropdown.value
        status = self.status_dropdown.value
        
        # Filter users
        self.filtered_users = []
        for user in self.users:
            # Apply search filter
            if search_text and search_text not in user["username"].lower() and search_text not in user["full_name"].lower() and search_text not in user["email"].lower():
                continue
            
            # Apply role filter
            if role != "All" and user["role"] != role:
                continue
            
            # Apply status filter
            if status != "All" and user["status"] != status:
                continue
            
            # Add user to filtered list
            self.filtered_users.append(user)
        
        # Update the data table
        self.update_data_table()
    
    def show_edit_dialog(self, e, user_id):
        """
        Show dialog for editing a user.
        
        Args:
            e: The click event
            user_id: ID of the user to edit
        """
        # Find the user
        user = next((u for u in self.users if u["id"] == user_id), None)
        
        if not user:
            return
        
        # Set form fields
        self.user_id_field.value = str(user["id"])
        self.username_field.value = user["username"]
        self.full_name_field.value = user["full_name"]
        self.email_field.value = user["email"]
        self.department_field.value = user["department"] or ""
        self.role_field.value = user["role"]
        self.status_field.value = user["status"]
        self.reset_password_checkbox.value = False
        
        # Set dialog title
        self.user_dialog.title = ft.Text("Edit User")
        
        # Show the dialog
        self.controller.page.dialog = self.user_dialog
        self.user_dialog.open = True
        self.controller.page.update()
    
    def close_dialog(self, e):
        """
        Close the user dialog.
        
        Args:
            e: The click event
        """
        self.user_dialog.open = False
        self.controller.page.update()
    
    def save_user(self, e):
        """
        Save the user.
        
        Args:
            e: The click event
        """
        # Validate required fields
        if not self.full_name_field.value or not self.email_field.value or not self.role_field.value or not self.status_field.value:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Please fill in all required fields"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
            return
        
        # Prepare user data
        user_data = {
            "full_name": self.full_name_field.value,
            "email": self.email_field.value,
            "department": self.department_field.value if self.department_field.value else None,
            "role": self.role_field.value,
            "status": self.status_field.value,
        }
        
        # Add reset password if checked
        if self.reset_password_checkbox.value:
            # Generate a temporary password
            temp_password = "Reset123"  # In a real app, generate a random password
            hashed_password = self.controller.security.hash_password(temp_password)
            user_data["password"] = hashed_password
        
        # Update the user
        user_id = int(self.user_id_field.value)
        
        # Build the update query
        query_parts = []
        params = []
        
        for key, value in user_data.items():
            query_parts.append(f"{key} = ?")
            params.append(value)
        
        query = f"UPDATE users SET {', '.join(query_parts)} WHERE id = ?"
        params.append(user_id)
        
        # Execute the update
        self.controller.db.execute_query(query, tuple(params))
        
        # Log the update
        self.controller.db.log_audit(
            self.controller.current_user["id"],
            "update",
            "user",
            user_id,
            f"Updated user: {self.username_field.value}"
        )
        
        # Show reset password message if applicable
        if self.reset_password_checkbox.value:
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text(f"User updated and password reset to '{temp_password}'"),
                bgcolor=ft.colors.GREEN_400,
            )
        else:
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("User updated successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
        
        # Close the dialog
        self.user_dialog.open = False
        
        # Reload user data
        self.load_user_data()
        
        # Update the page
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def delete_user(self, e, user_id):
        """
        Delete a user.
        
        Args:
            e: The click event
            user_id: ID of the user to delete
        """
        # Find the user
        user = next((u for u in self.users if u["id"] == user_id), None)
        
        if not user:
            return
        
        # Prevent deleting yourself
        if user["id"] == self.controller.current_user["id"]:
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("You cannot delete your own account"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
            return
        
        # Show confirmation dialog
        def confirm_delete(e):
            # Delete the user
            self.controller.db.execute_query(
                "DELETE FROM users WHERE id = ?",
                (user_id,)
            )
            
            # Log the delete
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "delete",
                "user",
                user_id,
                f"Deleted user: {user['username']}"
            )
            
            # Close the dialog
            confirm_dialog.open = False
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("User deleted successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
            self.controller.page.snack_bar.open = True
            
            # Reload user data
            self.load_user_data()
            
            # Update the page
            self.controller.page.update()
        
        # Create confirmation dialog
        confirm_dialog = ft.AlertDialog(
            title=ft.Text("Confirm Delete"),
            content=ft.Text(f"Are you sure you want to delete user '{user['username']}'? This action cannot be undone."),
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(confirm_dialog, "open", False)),
                ft.TextButton("Delete", on_click=confirm_delete),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = confirm_dialog
        confirm_dialog.open = True
        self.controller.page.update()
    
    def format_timestamp(self, timestamp):
        """
        Format a timestamp for display.
        
        Args:
            timestamp (str): The timestamp to format
            
        Returns:
            str: Formatted timestamp
        """
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime("%m/%d/%Y %I:%M %p")
        except:
            return timestamp
    
    def get_status_color(self, status):
        """
        Get the color for a user status.
        
        Args:
            status (str): The status
            
        Returns:
            str: The color for the status
        """
        if status == "active":
            return ft.colors.GREEN
        elif status == "inactive":
            return ft.colors.GREY
        elif status == "pending":
            return ft.colors.ORANGE
        elif status == "suspended":
            return ft.colors.RED
        else:
            return ft.colors.BLACK
        """
        Initialize the user administration view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        self.users = []
        self.filtered_users = []
        
        # Create filter controls
        self.search_field = ft.TextField(
            label="Search",
            prefix_icon=ft.icons.SEARCH,
            on_change=self.filter_users,
            expand=True,
        )
        
        self.role_dropdown = ft.Dropdown(
            label="Role",
            on_change=self.filter_users,
            options=[
                ft.dropdown.Option("All"),
                ft.dropdown.Option("administrator"),
                ft.dropdown.Option("lab_manager"),
                ft.dropdown.Option("researcher"),
                ft.dropdown.Option("user"),
            ],
            value="All",
            width=200,
        )
        
        self.status_dropdown = ft.Dropdown(
            label="Status",
            on_change=self.filter_users,
            options=[
                ft.dropdown.Option("All"),
                ft.dropdown.Option("active"),
                ft.dropdown.Option("inactive"),
                ft.dropdown.Option("pending"),
                ft.dropdown.Option("suspended"),
            ],
            value="All",
            width=200,
        )
        
        # Create data table
        self.data_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("Username")),
                ft.DataColumn(ft.Text("Full Name")),
                ft.DataColumn(ft.Text("Email")),
                ft.DataColumn(ft.Text("Department")),
                ft.DataColumn(ft.Text("Role")),
                ft.DataColumn(ft.Text("Status")),
                ft.DataColumn(ft.Text("Last Login")),
                ft.DataColumn(ft.Text("Actions")),
            ],
            rows=[],
        )
        
        # Create form fields for editing users
        self.user_id_field = ft.TextField(visible=False)
        self.username_field = ft.TextField(label="Username", read_only=True)
        self.full_name_field = ft.TextField(label="Full Name", required=True)
        self.email_field = ft.TextField(label="Email", required=True)
        self.department_field = ft.TextField(label="Department")
        
        self.role_field = ft.Dropdown(
            label="Role",
            required=True,
            options=[
                ft.dropdown.Option("administrator"),
                ft.dropdown.Option("lab_manager"),
                ft.dropdown.Option("researcher"),
                ft.dropdown.Option("user"),
            ],
        )
        
        self.status_field = ft.Dropdown(
            label="Status",
            required=True,
            options=[
                ft.dropdown.Option("active"),
                ft.dropdown.Option("inactive"),
                ft.dropdown.Option("pending"),
                ft.dropdown.Option("suspended"),
            ],
        )
        
        self.reset_password_checkbox = ft.Checkbox(label="Reset Password")
        
        # Create dialog for editing users
        self.user_dialog = ft.AlertDialog(
            title=ft.Text("Edit User"),
            content=ft.Column(
                [
                    self.user_id_field,
                    self.username_field,
                    self.full_name_field,
                    self.email_field,
                    self.department_field,
                    self.role_field,
                    self.status_field,
                    self.reset_password_checkbox,
                ],
                tight=True,
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                width=400,
                height=400,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=self.close_dialog),
                ft.TextButton("Save", on_click=self.save_user),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
    
    def build(self):
        """
        Build and return the user administration view.
        
        Returns:
            ft.Container: The user administration view container
        """
        # Load user data
        self.load_user_data()
        
        # Create filters row
        filters_row = ft.Row(
            [
                self.search_field,
                self.role_dropdown,
                self.status_dropdown,
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            wrap=True,
        )
        
        # Create data table container
        table_container = ft.Container(
            content=self.data_table,
            border=ft.border.all(1, ft.colors.GREY_300),
            border_radius=5,
            padding=10,
            expand=True,
        )
        
        # Return the complete view
        return ft.Container(
            content=ft.Column(
                [
                    ft.Text("User Administration", size=24, weight=ft.FontWeight.BOLD),
                    ft.Divider(),
                    filters_row,
                    ft.Container(height=10),
                    table_container,
                ],
                spacing=10,
                expand=True,
            ),
            padding=20,
            expand=True,
        )
    
    def load_user_data(self):
        """Load user data from the database."""
        # Query the database
        self.users = self.controller.db.execute_query("SELECT * FROM users ORDER BY username")
        self.filtered_users = self.users.copy()
        
        # Update the data table
        self.update_data_table()
    
    def update_data_table(self):
        """Update the data table with filtered users."""
        # Clear existing rows
        self.data_table.rows.clear()
        
        # Add rows for filtered users
        for user in self.filtered_users:
            # Format last login
            last_login = self.format_timestamp(user["last_login"]) if user["last_login"] else "Never"
            
            # Get status color
            status_color = self.get_status_color(user["status"])
            
            # Create row
            self.data_table.rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(user["username"])),
                        ft.DataCell(ft.Text(user["full_name"])),
                        ft.DataCell(ft.Text(user["email"])),
                        ft.DataCell(ft.Text(user["department"] or "")),
                        ft.DataCell(ft.Text(user["role"])),
                        ft.DataCell(ft.Text(user["status"], color=status_color)),
                        ft.DataCell(ft.Text(last_login)),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.icons.EDIT,
                                        tooltip="Edit",
                                        on_click=lambda e, user_id=user["id"]: self.show_edit_dialog(e, user_id),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.DELETE,
                                        tooltip="Delete",
                                        on_click=lambda e, user_id=user["id"]: self.delete_user(e, user_id),
                                    ),
                                ]
                            )
                        ),
                    ]
                )
            )
        
        # Update the page
        self.controller.page.update()
    
    def filter_users(self, e=None):
        """
        Filter users based on search and dropdown values.
        
        Args:
            e: The change event (optional)
        """
        search_text = self.search_field.value.lower() if self.search_field.value else ""
        role = self.role_dropdown.value
        status = self.status_dropdown.value
        
        # Filter users
        self.filtered_users = []
        for user in self.users:
            # Apply search filter
            if search_text and search_text not in user["username"].lower() and search_text not in user["full_name"].lower() and search_text not in user["email"].lower():
                continue
            
            # Apply role filter
            if role != "All" and user["role"] != role:
                continue
            
            # Apply status filter
            if status != "All" and user["status"] != status:
                continue
            
            # Add user to filtered list
            self.filtered_users.append(user)
        
        # Update the data table
        self.update_data_table()
    
    def show_edit_dialog(self, e, user_id):
        """
        Show dialog for editing a user.
        
        Args:
            e: The click event
            user_id: ID of the user to edit
        """
        # Find the user
        user = next((u for u in self.users if u["id"] == user_id), None)
        
        if not user:
            return
        
        # Set form fields
        self.user_id_field.value = str(user["id"])
        self.username_field.value = user["username"]
        self.full_name_field.value = user["full_name"]
        self.email_field.value = user["email"]
        self.department_field.value = user["department"] or ""
        self.role_field.value = user["role"]
        self.status_field.value = user["status"]
        self.reset_password_checkbox.value = False
        
        # Set dialog title
        self.user_dialog.title = ft.Text("Edit User")
        
        # Show the dialog
        self.controller.page.dialog = self.user_dialog
        self.user_dialog.open = True
        self.controller.page.update()
    
    def close_dialog(self, e):
        """
        Close the user dialog.
        
        Args:
            e: The click event
        """
        self.user_dialog.open = False
        self.controller.page.update()
    
    def save_user(self, e):
        """
        Save the user.
        
        Args:
            e: The click event
        """
        # Validate required fields
        if not self.full_name_field.value or not self.email_field.value or not self.role_field.value or not self.status_field.value:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Please fill in all required fields"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
            return
        
        # Prepare user data
        user_data = {
            "full_name": self.full_name_field.value,
            "email": self.email_field.value,
            "department": self.department_field.value if self.department_field.value else None,
            "role": self.role_field.value,
            "status": self.status_field.value,
        }
        
        # Add reset password if checked
        if self.reset_password_checkbox.value:
            # Generate a temporary password
            temp_password = "Reset123"  # In a real app, generate a random password
            hashed_password = self.controller.security.hash_password(temp_password)
            user_data["password"] = hashed_password
        
        # Update the user
        user_id = int(self.user_id_field.value)
        
        # Build the update query
        query_parts = []
        params = []
        
        for key, value in user_data.items():
            query_parts.append(f"{key} = ?")
            params.append(value)
        
        query = f"UPDATE users SET {', '.join(query_parts)} WHERE id = ?"
        params.append(user_id)
        
        # Execute the update
        self.controller.db.execute_query(query, tuple(params))
        
        # Log the update
        self.controller.db.log_audit(
            self.controller.current_user["id"],
            "update",
            "user",
            user_id,
            f"Updated user: {self.username_field.value}"
        )
        
        # Show reset password message if applicable
        if self.reset_password_checkbox.value:
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text(f"User updated and password reset to '{temp_password}'"),
                bgcolor=ft.colors.GREEN_400,
            )
        else:
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("User updated successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
        
        # Close the dialog
        self.user_dialog.open = False
        
        # Reload user data
        self.load_user_data()
        
        # Update the page
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def delete_user(self, e, user_id):
        """
        Delete a user.
        
        Args:
            e: The click event
            user_id: ID of the user to delete
        """
        # Find the user
        user = next((u for u in self.users if u["id"] == user_id), None)
        
        if not user:
            return
        
        # Prevent deleting yourself
        if user["id"] == self.controller.current_user["id"]:
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("You cannot delete your own account"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
            return
        
        # Show confirmation dialog
        def confirm_delete(e):
            # Delete the user
            self.controller.db.execute_query(
                "DELETE FROM users WHERE id = ?",
                (user_id,)
            )
            
            # Log the delete
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "delete",
                "user",
                user_id,
                f"Deleted user: {user['username']}"
            )
            
            # Close the dialog
            confirm_dialog.open = False
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("User deleted successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
            self.controller.page.snack_bar.open = True
            
            # Reload user data
            self.load_user_data()
            
            # Update the page
            self.controller.page.update()
        
        # Create confirmation dialog
        confirm_dialog = ft.AlertDialog(
            title=ft.Text("Confirm Delete"),
            content=ft.Text(f"Are you sure you want to delete user '{user['username']}'? This action cannot be undone."),
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(confirm_dialog, "open", False)),
                ft.TextButton("Delete", on_click=confirm_delete),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = confirm_dialog
        confirm_dialog.open = True
        self.controller.page.update()
    
    def format_timestamp(self, timestamp):
        """
        Format a timestamp for display.
        
        Args:
            timestamp (str): The timestamp to format
            
        Returns:
            str: Formatted timestamp
        """
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime("%m/%d/%Y %I:%M %p")
        except:
            return timestamp
    
    def get_status_color(self, status):
        """
        Get the color for a user status.
        
        Args:
            status (str): The status
            
        Returns:
            str: The color for the status
        """
        if status == "active":
            return ft.colors.GREEN
        elif status == "inactive":
            return ft.colors.GREY
        elif status == "pending":
            return ft.colors.ORANGE
        elif status == "suspended":
            return ft.colors.RED
        else:
            return ft.colors.BLACK