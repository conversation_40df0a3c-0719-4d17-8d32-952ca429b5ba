#!/usr/bin/env python
"""
Enhanced Science Laboratory Management System Application
Incorporates all recommended improvements: dark mode, PWA, analytics, notifications
"""
import flet as ft
import os
import sys
import json
import threading
from datetime import datetime

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Import enhanced modules
from src.ui.theme_manager import ThemeManager, ThemeMode
from src.pwa.pwa_manager import PWAManager
from src.analytics.analytics_engine import AnalyticsEngine
from src.notifications.notification_manager import NotificationManager, NotificationChannel, NotificationType
from src.database.db_manager import DatabaseManager
from src.auth.auth_manager import AuthManager
from src.ui.modern_components import ModernCard, StatCard, ModernDataTable, NotificationBanner
from src.utils.logger import setup_logger

# Set up logging
logger = setup_logger("enhanced_lab_management")


class EnhancedLabManagementApp:
    """
    Enhanced Laboratory Management Application with modern features.
    """
    
    def __init__(self, page: ft.Page):
        """
        Initialize the enhanced application.
        
        Args:
            page: Flet page instance
        """
        self.page = page
        self.current_user = None
        self.current_view = "login"
        
        # Initialize managers
        self.db_manager = DatabaseManager()
        self.auth_manager = AuthManager(self.db_manager)
        self.theme_manager = ThemeManager(page)
        self.pwa_manager = PWAManager()
        self.analytics_engine = AnalyticsEngine(self.db_manager)
        self.notification_manager = NotificationManager(self.db_manager)
        
        # UI components
        self.app_bar = None
        self.nav_rail = None
        self.content_area = None
        self.notification_overlay = None
        
        # Setup application
        self.setup_page()
        self.setup_pwa()
        self.setup_notifications()
        self.create_layout()
        
        logger.info("Enhanced Lab Management App initialized")
    
    def setup_page(self):
        """Setup page configuration."""
        self.page.title = "Enhanced Lab Management System"
        self.page.window_width = 1400
        self.page.window_height = 900
        self.page.padding = 0
        self.page.scroll = ft.ScrollMode.AUTO
        
        # Apply theme
        self.theme_manager.apply_theme()
        
        # Setup error handling
        self.page.on_error = self.handle_error
    
    def setup_pwa(self):
        """Setup Progressive Web App features."""
        try:
            self.pwa_manager.setup_pwa()
            logger.info("PWA features enabled")
        except Exception as e:
            logger.error(f"Error setting up PWA: {e}")
    
    def setup_notifications(self):
        """Setup notification system."""
        # Subscribe to in-app notifications
        self.notification_manager.subscribe(
            NotificationChannel.IN_APP,
            self.handle_in_app_notification
        )
        
        # Start system alert checking
        self.start_alert_checker()
        
        logger.info("Notification system initialized")
    
    def start_alert_checker(self):
        """Start background thread for checking system alerts."""
        def check_alerts():
            while True:
                try:
                    self.notification_manager.check_system_alerts()
                    threading.Event().wait(300)  # Check every 5 minutes
                except Exception as e:
                    logger.error(f"Error in alert checker: {e}")
                    threading.Event().wait(60)  # Wait 1 minute on error
        
        alert_thread = threading.Thread(target=check_alerts, daemon=True)
        alert_thread.start()
    
    def handle_in_app_notification(self, notification):
        """Handle in-app notifications."""
        try:
            # Create notification banner
            banner = NotificationBanner(
                message=notification["message"],
                notification_type=notification["type"],
                dismissible=True,
                duration=5000
            )
            
            # Add to page overlay
            self.page.overlay.append(banner)
            self.page.update()
            
        except Exception as e:
            logger.error(f"Error handling in-app notification: {e}")
    
    def create_layout(self):
        """Create the main application layout."""
        # Create enhanced app bar
        self.create_app_bar()
        
        # Create navigation rail
        self.create_navigation_rail()
        
        # Create content area
        self.content_area = ft.Container(
            expand=True,
            content=self.create_login_view(),
            padding=20
        )
        
        # Create main layout
        main_layout = ft.Column([
            self.app_bar,
            ft.Container(height=1, bgcolor=self.theme_manager.get_themed_color("divider")),
            ft.Row([
                self.nav_rail,
                ft.VerticalDivider(width=1),
                self.content_area,
            ], expand=True),
        ], spacing=0, expand=True)
        
        self.page.add(main_layout)
        
        # Show login view initially
        self.nav_rail.visible = False
        self.page.update()
    
    def create_app_bar(self):
        """Create enhanced app bar with theme toggle."""
        # Theme toggle button
        theme_toggle = self.theme_manager.create_theme_toggle_button()
        
        # Notification button with badge
        notification_count = len(self.notification_manager.get_user_notifications(
            user_id=self.current_user["id"] if self.current_user else 1,
            unread_only=True
        ))
        
        notification_button = ft.IconButton(
            icon=ft.icons.NOTIFICATIONS,
            tooltip="Notifications",
            badge=str(notification_count) if notification_count > 0 else None,
            on_click=self.show_notifications
        )
        
        # Analytics button
        analytics_button = ft.IconButton(
            icon=ft.icons.ANALYTICS,
            tooltip="Analytics Dashboard",
            on_click=self.show_analytics
        )
        
        self.app_bar = ft.AppBar(
            leading=ft.Icon(ft.icons.SCIENCE, color=ft.colors.WHITE),
            title=ft.Row([
                ft.Text("Enhanced Lab Management", weight=ft.FontWeight.BOLD),
                ft.Icon(ft.icons.CHEVRON_RIGHT, size=20, color=ft.colors.WHITE70),
                ft.Text("Dashboard", color=ft.colors.WHITE70),
            ], spacing=5),
            center_title=False,
            bgcolor=self.theme_manager.get_themed_color("app_bar_background"),
            color=self.theme_manager.get_themed_color("on_primary"),
            toolbar_height=60,
            actions=[
                ft.IconButton(
                    icon=ft.icons.SEARCH,
                    tooltip="Global Search",
                    on_click=self.show_global_search
                ),
                analytics_button,
                notification_button,
                theme_toggle,
                ft.IconButton(
                    icon=ft.icons.ACCOUNT_CIRCLE,
                    tooltip="User Profile",
                    on_click=self.show_user_menu
                ),
                ft.IconButton(
                    icon=ft.icons.SETTINGS,
                    tooltip="Settings",
                    on_click=self.show_settings
                ),
            ],
            elevation=4,
        )
    
    def create_navigation_rail(self):
        """Create enhanced navigation rail."""
        self.nav_rail = ft.NavigationRail(
            selected_index=0,
            label_type=ft.NavigationRailLabelType.ALL,
            min_width=100,
            min_extended_width=220,
            extended=True,
            bgcolor=self.theme_manager.get_themed_color("nav_rail_background"),
            leading=ft.Container(
                content=ft.Column([
                    ft.Container(
                        content=ft.Icon(
                            name=ft.icons.SCIENCE_OUTLINED,
                            size=40,
                            color=self.theme_manager.get_themed_color("primary"),
                        ),
                        margin=ft.margin.only(top=20, bottom=5),
                    ),
                    ft.Text(
                        "Enhanced Lab",
                        size=14,
                        weight=ft.FontWeight.BOLD,
                        color=self.theme_manager.get_themed_color("primary"),
                    ),
                    ft.Divider(thickness=1, height=20),
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.only(bottom=10),
            ),
            destinations=[
                ft.NavigationRailDestination(
                    icon=ft.icons.DASHBOARD_OUTLINED,
                    selected_icon=ft.icons.DASHBOARD,
                    label="Dashboard",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.INVENTORY_OUTLINED,
                    selected_icon=ft.icons.INVENTORY,
                    label="Inventory",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.SCIENCE_OUTLINED,
                    selected_icon=ft.icons.SCIENCE,
                    label="Experiments",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.SCHEDULE_OUTLINED,
                    selected_icon=ft.icons.SCHEDULE,
                    label="Scheduling",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.ANALYTICS_OUTLINED,
                    selected_icon=ft.icons.ANALYTICS,
                    label="Analytics",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.HEALTH_AND_SAFETY_OUTLINED,
                    selected_icon=ft.icons.HEALTH_AND_SAFETY,
                    label="Safety",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.ASSESSMENT_OUTLINED,
                    selected_icon=ft.icons.ASSESSMENT,
                    label="Reports",
                ),
            ],
            on_change=self.nav_rail_change,
        )
    
    def create_login_view(self):
        """Create enhanced login view."""
        def login_click(e):
            username = username_field.value
            password = password_field.value
            
            if not username or not password:
                self.show_error("Please enter both username and password")
                return
            
            # Authenticate user
            success, user = self.auth_manager.login(username, password)
            
            if success:
                self.current_user = user
                self.show_dashboard()
                self.nav_rail.visible = True
                self.page.update()
                
                # Send login notification
                self.notification_manager.create_from_template(
                    "user_login",
                    {
                        "user_name": user["full_name"],
                        "ip_address": "127.0.0.1"  # Would get real IP in production
                    },
                    user_id=user["id"]
                )
                
                logger.info(f"User logged in: {username}")
            else:
                self.show_error("Invalid username or password")
        
        username_field = ft.TextField(
            label="Username",
            width=300,
            prefix_icon=ft.icons.PERSON,
        )
        
        password_field = ft.TextField(
            label="Password",
            width=300,
            password=True,
            can_reveal_password=True,
            prefix_icon=ft.icons.LOCK,
        )
        
        login_button = ft.ElevatedButton(
            "Login",
            width=300,
            height=50,
            on_click=login_click,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=8)
            )
        )
        
        return ft.Container(
            content=ft.Column([
                ft.Container(height=50),
                ft.Icon(ft.icons.SCIENCE, size=80, color=self.theme_manager.get_themed_color("primary")),
                ft.Text(
                    "Enhanced Lab Management System",
                    size=28,
                    weight=ft.FontWeight.BOLD,
                    color=self.theme_manager.get_themed_color("text_primary")
                ),
                ft.Text(
                    "Advanced laboratory management with modern features",
                    size=16,
                    color=self.theme_manager.get_themed_color("text_secondary")
                ),
                ft.Container(height=30),
                username_field,
                password_field,
                ft.Container(height=20),
                login_button,
                ft.Container(height=20),
                ft.Row([
                    ft.Text("Features: "),
                    ft.Chip(label=ft.Text("Dark Mode"), bgcolor=ft.colors.BLUE_100),
                    ft.Chip(label=ft.Text("PWA Ready"), bgcolor=ft.colors.GREEN_100),
                    ft.Chip(label=ft.Text("Analytics"), bgcolor=ft.colors.ORANGE_100),
                    ft.Chip(label=ft.Text("Smart Alerts"), bgcolor=ft.colors.PURPLE_100),
                ], wrap=True, alignment=ft.MainAxisAlignment.CENTER),
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            expand=True,
            alignment=ft.alignment.center
        )
    
    def show_dashboard(self):
        """Show enhanced dashboard with analytics."""
        try:
            # Get analytics data
            analytics = self.analytics_engine.get_dashboard_analytics()
            overview = analytics["overview"]
            
            # Create stat cards
            stat_cards = ft.Row([
                StatCard(
                    title="Total Items",
                    value=str(overview["total_inventory_items"]),
                    subtitle="Inventory items",
                    icon=ft.icons.INVENTORY,
                    color=self.theme_manager.get_themed_color("primary"),
                    width=280
                ),
                StatCard(
                    title="Active Experiments",
                    value=str(overview["active_experiments"]),
                    subtitle="Currently running",
                    icon=ft.icons.SCIENCE,
                    color=ft.colors.GREEN,
                    width=280
                ),
                StatCard(
                    title="Low Stock Items",
                    value=str(overview["low_stock_items"]),
                    subtitle="Need attention",
                    icon=ft.icons.WARNING,
                    color=ft.colors.ORANGE,
                    width=280
                ),
                StatCard(
                    title="Daily Activities",
                    value=str(overview["daily_activities"]),
                    subtitle="Last 24 hours",
                    icon=ft.icons.ACTIVITY_ZONE,
                    color=ft.colors.PURPLE,
                    width=280
                ),
            ], spacing=20, scroll=ft.ScrollMode.AUTO)
            
            # Create alerts section
            alerts = analytics["alerts"]
            alert_cards = []
            
            for alert in alerts[:5]:  # Show top 5 alerts
                alert_color = {
                    "critical": ft.colors.RED,
                    "high": ft.colors.ORANGE,
                    "medium": ft.colors.BLUE,
                    "low": ft.colors.GREEN
                }.get(alert["severity"], ft.colors.GREY)
                
                alert_cards.append(
                    ModernCard(
                        title=alert["title"],
                        subtitle=f"Severity: {alert['severity'].upper()}",
                        content=ft.Text(alert["message"]),
                        width=400,
                        actions=[
                            ft.TextButton("View Details", icon=ft.icons.VISIBILITY)
                        ]
                    )
                )
            
            alerts_section = ft.Column([
                ft.Text("🚨 System Alerts", size=20, weight=ft.FontWeight.BOLD),
                ft.Row(alert_cards, spacing=20, scroll=ft.ScrollMode.AUTO) if alert_cards else ft.Text("No alerts"),
            ], spacing=10)
            
            # Quick actions
            quick_actions = ft.Row([
                ft.ElevatedButton(
                    "Add Inventory Item",
                    icon=ft.icons.ADD,
                    on_click=lambda e: self.navigate_to("inventory")
                ),
                ft.ElevatedButton(
                    "New Experiment",
                    icon=ft.icons.SCIENCE,
                    on_click=lambda e: self.navigate_to("experiments")
                ),
                ft.ElevatedButton(
                    "View Analytics",
                    icon=ft.icons.ANALYTICS,
                    on_click=lambda e: self.show_analytics(None)
                ),
                ft.ElevatedButton(
                    "Generate Report",
                    icon=ft.icons.ASSESSMENT,
                    on_click=lambda e: self.navigate_to("reports")
                ),
            ], spacing=10, wrap=True)
            
            # Dashboard content
            dashboard_content = ft.Column([
                ft.Text(
                    f"Welcome back, {self.current_user['full_name']}!",
                    size=24,
                    weight=ft.FontWeight.BOLD
                ),
                ft.Text(
                    f"Last login: {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                    color=self.theme_manager.get_themed_color("text_secondary")
                ),
                ft.Container(height=20),
                ft.Text("📊 Overview", size=20, weight=ft.FontWeight.BOLD),
                stat_cards,
                ft.Container(height=30),
                alerts_section,
                ft.Container(height=30),
                ft.Text("⚡ Quick Actions", size=20, weight=ft.FontWeight.BOLD),
                quick_actions,
            ], spacing=10, scroll=ft.ScrollMode.AUTO)
            
            self.content_area.content = dashboard_content
            self.current_view = "dashboard"
            
        except Exception as e:
            logger.error(f"Error showing dashboard: {e}")
            self.show_error(f"Error loading dashboard: {e}")
    
    def show_analytics(self, e):
        """Show analytics dashboard."""
        try:
            analytics = self.analytics_engine.get_dashboard_analytics()
            
            # Create analytics content
            analytics_content = ft.Column([
                ft.Text("📈 Advanced Analytics", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("Comprehensive insights into laboratory operations", 
                       color=self.theme_manager.get_themed_color("text_secondary")),
                ft.Container(height=20),
                
                # Inventory analytics
                ModernCard(
                    title="📦 Inventory Analytics",
                    content=ft.Column([
                        ft.Text(f"Categories: {len(analytics['inventory']['category_distribution'])}"),
                        ft.Text(f"Locations: {len(analytics['inventory']['location_utilization'])}"),
                        ft.Text(f"Usage Trends: {len(analytics['inventory']['usage_trends'])} days"),
                    ]),
                    actions=[ft.TextButton("View Details", icon=ft.icons.ARROW_FORWARD)]
                ),
                
                ft.Container(height=20),
                
                # Experiment analytics
                ModernCard(
                    title="🧪 Experiment Analytics",
                    content=ft.Column([
                        ft.Text(f"Total Experiments: {sum(s['count'] for s in analytics['experiments']['status_distribution'])}"),
                        ft.Text(f"Top Researchers: {len(analytics['experiments']['top_researchers'])}"),
                        ft.Text(f"Monthly Trends: {len(analytics['experiments']['monthly_creation_trends'])} months"),
                    ]),
                    actions=[ft.TextButton("View Details", icon=ft.icons.ARROW_FORWARD)]
                ),
                
                ft.Container(height=20),
                
                # Predictions
                ModernCard(
                    title="🔮 Predictive Analytics",
                    content=ft.Column([
                        ft.Text(f"Usage Trend: {analytics['predictions'].get('usage_forecast', {}).get('trend', 'N/A')}"),
                        ft.Text(f"Restock Predictions: {len(analytics['predictions'].get('restock_predictions', []))} items"),
                        ft.Text("AI-powered insights for better planning"),
                    ]),
                    actions=[ft.TextButton("View Predictions", icon=ft.icons.PSYCHOLOGY)]
                ),
            ], scroll=ft.ScrollMode.AUTO)
            
            self.content_area.content = analytics_content
            self.current_view = "analytics"
            self.page.update()
            
        except Exception as e:
            logger.error(f"Error showing analytics: {e}")
            self.show_error(f"Error loading analytics: {e}")
    
    def show_notifications(self, e):
        """Show notifications panel."""
        try:
            notifications = self.notification_manager.get_user_notifications(
                user_id=self.current_user["id"] if self.current_user else 1,
                limit=20
            )
            
            notification_items = []
            for notif in notifications:
                notification_items.append(
                    ft.ListTile(
                        leading=ft.Icon(
                            ft.icons.NOTIFICATIONS,
                            color=ft.colors.BLUE if not notif["is_read"] else ft.colors.GREY
                        ),
                        title=ft.Text(
                            notif["title"],
                            weight=ft.FontWeight.BOLD if not notif["is_read"] else ft.FontWeight.NORMAL
                        ),
                        subtitle=ft.Text(notif["message"]),
                        trailing=ft.Text(notif["created_at"][:16]),
                        on_click=lambda e, nid=notif["id"]: self.mark_notification_read(nid)
                    )
                )
            
            notifications_content = ft.Column([
                ft.Text("🔔 Notifications", size=24, weight=ft.FontWeight.BOLD),
                ft.Container(height=10),
                ft.Column(notification_items) if notification_items else ft.Text("No notifications"),
            ], scroll=ft.ScrollMode.AUTO)
            
            self.content_area.content = notifications_content
            self.current_view = "notifications"
            self.page.update()
            
        except Exception as e:
            logger.error(f"Error showing notifications: {e}")
            self.show_error(f"Error loading notifications: {e}")
    
    def mark_notification_read(self, notification_id: int):
        """Mark notification as read."""
        try:
            self.notification_manager.mark_as_read(
                notification_id,
                self.current_user["id"] if self.current_user else None
            )
            # Refresh notifications view
            self.show_notifications(None)
        except Exception as e:
            logger.error(f"Error marking notification as read: {e}")
    
    def show_settings(self, e):
        """Show settings dialog."""
        theme_dialog = self.theme_manager.create_theme_settings_dialog()
        self.page.dialog = theme_dialog
        theme_dialog.open = True
        self.page.update()
    
    def show_global_search(self, e):
        """Show global search."""
        self.show_info("Global search feature coming soon!")
    
    def show_user_menu(self, e):
        """Show user menu."""
        self.show_info("User menu feature coming soon!")
    
    def nav_rail_change(self, e):
        """Handle navigation rail changes."""
        index = e.control.selected_index
        
        if index == 0:
            self.show_dashboard()
        elif index == 1:
            self.navigate_to("inventory")
        elif index == 2:
            self.navigate_to("experiments")
        elif index == 3:
            self.navigate_to("scheduling")
        elif index == 4:
            self.show_analytics(None)
        elif index == 5:
            self.navigate_to("safety")
        elif index == 6:
            self.navigate_to("reports")
    
    def navigate_to(self, view_name: str):
        """Navigate to a specific view."""
        self.content_area.content = ft.Container(
            content=ft.Column([
                ft.Text(f"🚧 {view_name.title()} View", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("This view is under development with enhanced features."),
                ft.Container(height=20),
                ft.ElevatedButton(
                    "Back to Dashboard",
                    icon=ft.icons.DASHBOARD,
                    on_click=lambda e: self.show_dashboard()
                )
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            expand=True,
            alignment=ft.alignment.center
        )
        self.current_view = view_name
        self.page.update()
    
    def show_error(self, message: str):
        """Show error message."""
        self.page.show_snack_bar(
            ft.SnackBar(
                content=ft.Text(message),
                bgcolor=ft.colors.RED,
                action="OK"
            )
        )
    
    def show_info(self, message: str):
        """Show info message."""
        self.page.show_snack_bar(
            ft.SnackBar(
                content=ft.Text(message),
                bgcolor=ft.colors.BLUE,
                action="OK"
            )
        )
    
    def handle_error(self, e):
        """Handle application errors."""
        logger.error(f"Application error: {e}")
        self.show_error(f"An error occurred: {e}")


def main(page: ft.Page):
    """Main application entry point."""
    app = EnhancedLabManagementApp(page)


if __name__ == "__main__":
    # Run the enhanced application
    ft.app(target=main, port=8555, view=ft.WEB_BROWSER)
