import flet as ft
import os
import sys
import logging
from datetime import datetime
import json
import importlib

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Now we can use absolute imports
from src.utils.database import Database
from src.utils.auth import AuthManager
from src.utils.logger import setup_logger
from src.utils.config import Config

class AppController:
    """
    Main application controller for the Science Laboratory Management System.
    Handles navigation, view management, authentication, and application state.
    """
    
    def __init__(self, page: ft.Page):
        """
        Initialize the application controller.
        
        Args:
            page (ft.Page): The Flet page object
        """
        self.page = page
        self.setup_page()
        
        # Initialize logger
        self.logger = setup_logger("app_controller")
        self.logger.info("Initializing application controller")
        
        # Load configuration
        self.config = Config()
        self.config.load()
        
        # Initialize database
        self.db = Database(self.config.get("database", "path"))
        
        # Initialize authentication manager
        self.auth = AuthManager(self)
        
        # Set current user to None (not logged in)
        self.current_user = None
        
        # Initialize views dictionary
        self.views = {}
        
        # Set current view to None
        self.current_view = None
        
        # Initialize navigation history
        self.nav_history = []
        
        # Create main layout containers
        self.create_layout()
        
        # Register routes
        self.register_routes()
        
        # Check if user is already logged in (from session)
        self.check_session()
    
    def setup_page(self):
        """Configure the application page settings."""
        # Set page properties
        self.page.title = "Science Laboratory Management System"
        self.page.padding = 0
        self.page.theme_mode = ft.ThemeMode.LIGHT
        
        # Set window properties using the new API
        if hasattr(self.page, 'window') and self.page.window:
            self.page.window.width = 1200
            self.page.window.height = 800
            self.page.window.min_width = 800
            self.page.window.min_height = 600
            
            # Handle window close
            self.page.window.on_event = self.handle_window_event
        
        # Set up theme
        self.page.theme = ft.Theme(
            color_scheme_seed=ft.Colors.BLUE,  # Use Colors instead of colors
            visual_density=ft.VisualDensity.COMFORTABLE,  # Use VisualDensity instead of ThemeVisualDensity
        )
        
        # Set up error handling
        self.page.on_error = self.handle_error
    
    def create_layout(self):
        """Create the main application layout."""
        # Create app bar
        self.app_bar = ft.AppBar(
            title=ft.Text("Science Laboratory Management System"),
            center_title=False,
            bgcolor=ft.Colors.BLUE,  # Use Colors instead of colors
            color=ft.Colors.WHITE,   # Use Colors instead of colors
            actions=[
                ft.IconButton(
                    icon=ft.Icons.ACCOUNT_CIRCLE,  # Use Icons instead of icons
                    tooltip="User Profile",
                    on_click=self.show_user_menu,
                ),
                ft.IconButton(
                    icon=ft.Icons.NOTIFICATIONS,  # Use Icons instead of icons
                    tooltip="Notifications",
                    on_click=self.show_notifications,
                    badge=ft.Badge(text="0", bgcolor=ft.Colors.RED_400),  # Use text instead of value
                    visible=False,  # Will be shown after login
                ),
                ft.IconButton(
                    icon=ft.Icons.HELP_OUTLINE,  # Use Icons instead of icons
                    tooltip="Help",
                    on_click=self.show_help,
                ),
            ],
        )
        
        # Create navigation rail
        self.nav_rail = ft.NavigationRail(
            selected_index=0,
            label_type=ft.NavigationRailLabelType.ALL,
            min_width=100,
            min_extended_width=200,
            extended=True,
            group_alignment=-0.9,
            destinations=[
                # These will be populated after login
            ],
            on_change=self.nav_rail_change,
        )
        
        # Create user menu
        self.user_menu = ft.PopupMenuButton(
            tooltip="User Menu",
            icon=ft.Icons.ACCOUNT_CIRCLE,
            items=[
                ft.PopupMenuItem(
                    text="Login",
                    icon=ft.Icons.LOGIN,
                    on_click=lambda e: self.navigate_to("login"),
                ),
                ft.PopupMenuItem(
                    text="Register",
                    icon=ft.Icons.PERSON_ADD,
                    on_click=lambda e: self.navigate_to("register"),
                ),
            ],
            visible=False,  # Will be shown by show_user_menu
        )
        
        # Create breadcrumb navigation
        self.breadcrumb = ft.Row(
            [
                ft.TextButton(
                    text="Home",
                    icon=ft.Icons.HOME,
                    on_click=lambda e: self.navigate_to("dashboard"),
                ),
            ],
            spacing=5,
        )
        
        # Create main content container
        self.content = ft.Container(
            expand=True,
            content=ft.Text("Loading..."),
        )
        
        # Create status bar
        self.status_bar = ft.Container(
            content=ft.Row(
                [
                    ft.Text("Ready", size=12),
                    ft.Container(expand=True),
                    ft.Text(f"Version: {self.config.get('app', 'version')}", size=12),
                ],
                spacing=10,
            ),
            padding=5,
            bgcolor=ft.Colors.BLUE_50,
        )
        
        # Create main layout
        self.page.add(
            ft.Column(
                [
                    self.app_bar,
                    ft.Container(height=1, bgcolor=ft.Colors.BLUE_200),
                    ft.Container(
                        content=ft.Row(
                            [
                                self.breadcrumb,
                            ],
                            spacing=10,
                        ),
                        padding=10,
                    ),
                    ft.Row(
                        [
                            self.nav_rail,
                            ft.VerticalDivider(width=1),
                            self.content,
                        ],
                        expand=True,
                    ),
                    self.status_bar,
                ],
                spacing=0,
                expand=True,
            )
        )
    
    def register_routes(self):
        """Register application routes and views."""
        # Define routes
        routes = {
            # Authentication views
            "login": {"module": "src.views.login_view", "class": "LoginView"},
            "register": {"module": "src.views.registration_view", "class": "RegistrationView"},
            "password_reset": {"module": "src.views.password_reset_view", "class": "PasswordResetView"},
            
            # Main application views
            "dashboard": {"module": "src.views.dashboard_view", "class": "DashboardView"},
            "inventory": {"module": "src.views.inventory_view", "class": "InventoryView"},
            "experiments": {"module": "src.views.experiments_view", "class": "ExperimentsView"},
            "scheduling": {"module": "src.views.scheduling_view", "class": "SchedulingView"},
            "safety": {"module": "src.views.safety_view", "class": "SafetyView"},
            "reports": {"module": "src.views.reports_view", "class": "ReportsView"},
            "audit_trail": {"module": "src.views.audit_trail_view", "class": "AuditTrailView"},
            "data_export": {"module": "src.views.data_export_view", "class": "DataExportView"},
            "user_profile": {"module": "src.views.user_profile_view", "class": "UserProfileView"},
            "user_admin": {"module": "src.views.user_admin_view", "class": "UserAdminView"},
            "settings": {"module": "src.views.settings_view", "class": "SettingsView"},
            "lab_config": {"module": "src.views.lab_config_view", "class": "LabConfigView"},
            "notifications": {"module": "src.views.notification_view", "class": "NotificationView"},
        }
        
        # Store routes
        self.routes = routes
    
    def load_view(self, route_name):
        """
        Load a view by route name.
        
        Args:
            route_name (str): The route name to load
            
        Returns:
            object: The view instance or None if not found
        """
        if route_name not in self.routes:
            self.logger.error(
                f"Route not found: {route_name}"
            )
            return None
        
        # Check if view is already loaded
        if route_name in self.views:
            return self.views[route_name]
        
        # Get route info
        route = self.routes[route_name]
        
        try:
            # Import the module
            module = importlib.import_module(route["module"])
            
            # Get the class
            view_class = getattr(module, route["class"])
            
            # Create an instance
            view_instance = view_class(self)
            
            # Store the instance
            self.views[route_name] = view_instance
            
            return view_instance
        
        except Exception as e:
            self.logger.error(
                f"Error loading view {route_name}: {str(e)}"
            )
            self.show_error(f"Error loading view: {str(e)}")
            return None
    
    def navigate_to(self, route_name, params=None):
        """
        Navigate to a specific route.
        
        Args:
            route_name (str): The route name to navigate to
            params (dict, optional): Parameters to pass to the view
        """
        # Check if user is authenticated for protected routes
        if route_name not in ["login", "register", "password_reset"]:
            if not self.current_user:
                # Store the requested route for after login
                self.auth.set_redirect_route(route_name, params)
                
                # Redirect to login
                route_name = "login"
                params = None
        
        # Load the view
        view = self.load_view(route_name)
        
        if not view:
            return
        
        # Update navigation history
        if self.current_view:
            self.nav_history.append((self.current_view, params))
        
        # Set current view
        self.current_view = route_name
        
        # Update breadcrumb
        self.update_breadcrumb(route_name)
        
        # Update navigation rail
        self.update_nav_rail(route_name)
        
        # Update content
        self.content.content = view.build()
        
        # Update status
        self.update_status(f"Navigated to {route_name}")
        
        # Update page
        self.page.update()
        
        # Log navigation
        self.logger.info(f"Navigated to {route_name}")
        
        # Log in audit trail if user is logged in
        if self.current_user:
            self.db.log_audit(
                self.current_user["id"],
                "view",
                route_name,
                None,
                f"Navigated to {route_name}"
            )
    
    def navigate_back(self):
        """Navigate back to the previous view."""
        if not self.nav_history:
            # If no history, go to dashboard
            self.navigate_to("dashboard")
            return
        
        # Get the last view from history
        last_view, params = self.nav_history.pop()
        
        # Navigate to it
        self.navigate_to(last_view, params)
    
    def update_breadcrumb(self, route_name):
        """
        Update the breadcrumb navigation.
        
        Args:
            route_name (str): The current route name
        """
        # Clear existing breadcrumb items (except Home)
        if len(self.breadcrumb.controls) > 1:
            self.breadcrumb.controls = self.breadcrumb.controls[:1]
        
        # Add separator
        self.breadcrumb.controls.append(ft.Text(">", size=12))
        
        # Add current route
        route_display_name = route_name.replace("_", " ").title()
        self.breadcrumb.controls.append(ft.Text(route_display_name, size=14))
    
    def update_nav_rail(self, current_route):
        """
        Update the navigation rail based on user role and current route.
        
        Args:
            current_route (str): The current route name
        """
        # Skip if user is not logged in
        if not self.current_user:
            self.nav_rail.visible = False
            # Make sure we don't try to update destinations when not logged in
            return
        
        # Show navigation rail
        self.nav_rail.visible = True
        
        # Define navigation items based on user role
        nav_items = [
            {"route": "dashboard", "text": "Dashboard", "icon": ft.icons.DASHBOARD},
            {"route": "inventory", "text": "Inventory", "icon": ft.icons.INVENTORY},
            {"route": "experiments", "text": "Experiments", "icon": ft.icons.SCIENCE},
            {"route": "scheduling", "text": "Scheduling", "icon": ft.icons.CALENDAR_TODAY},
            {"route": "safety", "text": "Safety", "icon": ft.icons.HEALTH_AND_SAFETY},
            {"route": "reports", "text": "Reports", "icon": ft.icons.ANALYTICS},
        ]
        
        # Add admin items if user has admin role
        if self.current_user["role"] in ["admin", "manager"]:
            nav_items.extend([
                {"route": "user_admin", "text": "User Admin", "icon": ft.icons.ADMIN_PANEL_SETTINGS},
                {"route": "lab_config", "text": "Lab Config", "icon": ft.icons.SETTINGS},
            ])
        
        # Create destinations
        destinations = []
        selected_index = 0
        
        for i, item in enumerate(nav_items):
            destinations.append(
                ft.NavigationRailDestination(
                    icon=ft.Icon(item["icon"]),
                    selected_icon=ft.Icon(item["icon"]),
                    label=item["text"],
                )
            )
            
            # Set selected index
            if item["route"] == current_route:
                selected_index = i
        
        # Update navigation rail
        self.nav_rail.destinations = destinations
        # Only set selected_index if there are destinations
        if destinations and len(destinations) > 0:
            self.nav_rail.selected_index = selected_index
    
    def nav_rail_change(self, e):
        """
        Handle navigation rail selection change.
        
        Args:
            e: The change event
        """
        # Get selected index
        index = e.control.selected_index
        
        # Define navigation items (must match the ones in update_nav_rail)
        nav_items = [
            {"route": "dashboard", "text": "Dashboard", "icon": ft.icons.DASHBOARD},
            {"route": "inventory", "text": "Inventory", "icon": ft.icons.INVENTORY},
            {"route": "experiments", "text": "Experiments", "icon": ft.icons.SCIENCE},
            {"route": "scheduling", "text": "Scheduling", "icon": ft.icons.CALENDAR_TODAY},
            {"route": "safety", "text": "Safety", "icon": ft.icons.HEALTH_AND_SAFETY},
            {"route": "reports", "text": "Reports", "icon": ft.icons.ANALYTICS},
        ]
        
        # Add admin items if user has admin role
        if self.current_user["role"] in ["admin", "manager"]:
            nav_items.extend([
                {"route": "user_admin", "text": "User Admin", "icon": ft.icons.ADMIN_PANEL_SETTINGS},
                {"route": "lab_config", "text": "Lab Config", "icon": ft.icons.SETTINGS},
            ])
        
        # Navigate to the selected route
        if 0 <= index < len(nav_items):
            self.navigate_to(nav_items[index]["route"])
    
    def update_status(self, message):
        """
        Update the status bar message.
        
        Args:
            message (str): The status message to display
        """
        # Update status text
        self.status_bar.content.controls[0].value = message
        
        # Add timestamp
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_bar.content.controls[2].value = f"{timestamp} | Version: {self.config.get('app', 'version')}"
    
    def show_user_menu(self, e):
        """
        Show the user menu.
        
        Args:
            e: The click event
        """
        # Update menu items based on authentication state
        if self.current_user:
            self.user_menu.items = [
                ft.PopupMenuItem(
                    text=f"Logged in as {self.current_user['username']}",
                    icon=ft.icons.PERSON,
                    disabled=True,
                ),
                ft.PopupMenuItem(
                    text="User Profile",
                    icon=ft.icons.ACCOUNT_CIRCLE,
                    on_click=lambda e: self.navigate_to("user_profile"),
                ),
                ft.PopupMenuItem(
                    text="Notifications",
                    icon=ft.icons.NOTIFICATIONS,
                    on_click=lambda e: self.navigate_to("notifications"),
                ),
                ft.PopupMenuItem(
                    text="Settings",
                    icon=ft.icons.SETTINGS,
                    on_click=lambda e: self.navigate_to("settings"),
                ),
                ft.PopupMenuItem(divider=True),
                ft.PopupMenuItem(
                    text="Logout",
                    icon=ft.icons.LOGOUT,
                    on_click=self.logout,
                ),
            ]
        else:
            self.user_menu.items = [
                ft.PopupMenuItem(
                    text="Login",
                    icon=ft.icons.LOGIN,
                    on_click=lambda e: self.navigate_to("login"),
                ),
                ft.PopupMenuItem(
                    text="Register",
                    icon=ft.icons.PERSON_ADD,
                    on_click=lambda e: self.navigate_to("register"),
                ),
            ]
        
        # Show the menu
        self.user_menu.visible = True
        self.page.overlay.append(self.user_menu)
        self.page.update()
        
        # Position the menu near the account icon
        self.user_menu.open = True
        self.page.update()
    
    def show_notifications(self, e):
        """
        Show notifications panel.
        
        Args:
            e: The click event
        """
        # Navigate to notifications view
        self.navigate_to("notifications")
    
    def show_help(self, e):
        """
        Show help dialog.
        
        Args:
            e: The click event
        """
        # Create help dialog
        help_dialog = ft.AlertDialog(
            title=ft.Text("Help"),
            content=ft.Column(
                [
                    ft.Text("Science Laboratory Management System"),
                    ft.Text(f"Version: {self.config.get('app', 'version')}"),
                    ft.Container(height=20),
                    ft.Text("For assistance, please contact:"),
                    ft.Text("<EMAIL>"),
                    ft.Container(height=20),
                    ft.Text("Documentation:"),
                    ft.TextButton(
                        text="User Manual",
                        icon=ft.icons.BOOK,
                        on_click=lambda e: self.open_documentation("user_manual"),
                    ),
                    ft.TextButton(
                        text="Quick Start Guide",
                        icon=ft.icons.QUICKREPLY,
                        on_click=lambda e: self.open_documentation("quick_start"),
                    ),
                ],
                scroll=ft.ScrollMode.AUTO,
                width=400,
                height=300,
            ),
            actions=[
                ft.TextButton("Close", on_click=lambda e: setattr(help_dialog, "open", False)),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.page.dialog = help_dialog
        help_dialog.open = True
        self.page.update()
    
    def open_documentation(self, doc_type):
        """
        Open documentation.
        
        Args:
            doc_type (str): The type of documentation to open
        """
        # In a real application, this would open documentation
        # For this example, we'll just show a message
        self.page.snack_bar = ft.SnackBar(
            content=ft.Text(f"Opening {doc_type} documentation..."),
            bgcolor=ft.colors.BLUE_400,
        )
        self.page.snack_bar.open = True
        self.page.update()
    
    def show_error(self, message):
        """
        Show an error message.
        
        Args:
            message (str): The error message to display
        """
        # Create error dialog
        error_dialog = ft.AlertDialog(
            title=ft.Text("Error"),
            content=ft.Text(message),
            actions=[
                ft.TextButton("OK", on_click=lambda e: setattr(error_dialog, "open", False)),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog using the new API
        if hasattr(self.page, 'overlay') and self.page.overlay is not None:
            self.page.overlay.append(error_dialog)
            error_dialog.open = True
            self.page.update()
        else:
            # Fallback to the old API
            self.page.dialog = error_dialog
            error_dialog.open = True
            self.page.update()
        
        # Log the error
        self.logger.error(
            f"Error: {message}"
        )
    
    def handle_error(self, e):
        """
        Handle application errors.
        
        Args:
            e: The error event
        """
        # Log the error
        self.logger.error(
            f"Application error: {str(e)}"
        )
        
        # Show error message
        self.show_error(f"An error occurred: {str(e)}")
    
    def handle_window_event(self, e):
        """
        Handle window events.
        
        Args:
            e: The window event
        """
        # Check if the event is a close event
        if hasattr(e, 'data') and e.data == "close":
            # Perform cleanup before closing
            self.cleanup()
        # For the new API, the event might be different
        elif hasattr(e, 'type') and e.type == "close":
            # Perform cleanup before closing
            self.cleanup()
    
    def cleanup(self):
        """Perform cleanup before application exit."""
        # Close database connection
        if hasattr(self, "db") and self.db:
            self.db.close()
        
        # Log application exit
        if hasattr(self, "logger") and self.logger:
            self.logger.info("Application exiting")
    
    def check_session(self):
        """Check if user is already logged in from session."""
        # Try to get user from session
        user_id = self.page.session.get("user_id")
        
        if user_id:
            # Get user from database
            user = self.db.execute_query(
                "SELECT * FROM users WHERE id = ?",
                (user_id,)
            )
            
            if user and len(user) > 0:
                # Set current user
                self.current_user = user[0]
                
                # Update UI for logged in user
                self.update_ui_for_logged_in_user()
                
                # Navigate to dashboard or saved redirect
                redirect_route = self.auth.get_redirect_route()
                if redirect_route:
                    route, params = redirect_route
                    self.navigate_to(route, params)
                else:
                    self.navigate_to("dashboard")
                
                # Log login from session
                self.logger.info(f"User {self.current_user['username']} logged in from session")
                
                return
        
        # If no valid session, navigate to login
        self.navigate_to("login")
    
    def authenticate(self, username, password, ip_address=None, user_agent=None):
        """
        Authenticate a user with username and password.
        
        Args:
            username (str): The username
            password (str): The password
            ip_address (str, optional): The client IP address
            user_agent (str, optional): The client user agent
            
        Returns:
            tuple: (user, error, requires_2fa) where:
                - user is the user data if authentication successful, None otherwise
                - error is an error message if authentication failed, None otherwise
                - requires_2fa is a boolean indicating if 2FA is required
        """
        try:
            # Query user by username
            users = self.db.execute_query(
                "SELECT * FROM users WHERE username = ? AND is_active = 1",
                (username,)
            )
            
            if not users or len(users) == 0:
                self.logger.warning(
                    f"Authentication failed: User not found or inactive - {username}"
                )
                return None, "Invalid username or password", False
            
            user = users[0]
            
            # Get the user's salt
            salt = user.get("salt", "")
            
            # Hash the password with the salt using SHA-256
            import hashlib
            salted_password = password + salt
            hash_obj = hashlib.sha256(salted_password.encode())
            hashed_password = hash_obj.hexdigest()
            
            # Verify password
            if hashed_password != user["password_hash"]:
                self.logger.warning(
                    f"Authentication failed: Invalid password - {username}"
                )
                
                # Update failed login attempts
                self.db.execute_query(
                    "UPDATE users SET failed_login_attempts = failed_login_attempts + 1 WHERE id = ?",
                    (user["id"],)
                )
                
                return None, "Invalid username or password", False
            
            # Check if 2FA is enabled for this user
            if user.get("totp_enabled", 0) == 1:
                self.logger.info(f"2FA required for user: {username}")
                return user, None, True
            
            # Reset failed login attempts
            self.db.execute_query(
                "UPDATE users SET failed_login_attempts = 0 WHERE id = ?",
                (user["id"],)
            )
            
            self.logger.info(f"Authentication successful: {username}")
            return user, None, False
            
        except Exception as e:
            self.logger.error(
                f"Authentication error: {str(e)}"
            )
            return None, f"An error occurred during authentication: {str(e)}", False
    
    def verify_2fa(self, user_id, verification_code, ip_address=None, user_agent=None):
        """
        Verify a 2FA code.
        
        Args:
            user_id (int): The user ID
            verification_code (str): The verification code
            ip_address (str, optional): The client IP address
            user_agent (str, optional): The client user agent
            
        Returns:
            tuple: (user, error) where:
                - user is the user data if verification successful, None otherwise
                - error is an error message if verification failed, None otherwise
        """
        try:
            # Get user
            users = self.db.execute_query(
                "SELECT * FROM users WHERE id = ? AND is_active = 1",
                (user_id,)
            )
            
            if not users or len(users) == 0:
                self.logger.warning(
                    f"2FA verification failed: User not found or inactive - ID {user_id}"
                )
                return None, "Invalid user"
            
            user = users[0]
            
            # Verify code (simplified for now)
            # In a real app, you would use a proper TOTP library
            if verification_code == "123456":  # Placeholder for actual verification
                self.logger.info(f"2FA verification successful: {user['username']}")
                
                # Reset failed login attempts
                self.db.execute_query(
                    "UPDATE users SET failed_login_attempts = 0 WHERE id = ?",
                    (user["id"],)
                )
                
                return user, None
            else:
                self.logger.warning(
                    f"2FA verification failed: Invalid code - {user['username']}"
                )
                return None, "Invalid verification code"
                
        except Exception as e:
            self.logger.error(
                f"2FA verification error: {str(e)}"
            )
            return None, f"An error occurred during verification: {str(e)}"
    
    def complete_login(self, user):
        """
        Complete the login process after successful authentication.
        
        Args:
            user (dict): The user data
        """
        # Set current user
        self.current_user = user
        
        # Store user ID in session
        self.page.session.set("user_id", user["id"])
        
        # Update last login timestamp
        self.db.execute_query(
            "UPDATE users SET last_login = ? WHERE id = ?",
            (datetime.now().isoformat(), user["id"])
        )
        
        # Update UI for logged in user
        self.update_ui_for_logged_in_user()
        
        # Log login in audit trail
        self.db.log_audit(
            user["id"],
            "login",
            "user",
            user["id"],
            f"User {user['username']} logged in"
        )
        
        # Navigate to dashboard or saved redirect
        redirect_route = self.auth.get_redirect_route()
        if redirect_route:
            route, params = redirect_route
            self.navigate_to(route, params)
        else:
            self.navigate_to("dashboard")
        
        # Show welcome message
        self.page.snack_bar = ft.SnackBar(
            content=ft.Text(f"Welcome, {user['full_name']}!"),
            bgcolor=ft.colors.GREEN_400,
        )
        self.page.snack_bar.open = True
        self.page.update()
    
    def logout(self, e=None):
        """
        Handle user logout.
        
        Args:
            e: The click event (optional)
        """
        if self.current_user:
            # Log logout in audit trail
            self.db.log_audit(
                self.current_user["id"],
                "logout",
                "user",
                self.current_user["id"],
                f"User {self.current_user['username']} logged out"
            )
        
        # Clear current user
        self.current_user = None
        
        # Clear session
        self.page.session.clear()
        
        # Update UI for logged out user
        self.update_ui_for_logged_out_user()
        
        # Navigate to login
        self.navigate_to("login")
        
        # Show logout message
        self.page.snack_bar = ft.SnackBar(
            content=ft.Text("You have been logged out."),
            bgcolor=ft.colors.BLUE_400,
        )
        self.page.snack_bar.open = True
        self.page.update()
    
    def update_ui_for_logged_in_user(self):
        """Update UI elements for logged in user."""
        # Show notifications button
        self.app_bar.actions[1].visible = True
        
        # Update app bar title
        self.app_bar.title = ft.Text(f"Science Laboratory Management System - {self.current_user['department']}")
        
        # Show navigation rail
        self.nav_rail.visible = True
        
        # Update navigation rail
        self.update_nav_rail(self.current_view or "dashboard")
        
        # Update the page
        self.page.update()
    
    def update_ui_for_logged_out_user(self):
        """Update UI elements for logged out user."""
        # Hide notifications button
        self.app_bar.actions[1].visible = False
        
        # Reset app bar title
        self.app_bar.title = ft.Text("Science Laboratory Management System")
        
        # Hide navigation rail
        self.nav_rail.visible = False
        
        # Clear navigation rail
        self.nav_rail.destinations = []
        
        # Update the page
        self.page.update()
    
    def check_permission(self, permission):
        """
        Check if current user has a specific permission.
        
        Args:
            permission (str): The permission to check
            
        Returns:
            bool: True if user has permission, False otherwise
        """
        # If no user is logged in, no permissions
        if not self.current_user:
            return False
        
        # Admin role has all permissions
        if self.current_user["role"] == "admin":
            return True
        
        # Check role-based permissions
        role_permissions = {
            "manager": [
                "view_reports", "export_data", "manage_inventory", 
                "manage_experiments", "manage_schedules", "manage_safety",
                "view_audit_trail", "manage_users"
            ],
            "lab_technician": [
                "view_reports", "manage_inventory", "manage_experiments", 
                "view_schedules", "manage_safety"
            ],
            "researcher": [
                "view_reports", "view_inventory", "manage_experiments", 
                "view_schedules", "view_safety"
            ],
            "student": [
                "view_inventory", "view_experiments", "view_schedules", "view_safety"
            ],
            "guest": [
                "view_schedules"
            ]
        }
        
        # Get permissions for user's role
        user_role = self.current_user["role"]
        if user_role in role_permissions:
            return permission in role_permissions[user_role]
        
        return False
    
    def get_notification_count(self):
        """
        Get the count of unread notifications for the current user.
        
        Returns:
            int: The number of unread notifications
        """
        if not self.current_user:
            return 0
        
        # Query unread notifications
        result = self.db.execute_query(
            "SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND read = 0",
            (self.current_user["id"],)
        )
        
        if result and len(result) > 0:
            return result[0]["count"]
        
        return 0
    
    def update_notification_badge(self):
        """Update the notification badge with the current count."""
        if not self.current_user:
            return
        
        # Get notification count
        count = self.get_notification_count()
        
        # Update badge
        if count > 0:
            self.app_bar.actions[1].badge.text = str(count)
            self.app_bar.actions[1].badge.visible = True
        else:
            self.app_bar.actions[1].badge.visible = False
        
        # Update the page
        self.page.update()
    
    def add_notification(self, user_id, title, message, notification_type="system"):
        """
        Add a notification for a user.
        
        Args:
            user_id (int): The user ID to notify
            title (str): The notification title
            message (str): The notification message
            notification_type (str): The type of notification
        """
        # Insert notification into database
        self.db.execute_query(
            """
            INSERT INTO notifications (
                user_id, title, message, type, created_at, read
            ) VALUES (?, ?, ?, ?, ?, ?)
            """,
            (user_id, title, message, notification_type, datetime.now().isoformat(), 0)
        )
        
        # Update notification badge if for current user
        if self.current_user and user_id == self.current_user["id"]:
            self.update_notification_badge()
    
    def broadcast_notification(self, title, message, notification_type="system", role=None):
        """
        Send a notification to multiple users.
        
        Args:
            title (str): The notification title
            message (str): The notification message
            notification_type (str): The type of notification
            role (str, optional): If specified, only send to users with this role
        """
        # Build query
        if role:
            query = "SELECT id FROM users WHERE role = ?"
            params = (role,)
        else:
            query = "SELECT id FROM users"
            params = ()
        
        # Get users
        users = self.db.execute_query(query, params)
        
        # Send notification to each user
        for user in users:
            self.add_notification(user["id"], title, message, notification_type)


def main(page: ft.Page):
    """
    Main application entry point.
    
    Args:
        page (ft.Page): The Flet page object
    """
    # Configure the page with basic settings
    page.title = "Science Laboratory Management System"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window_width = 1200
    page.window_height = 800
    page.window_min_width = 800
    page.window_min_height = 600
    page.padding = 0
    page.spacing = 0
    page.window_center()
    
    # Create a simple login view
    def login_clicked(e):
        page.clean()
        page.add(ft.Text("Login successful! Main application would load here."))
        page.update()
    
    # Create a simple login form
    login_card = ft.Card(
        content=ft.Container(
            content=ft.Column([
                ft.Text("Science Laboratory Management System", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("Login", size=18),
                ft.TextField(label="Username", autofocus=True),
                ft.TextField(label="Password", password=True),
                ft.ElevatedButton("Login", on_click=login_clicked),
            ], spacing=20),
            padding=20,
        ),
        width=400,
    )
    
    # Center the login card
    page.add(
        ft.Container(
            content=login_card,
            alignment=ft.alignment.center,
            expand=True,
        )
    )
    
    page.update()


# Run the application if executed directly
if __name__ == "__main__":
    ft.app(target=main)