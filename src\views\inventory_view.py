import flet as ft
from datetime import datetime
from src.models.inventory_model import InventoryModel

class InventoryView:
    """
    Inventory management view for the Science Laboratory Management System.
    Allows users to view, add, edit, and manage inventory items.
    """
    
    def __init__(self, controller):
        """
        Initialize the inventory view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        self.inventory_model = InventoryModel(self.controller.db)
        self.items = []
        self.filtered_items = []
        self.categories = []
        self.locations = []
        self.transactions = []
        
        # Create filter controls
        self.search_field = ft.TextField(
            label="Search",
            prefix_icon=ft.icons.SEARCH,
            on_change=self.filter_items,
            expand=True,
        )
        
        self.category_dropdown = ft.Dropdown(
            label="Category",
            on_change=self.filter_items,
            options=[],
            width=200,
        )
        
        self.location_dropdown = ft.Dropdown(
            label="Location",
            on_change=self.filter_items,
            options=[],
            width=200,
        )
        
        self.status_dropdown = ft.Dropdown(
            label="Status",
            on_change=self.filter_items,
            options=[
                ft.dropdown.Option("All"),
                ft.dropdown.Option("Low Stock"),
                ft.dropdown.Option("Expiring Soon"),
                ft.dropdown.Option("Expired"),
            ],
            value="All",
            width=200,
        )
        
        # Create data table
        self.data_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("Name")),
                ft.DataColumn(ft.Text("Category")),
                ft.DataColumn(ft.Text("Quantity")),
                ft.DataColumn(ft.Text("Unit")),
                ft.DataColumn(ft.Text("Location")),
                ft.DataColumn(ft.Text("Expiry Date")),
                ft.DataColumn(ft.Text("Status")),
                ft.DataColumn(ft.Text("Actions")),
            ],
            rows=[],
        )
        
        # Create form fields for adding/editing items
        self.item_id_field = ft.TextField(visible=False)
        self.name_field = ft.TextField(label="Name", required=True)
        self.category_field = ft.Dropdown(label="Category", required=True)
        self.quantity_field = ft.TextField(label="Quantity", required=True, keyboard_type=ft.KeyboardType.NUMBER)
        self.unit_field = ft.TextField(label="Unit", required=True)
        self.location_field = ft.Dropdown(label="Location")
        self.min_quantity_field = ft.TextField(label="Minimum Quantity", keyboard_type=ft.KeyboardType.NUMBER)
        self.expiry_date_field = ft.TextField(label="Expiry Date (YYYY-MM-DD)")
        self.barcode_field = ft.TextField(label="Barcode/QR Code")
        self.notes_field = ft.TextField(label="Notes", multiline=True, min_lines=3, max_lines=5)
        
        # Create dialog for adding/editing items
        self.item_dialog = ft.AlertDialog(
            title=ft.Text("Add Inventory Item"),
            content=ft.Column(
                [
                    self.item_id_field,
                    self.name_field,
                    self.category_field,
                    ft.Row([
                        self.quantity_field,
                        self.unit_field,
                    ]),
                    self.location_field,
                    ft.Row([
                        self.min_quantity_field,
                        self.expiry_date_field,
                    ]),
                    self.barcode_field,
                    self.notes_field,
                ],
                tight=True,
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                width=500,
                height=400,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=self.close_dialog),
                ft.TextButton("Save", on_click=self.save_item),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
    
    def build(self):
        """
        Build and return the inventory view.
        
        Returns:
            ft.Container: The inventory view container
        """
        # Load inventory data
        self.load_inventory_data()
        
        # Create add button
        add_button = ft.ElevatedButton(
            "Add Item",
            icon=ft.icons.ADD,
            on_click=self.show_add_dialog,
        )
        
        # Create export button
        export_button = ft.ElevatedButton(
            "Export",
            icon=ft.icons.DOWNLOAD,
            on_click=self.export_inventory,
        )
        
        # Create header row
        header_row = ft.Row(
            [
                ft.Text("Inventory Management", size=24, weight=ft.FontWeight.BOLD),
                ft.Row([add_button, export_button]),
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        )
        
        # Create filters row
        filters_row = ft.Row(
            [
                self.search_field,
                self.category_dropdown,
                self.location_dropdown,
                self.status_dropdown,
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            wrap=True,
        )
        
        # Create data table container
        table_container = ft.Container(
            content=self.data_table,
            border=ft.border.all(1, ft.colors.GREY_300),
            border_radius=5,
            padding=10,
            expand=True,
        )
        
        # Create transaction history table
        self.transaction_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("Date/Time")),
                ft.DataColumn(ft.Text("Item")),
                ft.DataColumn(ft.Text("Type")),
                ft.DataColumn(ft.Text("Quantity")),
                ft.DataColumn(ft.Text("Unit")),
                ft.DataColumn(ft.Text("User")),
                ft.DataColumn(ft.Text("Reason")),
            ],
            rows=self.create_transaction_rows(),
        )
        
        # Create transaction history container
        transaction_container = ft.Container(
            content=self.transaction_table,
            border=ft.border.all(1, ft.colors.GREY_300),
            border_radius=5,
            padding=10,
            expand=True,
        )
        
        # Create tabs
        tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            tabs=[
                ft.Tab(
                    text="Inventory Items",
                    content=ft.Column(
                        [
                            filters_row,
                            ft.Container(height=10),
                            table_container,
                        ],
                        spacing=10,
                        expand=True,
                    ),
                ),
                ft.Tab(
                    text="Transaction History",
                    content=transaction_container,
                ),
            ],
            expand=True,
        )
        
        # Return the complete view
        return ft.Container(
            content=ft.Column(
                [
                    header_row,
                    ft.Divider(),
                    tabs,
                ],
                spacing=10,
                expand=True,
            ),
            padding=20,
            expand=True,
        )
    
    def load_inventory_data(self):
        """Load inventory data from the database."""
        # Get data from the inventory model
        self.items = self.inventory_model.get_all_items()
        self.filtered_items = self.items.copy()
        
        # Get recent transactions
        self.transactions = self.inventory_model.get_transactions(limit=20)
        
        # Get categories and locations from the model
        self.categories = sorted(self.inventory_model.get_categories())
        self.locations = sorted(self.inventory_model.get_locations())
        
        # Update dropdowns
        self.category_dropdown.options = [ft.dropdown.Option("All")] + [
            ft.dropdown.Option(category) for category in self.categories
        ]
        self.category_dropdown.value = "All"
        
        self.location_dropdown.options = [ft.dropdown.Option("All")] + [
            ft.dropdown.Option(location) for location in self.locations
        ]
        self.location_dropdown.value = "All"
        
        # Update form dropdowns
        self.category_field.options = [
            ft.dropdown.Option(category) for category in self.categories
        ]
        self.category_field.options.append(ft.dropdown.Option("Add New..."))
        
        self.location_field.options = [
            ft.dropdown.Option(location) for location in self.locations
        ]
        self.location_field.options.append(ft.dropdown.Option("Add New..."))
        
        # Update the data table
        self.update_data_table()
    
    def update_data_table(self):
        """Update the data table with filtered items."""
        # Clear existing rows
        self.data_table.rows.clear()
        
        # Add rows for filtered items
        for item in self.filtered_items:
            # Determine status
            status = "Normal"
            status_color = ft.colors.GREEN
            
            if item["quantity"] <= item["min_quantity"]:
                status = "Low Stock"
                status_color = ft.colors.RED
            
            if item["expiry_date"]:
                try:
                    expiry_date = datetime.strptime(item["expiry_date"], "%Y-%m-%d")
                    today = datetime.now()
                    
                    if expiry_date < today:
                        status = "Expired"
                        status_color = ft.colors.RED
                    elif (expiry_date - today).days <= 30:
                        status = "Expiring Soon"
                        status_color = ft.colors.ORANGE
                except:
                    pass
            
            # Create row
            self.data_table.rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(item["name"])),
                        ft.DataCell(ft.Text(item["category"])),
                        ft.DataCell(ft.Text(f"{item['quantity']}")),
                        ft.DataCell(ft.Text(item["unit"])),
                        ft.DataCell(ft.Text(item["location"] or "")),
                        ft.DataCell(ft.Text(item["expiry_date"] or "N/A")),
                        ft.DataCell(ft.Text(status, color=status_color)),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.icons.EDIT,
                                        tooltip="Edit",
                                        on_click=lambda e, item_id=item["id"]: self.show_edit_dialog(e, item_id),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.ADD_CIRCLE,
                                        tooltip="Adjust Quantity",
                                        on_click=lambda e, item_id=item["id"]: self.show_adjust_quantity_dialog(e, item_id),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.DELETE,
                                        tooltip="Delete",
                                        on_click=lambda e, item_id=item["id"]: self.delete_item(e, item_id),
                                    ),
                                ]
                            )
                        ),
                    ]
                )
            )
        
        # Update the page
        self.controller.page.update()
    
    def filter_items(self, e=None):
        """
        Filter inventory items based on search and dropdown values.
        
        Args:
            e: The change event (optional)
        """
        search_text = self.search_field.value.lower() if self.search_field.value else ""
        category = self.category_dropdown.value
        location = self.location_dropdown.value
        status = self.status_dropdown.value
        
        # Filter items
        self.filtered_items = []
        for item in self.items:
            # Apply search filter
            if search_text and search_text not in item["name"].lower() and search_text not in (item["barcode"] or "").lower():
                continue
            
            # Apply category filter
            if category != "All" and item["category"] != category:
                continue
            
            # Apply location filter
            if location != "All" and item["location"] != location:
                continue
            
            # Apply status filter
            if status != "All":
                if status == "Low Stock" and item["quantity"] > item["min_quantity"]:
                    continue
                
                if status in ["Expiring Soon", "Expired"] and not item["expiry_date"]:
                    continue
                
                if status == "Expiring Soon":
                    try:
                        expiry_date = datetime.strptime(item["expiry_date"], "%Y-%m-%d")
                        today = datetime.now()
                        
                        if expiry_date < today or (expiry_date - today).days > 30:
                            continue
                    except:
                        continue
                
                if status == "Expired":
                    try:
                        expiry_date = datetime.strptime(item["expiry_date"], "%Y-%m-%d")
                        today = datetime.now()
                        
                        if expiry_date >= today:
                            continue
                    except:
                        continue
            
            # Add item to filtered list
            self.filtered_items.append(item)
        
        # Update the data table
        self.update_data_table()
    
    def show_add_dialog(self, e):
        """
        Show dialog for adding a new inventory item.
        
        Args:
            e: The click event
        """
        # Clear form fields
        self.item_id_field.value = ""
        self.name_field.value = ""
        self.category_field.value = None
        self.quantity_field.value = ""
        self.unit_field.value = ""
        self.location_field.value = None
        self.min_quantity_field.value = ""
        self.expiry_date_field.value = ""
        self.barcode_field.value = ""
        self.notes_field.value = ""
        
        # Set dialog title
        self.item_dialog.title = ft.Text("Add Inventory Item")
        
        # Show the dialog
        self.controller.page.dialog = self.item_dialog
        self.item_dialog.open = True
        self.controller.page.update()
    
    def show_edit_dialog(self, e, item_id):
        """
        Show dialog for editing an inventory item.
        
        Args:
            e: The click event
            item_id: ID of the item to edit
        """
        # Find the item
        item = next((item for item in self.items if item["id"] == item_id), None)
        
        if not item:
            return
        
        # Set form fields
        self.item_id_field.value = str(item["id"])
        self.name_field.value = item["name"]
        self.category_field.value = item["category"]
        self.quantity_field.value = str(item["quantity"])
        self.unit_field.value = item["unit"]
        self.location_field.value = item["location"]
        self.min_quantity_field.value = str(item["min_quantity"]) if item["min_quantity"] is not None else ""
        self.expiry_date_field.value = item["expiry_date"] or ""
        self.barcode_field.value = item["barcode"] or ""
        self.notes_field.value = item["notes"] or ""
        
        # Set dialog title
        self.item_dialog.title = ft.Text("Edit Inventory Item")
        
        # Show the dialog
        self.controller.page.dialog = self.item_dialog
        self.item_dialog.open = True
        self.controller.page.update()
    
    def close_dialog(self, e):
        """
        Close the item dialog.
        
        Args:
            e: The click event
        """
        self.item_dialog.open = False
        self.controller.page.update()
    
    def save_item(self, e):
        """
        Save the inventory item.
        
        Args:
            e: The click event
        """
        # Validate required fields
        if not self.name_field.value or not self.category_field.value or not self.quantity_field.value or not self.unit_field.value:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Please fill in all required fields"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
            return
        
        # Handle "Add New..." options
        if self.category_field.value == "Add New...":
            # Show dialog to add new category
            self.show_add_category_dialog()
            return
        
        if self.location_field.value == "Add New...":
            # Show dialog to add new location
            self.show_add_location_dialog()
            return
        
        # Prepare item data
        item_data = {
            "name": self.name_field.value,
            "category": self.category_field.value,
            "quantity": float(self.quantity_field.value),
            "unit": self.unit_field.value,
            "location": self.location_field.value,
            "min_quantity": float(self.min_quantity_field.value) if self.min_quantity_field.value else None,
            "expiry_date": self.expiry_date_field.value if self.expiry_date_field.value else None,
            "barcode": self.barcode_field.value if self.barcode_field.value else None,
            "notes": self.notes_field.value if self.notes_field.value else None,
        }
        
        # Add or update item
        if self.item_id_field.value:
            # Update existing item
            item_id = int(self.item_id_field.value)
            
            # Use the inventory model to update the item
            success = self.inventory_model.update_item(
                item_id=item_id,
                item_data=item_data,
                user_id=self.controller.current_user["id"]
            )
            
            if success:
                # Show success message
                self.controller.page.snack_bar = ft.SnackBar(
                    content=ft.Text("Item updated successfully"),
                    bgcolor=ft.colors.GREEN_400,
                )
            else:
                # Show error message
                self.controller.page.snack_bar = ft.SnackBar(
                    content=ft.Text("Error updating item"),
                    bgcolor=ft.colors.RED_400,
                )
                self.controller.page.snack_bar.open = True
                self.controller.page.update()
                return
        else:
            # Add new item using the inventory model
            item_id = self.inventory_model.add_item(
                item_data=item_data,
                user_id=self.controller.current_user["id"]
            )
            
            if item_id:
                # Show success message
                self.controller.page.snack_bar = ft.SnackBar(
                    content=ft.Text("Item added successfully"),
                    bgcolor=ft.colors.GREEN_400,
                )
            else:
                # Show error message
                self.controller.page.snack_bar = ft.SnackBar(
                    content=ft.Text("Error adding item"),
                    bgcolor=ft.colors.RED_400,
                )
                self.controller.page.snack_bar.open = True
                self.controller.page.update()
                return
        
        # Close the dialog
        self.item_dialog.open = False
        
        # Reload inventory data
        self.load_inventory_data()
        
        # Update the page
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def delete_item(self, e, item_id):
        """
        Delete an inventory item.
        
        Args:
            e: The click event
            item_id: ID of the item to delete
        """
        # Find the item
        item = next((item for item in self.items if item["id"] == item_id), None)
        
        if not item:
            return
        
        # Show confirmation dialog
        def confirm_delete(e):
            # Delete the item using the inventory model
            success = self.inventory_model.delete_item(
                item_id=item_id,
                user_id=self.controller.current_user["id"]
            )
            
            # Close the dialog
            confirm_dialog.open = False
            
            if success:
                # Show success message
                self.controller.page.snack_bar = ft.SnackBar(
                    content=ft.Text("Item deleted successfully"),
                    bgcolor=ft.colors.GREEN_400,
                )
            else:
                # Show error message
                self.controller.page.snack_bar = ft.SnackBar(
                    content=ft.Text("Error deleting item"),
                    bgcolor=ft.colors.RED_400,
                )
            
            self.controller.page.snack_bar.open = True
            
            # Reload inventory data
            self.load_inventory_data()
            
            # Update the page
            self.controller.page.update()
        
        # Create confirmation dialog
        confirm_dialog = ft.AlertDialog(
            title=ft.Text("Confirm Delete"),
            content=ft.Text(f"Are you sure you want to delete '{item['name']}'?"),
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(confirm_dialog, "open", False)),
                ft.TextButton("Delete", on_click=confirm_delete),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = confirm_dialog
        confirm_dialog.open = True
        self.controller.page.update()
    
    def show_add_category_dialog(self):
        """Show dialog for adding a new category."""
        # Create text field for new category
        new_category_field = ft.TextField(
            label="New Category",
            autofocus=True,
        )
        
        # Create dialog
        def add_category(e):
            if not new_category_field.value:
                return
            
            # Add the new category to the list
            new_category = new_category_field.value
            self.categories.append(new_category)
            self.categories.sort()
            
            # Update dropdowns
            self.category_dropdown.options = [ft.dropdown.Option("All")] + [
                ft.dropdown.Option(category) for category in self.categories
            ]
            
            self.category_field.options = [
                ft.dropdown.Option(category) for category in self.categories
            ]
            self.category_field.options.append(ft.dropdown.Option("Add New..."))
            
            # Set the new category as selected
            self.category_field.value = new_category
            
            # Close the dialog
            category_dialog.open = False
            
            # Update the page
            self.controller.page.update()
        
        # Create dialog
        category_dialog = ft.AlertDialog(
            title=ft.Text("Add New Category"),
            content=new_category_field,
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(category_dialog, "open", False)),
                ft.TextButton("Add", on_click=add_category),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = category_dialog
        category_dialog.open = True
        self.controller.page.update()
    
    def show_add_location_dialog(self):
        """Show dialog for adding a new location."""
        # Create text field for new location
        new_location_field = ft.TextField(
            label="New Location",
            autofocus=True,
        )
        
        # Create dialog
        def add_location(e):
            if not new_location_field.value:
                return
            
            # Add the new location to the list
            new_location = new_location_field.value
            self.locations.append(new_location)
            self.locations.sort()
            
            # Update dropdowns
            self.location_dropdown.options = [ft.dropdown.Option("All")] + [
                ft.dropdown.Option(location) for location in self.locations
            ]
            
            self.location_field.options = [
                ft.dropdown.Option(location) for location in self.locations
            ]
            self.location_field.options.append(ft.dropdown.Option("Add New..."))
            
            # Set the new location as selected
            self.location_field.value = new_location
            
            # Close the dialog
            location_dialog.open = False
            
            # Update the page
            self.controller.page.update()
        
        # Create dialog
        location_dialog = ft.AlertDialog(
            title=ft.Text("Add New Location"),
            content=new_location_field,
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(location_dialog, "open", False)),
                ft.TextButton("Add", on_click=add_location),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = location_dialog
        location_dialog.open = True
        self.controller.page.update()
    
    def show_adjust_quantity_dialog(self, e, item_id):
        """
        Show dialog for adjusting item quantity.
        
        Args:
            e: The click event
            item_id: ID of the item to adjust
        """
        # Find the item
        item = next((item for item in self.items if item["id"] == item_id), None)
        
        if not item:
            return
        
        # Create quantity field
        quantity_field = ft.TextField(
            label="Quantity to Add/Remove",
            hint_text="Use positive for addition, negative for removal",
            keyboard_type=ft.KeyboardType.NUMBER,
            autofocus=True,
        )
        
        # Create reason field
        reason_field = ft.TextField(
            label="Reason",
            hint_text="Why are you adjusting the quantity?",
            required=True,
        )
        
        # Create notes field
        notes_field = ft.TextField(
            label="Notes",
            multiline=True,
            min_lines=2,
            max_lines=4,
        )
        
        # Create dialog
        def adjust_quantity(e):
            try:
                # Validate quantity
                if not quantity_field.value:
                    self.controller.page.snack_bar = ft.SnackBar(
                        content=ft.Text("Please enter a quantity"),
                        bgcolor=ft.colors.RED_400,
                    )
                    self.controller.page.snack_bar.open = True
                    self.controller.page.update()
                    return
                
                # Validate reason
                if not reason_field.value:
                    self.controller.page.snack_bar = ft.SnackBar(
                        content=ft.Text("Please enter a reason"),
                        bgcolor=ft.colors.RED_400,
                    )
                    self.controller.page.snack_bar.open = True
                    self.controller.page.update()
                    return
                
                # Parse quantity
                quantity_change = float(quantity_field.value)
                
                # Update quantity using the inventory model
                success = self.inventory_model.update_item_quantity(
                    item_id=item_id,
                    quantity_change=quantity_change,
                    reason=reason_field.value,
                    user_id=self.controller.current_user["id"],
                    notes=notes_field.value if notes_field.value else None
                )
                
                # Close the dialog
                adjust_dialog.open = False
                
                if success:
                    # Show success message
                    action = "added to" if quantity_change > 0 else "removed from"
                    self.controller.page.snack_bar = ft.SnackBar(
                        content=ft.Text(f"{abs(quantity_change)} {item['unit']} {action} {item['name']}"),
                        bgcolor=ft.colors.GREEN_400,
                    )
                else:
                    # Show error message
                    self.controller.page.snack_bar = ft.SnackBar(
                        content=ft.Text("Error adjusting quantity"),
                        bgcolor=ft.colors.RED_400,
                    )
                
                self.controller.page.snack_bar.open = True
                
                # Reload inventory data
                self.load_inventory_data()
                
                # Update the page
                self.controller.page.update()
            
            except ValueError:
                # Show error for invalid number
                self.controller.page.snack_bar = ft.SnackBar(
                    content=ft.Text("Please enter a valid number"),
                    bgcolor=ft.colors.RED_400,
                )
                self.controller.page.snack_bar.open = True
                self.controller.page.update()
        
        # Create dialog
        adjust_dialog = ft.AlertDialog(
            title=ft.Text(f"Adjust Quantity: {item['name']}"),
            content=ft.Column(
                [
                    ft.Text(f"Current quantity: {item['quantity']} {item['unit']}"),
                    quantity_field,
                    reason_field,
                    notes_field,
                ],
                tight=True,
                spacing=10,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(adjust_dialog, "open", False)),
                ft.TextButton("Save", on_click=adjust_quantity),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = adjust_dialog
        adjust_dialog.open = True
        self.controller.page.update()
    
    def export_inventory(self, e):
        """
        Export inventory data to CSV.
        
        Args:
            e: The click event
        """
        # Create CSV content
        csv_content = "ID,Name,Category,Quantity,Unit,Location,Min Quantity,Expiry Date,Barcode,Notes\n"
        
        for item in self.filtered_items:
            csv_content += f"{item['id']},{item['name']},{item['category']},{item['quantity']},{item['unit']},"
            csv_content += f"{item['location'] or ''},{'0' if item['min_quantity'] is None else item['min_quantity']},"
            csv_content += f"{item['expiry_date'] or ''},{item['barcode'] or ''},{(item['notes'] or '').replace(',', ';')}\n"
        
        # Create a download link
        download_link = ft.Text(
            "Click here to download",
            color=ft.colors.BLUE,
            style=ft.TextStyle(decoration=ft.TextDecoration.UNDERLINE),
        )
        
        # Create dialog
        export_dialog = ft.AlertDialog(
            title=ft.Text("Export Inventory"),
            content=ft.Column(
                [
                    ft.Text("Your inventory data is ready to export."),
                    download_link,
                ],
                tight=True,
                spacing=10,
            ),
            actions=[
                ft.TextButton("Close", on_click=lambda e: setattr(export_dialog, "open", False)),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = export_dialog
        export_dialog.open = True
        self.controller.page.update()
        
        # In a real application, we would save the CSV to a file and provide a download link
        # For this example, we'll just show a dialog with instructions