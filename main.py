import sys
import os
# Add the project root directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import flet as ft
from src.controllers.app_controller import App<PERSON><PERSON>roller

def main(page: ft.Page):
    """
    Main entry point for the Science Laboratory Management System application.
    
    Args:
        page (ft.Page): The main Flet page object
    """
    # Initialize the application controller
    app = AppController(page)
    
    # Configure the page
    page.title = "Science Laboratory Management System"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window_width = 1200
    page.window_height = 800
    page.window_min_width = 800
    page.window_min_height = 600
    page.padding = 0
    page.spacing = 0
    page.window_center()
    page.update()
    
    # Start the application
    app.initialize()

if __name__ == "__main__":
    ft.app(target=main)