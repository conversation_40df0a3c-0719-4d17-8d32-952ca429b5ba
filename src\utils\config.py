
import os
import json
import logging
from datetime import datetime

class Config:
    """
    Configuration manager for the Science Laboratory Management System.
    Handles loading, saving, and accessing application configuration.
    """
    
    def __init__(self, config_file=None):
        """
        Initialize the configuration manager.
        
        Args:
            config_file (str, optional): Path to the configuration file
        """
        # Set default config file if not specified
        if not config_file:
            config_file = os.path.join(os.getcwd(), "config", "app_config.json")
        
        self.config_file = config_file
        self.logger = logging.getLogger("config")
        
        # Initialize empty configuration
        self.config = {}
        
        # Default configuration
        self.default_config = {
            "app": {
                "name": "Science Laboratory Management System",
                "version": "1.0.0",
                "theme": "light",
                "language": "en",
                "debug": False
            },
            "database": {
                "path": os.path.join(os.getcwd(), "data", "lab_db.db"),
                "backup_interval": "daily",
                "backup_retention": 30
            },
            "logging": {
                "level": "INFO",
                "file_logging": True,
                "log_dir": os.path.join(os.getcwd(), "logs"),
                "max_log_files": 30
            },
            "security": {
                "session_timeout": 30,  # minutes
                "password_expiry": 90,  # days
                "max_login_attempts": 5,
                "lockout_duration": 15  # minutes
            },
            "email": {
                "enabled": False,
                "smtp_server": "",
                "smtp_port": 587,
                "smtp_username": "",
                "smtp_password": "",
                "from_address": "",
                "use_tls": True
            },
            "notifications": {
                "enabled": True,
                "check_interval": 5  # minutes
            },
            "backup": {
                "enabled": True,
                "auto_backup": True,
                "backup_dir": os.path.join(os.getcwd(), "backups"),
                "backup_on_startup": True,
                "backup_on_shutdown": True
            },
            "updates": {
                "check_for_updates": True,
                "update_channel": "stable",
                "auto_update": False
            }
        }
    
    def load(self):
        """
        Load configuration from file.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if config file exists
            if os.path.exists(self.config_file):
                # Open and read config file
                with open(self.config_file, 'r') as f:
                    self.config = json.load(f)
                
                self.logger.info(f"Configuration loaded from {self.config_file}")
                
                # Merge with default config to ensure all keys exist
                self.merge_with_defaults()
                
                return True
            else:
                # Create default configuration
                self.logger.warning(f"Configuration file not found: {self.config_file}")
                self.config = self.default_config.copy()
                
                # Save default configuration
                self.save()
                
                return True
        
        except Exception as e:
            self.logger.error(f"Error loading configuration: {str(e)}")
            
            # Use default configuration
            self.config = self.default_config.copy()
            
            return False
    
    def save(self):
        """
        Save configuration to file.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create config directory if it doesn't exist
            config_dir = os.path.dirname(self.config_file)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
            
            # Save config to file
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            
            self.logger.info(f"Configuration saved to {self.config_file}")
            
            return True
        
        except Exception as e:
            self.logger.error(f"Error saving configuration: {str(e)}")
            return False
    
    def get(self, section, key=None, default=None):
        """
        Get a configuration value.
        
        Args:
            section (str): The configuration section
            key (str, optional): The configuration key (if None, returns entire section)
            default: Default value if key not found
            
        Returns:
            The configuration value or default if not found
        """
        try:
            if section in self.config:
                if key is None:
                    return self.config[section]
                elif key in self.config[section]:
                    return self.config[section][key]
            
            # If not found, check default config
            if section in self.default_config:
                if key is None:
                    return self.default_config[section]
                elif key in self.default_config[section]:
                    return self.default_config[section][key]
            
            return default
        
        except Exception as e:
            self.logger.error(f"Error getting configuration value: {str(e)}")
            return default
    
    def set(self, section, key, value):
        """
        Set a configuration value.
        
        Args:
            section (str): The configuration section
            key (str): The configuration key
            value: The configuration value
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create section if it doesn't exist
            if section not in self.config:
                self.config[section] = {}
            
            # Set value
            self.config[section][key] = value
            
            # Save configuration
            self.save()
            
            return True
        
        except Exception as e:
            self.logger.error(f"Error setting configuration value: {str(e)}")
            return False
    
    def merge_with_defaults(self):
        """
        Merge loaded configuration with default configuration to ensure all keys exist.
        """
        for section, section_data in self.default_config.items():
            if section not in self.config:
                self.config[section] = section_data
            else:
                for key, value in section_data.items():
                    if key not in self.config[section]:
                        self.config[section][key] = value
    
    def reset_to_defaults(self):
        """
        Reset configuration to default values.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create backup of current configuration
            backup_file = f"{self.config_file}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as src, open(backup_file, 'w') as dst:
                    dst.write(src.read())
            
            # Reset to defaults
            self.config = self.default_config.copy()
            
            # Save configuration
            self.save()
            
            self.logger.info(f"Configuration reset to defaults (backup: {backup_file})")
            
            return True
        
        except Exception as e:
            self.logger.error(f"Error resetting configuration: {str(e)}")
            return False
    
    def export_config(self, export_file=None):
        """
        Export configuration to a file.
        
        Args:
            export_file (str, optional): Path to export file
            
        Returns:
            str: Path to export file if successful, None otherwise
        """
        try:
            # Generate export filename if not specified
            if not export_file:
                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                export_file = os.path.join(os.path.dirname(self.config_file), f"config_export_{timestamp}.json")
            
            # Create directory if it doesn't exist
            export_dir = os.path.dirname(export_file)
            if not os.path.exists(export_dir):
                os.makedirs(export_dir, exist_ok=True)
            
            # Export configuration
            with open(export_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            
            self.logger.info(f"Configuration exported to {export_file}")
            
            return export_file
        
        except Exception as e:
            self.logger.error(f"Error exporting configuration: {str(e)}")
            return None
    
    def import_config(self, import_file):
        """
        Import configuration from a file.
        
        Args:
            import_file (str): Path to import file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if import file exists
            if not os.path.exists(import_file):
                self.logger.error(f"Import file not found: {import_file}")
                return False
            
            # Create backup of current configuration
            backup_file = f"{self.config_file}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as src, open(backup_file, 'w') as dst:
                    dst.write(src.read())
            
            # Import configuration
            with open(import_file, 'r') as f:
                imported_config = json.load(f)
            
            # Validate imported configuration
            if not self.validate_config(imported_config):
                self.logger.error(f"Invalid configuration in import file: {import_file}")
                return False
            
            # Update configuration
            self.config = imported_config
            
            # Merge with defaults to ensure all keys exist
            self.merge_with_defaults()
            
            # Save configuration
            self.save()
            
            self.logger.info(f"Configuration imported from {import_file} (backup: {backup_file})")
            
            return True
        
        except Exception as e:
            self.logger.error(f"Error importing configuration: {str(e)}")
            return False
    
    def validate_config(self, config):
        """
        Validate a configuration.
        
        Args:
            config (dict): The configuration to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        try:
            # Check if config is a dictionary
            if not isinstance(config, dict):
                return False
            
            # Check required sections
            required_sections = ["app", "database", "logging"]
            
            for section in required_sections:
                if section not in config:
                    return False
            
            # Check app section
            if "name" not in config["app"] or "version" not in config["app"]:
                return False
            
            # Check database section
            if "path" not in config["database"]:
                return False
            
            return True
        
        except Exception:
            return False