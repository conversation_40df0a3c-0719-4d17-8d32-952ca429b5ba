import os
import sys

print("Test script running")
print(f"Python version: {sys.version}")
print(f"Python executable: {sys.executable}")
print(f"Current directory: {os.getcwd()}")

# Check if required directories exist
dirs_to_check = ["src", "config", "data"]
for dir_path in dirs_to_check:
    full_path = os.path.join(os.getcwd(), dir_path)
    exists = os.path.exists(full_path)
    print(f"Directory {dir_path}: {'EXISTS' if exists else 'MISSING'}")