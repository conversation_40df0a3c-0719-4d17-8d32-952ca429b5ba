#!/usr/bin/env python
"""
Setup Script for Enhanced Science Laboratory Management System
Initializes all recommended improvements and features
"""
import os
import sys
import json
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.pwa.pwa_manager import PWAManager
from src.notifications.notification_manager import NotificationManager
from src.analytics.analytics_engine import AnalyticsEngine
from src.database.db_manager import DatabaseManager


def create_directories():
    """Create necessary directories for enhanced features."""
    directories = [
        "static",
        "static/icons",
        "static/screenshots",
        "static/css",
        "static/js",
        "config",
        "logs/analytics",
        "logs/notifications",
        "data/backups",
        "data/exports",
        "data/reports"
    ]
    
    print("📁 Creating directories...")
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"   ✅ {directory}")


def setup_configuration_files():
    """Setup configuration files for enhanced features."""
    print("\n⚙️ Setting up configuration files...")
    
    # Theme configuration
    theme_config = {
        "current_theme": "light",
        "custom_colors": {},
        "auto_switch": False,
        "switch_times": {
            "dark_mode_start": "18:00",
            "light_mode_start": "06:00"
        }
    }
    
    with open("config/theme_config.json", "w") as f:
        json.dump(theme_config, f, indent=2)
    print("   ✅ Theme configuration")
    
    # Notification configuration
    notification_config = {
        "email": {
            "enabled": False,
            "smtp_server": "smtp.gmail.com",
            "smtp_port": 587,
            "username": "",
            "password": "",
            "from_address": "",
            "use_tls": True
        },
        "push": {
            "enabled": False,
            "vapid_public_key": "",
            "vapid_private_key": "",
            "vapid_email": ""
        },
        "in_app": {
            "enabled": True,
            "max_notifications": 100,
            "auto_dismiss_seconds": 5
        },
        "rules": {
            "low_stock_threshold": 10,
            "expiry_warning_days": 30,
            "experiment_overdue_days": 7,
            "check_interval_minutes": 5
        }
    }
    
    with open("config/notification_config.json", "w") as f:
        json.dump(notification_config, f, indent=2)
    print("   ✅ Notification configuration")
    
    # Analytics configuration
    analytics_config = {
        "cache_ttl_seconds": 300,
        "report_retention_days": 90,
        "auto_reports": {
            "enabled": True,
            "schedule": "weekly",
            "recipients": []
        },
        "predictive_analytics": {
            "enabled": True,
            "forecast_days": 30,
            "min_data_points": 5
        }
    }
    
    with open("config/analytics_config.json", "w") as f:
        json.dump(analytics_config, f, indent=2)
    print("   ✅ Analytics configuration")
    
    # PWA configuration
    pwa_config = {
        "app_name": "Lab Management System",
        "short_name": "Lab Manager",
        "description": "Complete laboratory management solution",
        "theme_color": "#1976d2",
        "background_color": "#1976d2",
        "start_url": "/",
        "display": "standalone",
        "offline_enabled": True
    }
    
    with open("config/pwa_config.json", "w") as f:
        json.dump(pwa_config, f, indent=2)
    print("   ✅ PWA configuration")


def create_sample_icons():
    """Create sample icon files for PWA."""
    print("\n🎨 Creating sample icons...")
    
    # Create simple SVG icons (in production, use proper PNG icons)
    icon_sizes = [72, 96, 128, 144, 152, 192, 384, 512]
    
    for size in icon_sizes:
        svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="{size}" height="{size}" viewBox="0 0 {size} {size}" xmlns="http://www.w3.org/2000/svg">
    <rect width="{size}" height="{size}" fill="#1976d2" rx="16"/>
    <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="{size//3}" 
          fill="white" text-anchor="middle" dominant-baseline="central">🧪</text>
</svg>'''
        
        with open(f"static/icons/icon-{size}x{size}.svg", "w") as f:
            f.write(svg_content)
    
    print(f"   ✅ Created {len(icon_sizes)} icon sizes")


def setup_database_enhancements():
    """Setup database enhancements."""
    print("\n🗄️ Setting up database enhancements...")
    
    try:
        # Initialize database manager
        db_manager = DatabaseManager()
        
        # Initialize notification manager (creates tables)
        notification_manager = NotificationManager(db_manager)
        print("   ✅ Notification tables created")
        
        # Initialize analytics engine
        analytics_engine = AnalyticsEngine(db_manager)
        print("   ✅ Analytics engine initialized")
        
        # Create sample notifications
        notification_manager.create_notification(
            title="Welcome to Enhanced Lab Management!",
            message="Your system has been upgraded with new features including dark mode, PWA support, and advanced analytics.",
            notification_type=notification_manager.NotificationType.INFO,
            priority=notification_manager.NotificationPriority.MEDIUM
        )
        print("   ✅ Sample notifications created")
        
    except Exception as e:
        print(f"   ❌ Error setting up database: {e}")


def setup_pwa_files():
    """Setup PWA files."""
    print("\n📱 Setting up PWA files...")
    
    try:
        pwa_manager = PWAManager()
        pwa_manager.setup_pwa()
        print("   ✅ PWA files created")
        
        # Create additional PWA files
        create_app_css()
        create_app_js()
        
    except Exception as e:
        print(f"   ❌ Error setting up PWA: {e}")


def create_app_css():
    """Create basic app CSS file."""
    css_content = '''
/* Enhanced Lab Management System Styles */
:root {
    --primary-color: #1976d2;
    --secondary-color: #42a5f5;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --background-color: #ffffff;
    --surface-color: #f5f5f5;
    --text-primary: #212121;
    --text-secondary: #757575;
}

[data-theme="dark"] {
    --primary-color: #64b5f6;
    --secondary-color: #90caf9;
    --background-color: #121212;
    --surface-color: #1e1e1e;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.notification-banner {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 400px;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-info { background-color: var(--primary-color); color: white; }
.notification-success { background-color: var(--success-color); color: white; }
.notification-warning { background-color: var(--warning-color); color: white; }
.notification-error { background-color: var(--error-color); color: white; }

.card {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease, transform 0.2s ease;
}

.card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.stat-card {
    text-align: center;
    min-width: 200px;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 10px 0;
}

.stat-title {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 5px;
}

.stat-subtitle {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

@media (max-width: 768px) {
    .notification-banner {
        left: 20px;
        right: 20px;
        max-width: none;
    }
    
    .card {
        margin: 10px;
        padding: 15px;
    }
    
    .stat-card {
        min-width: 150px;
    }
    
    .stat-value {
        font-size: 2rem;
    }
}
'''
    
    with open("static/css/app.css", "w") as f:
        f.write(css_content)
    print("   ✅ App CSS created")


def create_app_js():
    """Create basic app JavaScript file."""
    js_content = '''
// Enhanced Lab Management System JavaScript
class LabManagementApp {
    constructor() {
        this.init();
    }
    
    init() {
        this.setupServiceWorker();
        this.setupNotifications();
        this.setupTheme();
        this.setupOfflineDetection();
    }
    
    async setupServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                console.log('Service Worker registered:', registration);
                
                // Check for updates
                registration.addEventListener('updatefound', () => {
                    console.log('Service Worker update found');
                });
            } catch (error) {
                console.error('Service Worker registration failed:', error);
            }
        }
    }
    
    setupNotifications() {
        // Request notification permission
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission().then(permission => {
                console.log('Notification permission:', permission);
            });
        }
    }
    
    setupTheme() {
        // Check for saved theme preference
        const savedTheme = localStorage.getItem('lab-theme') || 'light';
        this.setTheme(savedTheme);
        
        // Listen for system theme changes
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        mediaQuery.addEventListener('change', (e) => {
            if (!localStorage.getItem('lab-theme')) {
                this.setTheme(e.matches ? 'dark' : 'light');
            }
        });
    }
    
    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('lab-theme', theme);
    }
    
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
        return newTheme;
    }
    
    setupOfflineDetection() {
        window.addEventListener('online', () => {
            this.showNotification('Connection restored', 'success');
        });
        
        window.addEventListener('offline', () => {
            this.showNotification('You are offline', 'warning');
        });
    }
    
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification-banner notification-${type}`;
        notification.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="background: none; border: none; color: inherit; cursor: pointer; font-size: 1.2rem;">×</button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
    
    // Install prompt for PWA
    setupInstallPrompt() {
        let deferredPrompt;
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            
            // Show install button
            const installButton = document.getElementById('install-button');
            if (installButton) {
                installButton.style.display = 'block';
                installButton.addEventListener('click', async () => {
                    if (deferredPrompt) {
                        deferredPrompt.prompt();
                        const { outcome } = await deferredPrompt.userChoice;
                        console.log('Install prompt outcome:', outcome);
                        deferredPrompt = null;
                        installButton.style.display = 'none';
                    }
                });
            }
        });
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.labApp = new LabManagementApp();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LabManagementApp;
}
'''
    
    with open("static/js/app.js", "w") as f:
        f.write(js_content)
    print("   ✅ App JavaScript created")


def create_documentation():
    """Create documentation for enhanced features."""
    print("\n📚 Creating documentation...")
    
    readme_content = '''# Enhanced Science Laboratory Management System

## 🚀 New Features

### 🌙 Dark Mode Support
- Toggle between light and dark themes
- System theme detection
- Persistent theme preferences
- Smooth transitions

### 📱 Progressive Web App (PWA)
- Install as native app
- Offline functionality
- Background sync
- Push notifications
- App shortcuts

### 📊 Advanced Analytics
- Predictive insights
- Usage trends
- Performance metrics
- Custom reports
- Real-time dashboards

### 🔔 Enhanced Notifications
- Multi-channel delivery (in-app, email, push)
- Smart alerts and warnings
- Notification templates
- User preferences
- Background monitoring

## 🛠️ Setup Instructions

1. Run the setup script:
   ```bash
   python setup_enhancements.py
   ```

2. Start the enhanced application:
   ```bash
   python enhanced_lab_management_app.py
   ```

3. Access the application:
   - Main app: http://localhost:8555
   - API docs: http://localhost:8000/api/docs

## 📱 PWA Installation

1. Open the app in a supported browser
2. Look for the "Install" prompt or button
3. Follow the installation instructions
4. The app will be available as a native application

## 🌙 Theme Switching

- Use the theme toggle button in the app bar
- Or go to Settings > Theme Settings
- Supports light, dark, and system themes

## 🔔 Notification Setup

1. Configure email settings in `config/notification_config.json`
2. Enable desired notification channels
3. Set up notification rules and thresholds
4. Test notifications in the app

## 📊 Analytics Features

- Real-time dashboard with key metrics
- Predictive analytics for inventory management
- Usage patterns and trends
- Custom report generation
- Export capabilities

## 🔧 Configuration

All configuration files are located in the `config/` directory:
- `theme_config.json` - Theme settings
- `notification_config.json` - Notification preferences
- `analytics_config.json` - Analytics configuration
- `pwa_config.json` - PWA settings

## 🆘 Support

For issues or questions:
1. Check the logs in the `logs/` directory
2. Review configuration files
3. Restart the application
4. Contact system administrator

## 🔄 Updates

The system includes automatic update checking and background sync capabilities.
New features and improvements are delivered seamlessly.
'''
    
    with open("ENHANCED_FEATURES.md", "w") as f:
        f.write(readme_content)
    print("   ✅ Enhanced features documentation")


def run_enhanced_app():
    """Run the enhanced application."""
    print("\n🚀 Starting Enhanced Lab Management System...")
    print("   📱 PWA features enabled")
    print("   🌙 Dark mode support active")
    print("   📊 Analytics engine ready")
    print("   🔔 Notification system online")
    print("\n   🌐 Access the app at: http://localhost:8555")
    print("   📚 API docs at: http://localhost:8000/api/docs")
    print("\n   Press Ctrl+C to stop the application")
    
    try:
        import subprocess
        subprocess.run([sys.executable, "enhanced_lab_management_app.py"])
    except KeyboardInterrupt:
        print("\n👋 Application stopped")
    except Exception as e:
        print(f"\n❌ Error running application: {e}")


def main():
    """Main setup function."""
    print("🔧 Enhanced Science Laboratory Management System Setup")
    print("=" * 60)
    
    try:
        create_directories()
        setup_configuration_files()
        create_sample_icons()
        setup_database_enhancements()
        setup_pwa_files()
        create_documentation()
        
        print("\n" + "=" * 60)
        print("✅ Setup completed successfully!")
        print("\n🎉 Enhanced features ready:")
        print("   🌙 Dark Mode Theme Support")
        print("   📱 Progressive Web App (PWA)")
        print("   📊 Advanced Analytics Engine")
        print("   🔔 Smart Notification System")
        print("   ⚡ Performance Optimizations")
        print("   🎨 Modern UI Components")
        
        print("\n" + "=" * 60)
        
        # Ask if user wants to run the app
        response = input("\n🚀 Would you like to start the enhanced application now? (y/n): ")
        if response.lower() in ['y', 'yes']:
            run_enhanced_app()
        else:
            print("\n📝 To start the application later, run:")
            print("   python enhanced_lab_management_app.py")
        
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
