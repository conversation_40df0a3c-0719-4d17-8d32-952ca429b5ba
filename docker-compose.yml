version: '3.8'

services:
  # Main application service
  lab-app:
    build: .
    container_name: lab-management-app
    ports:
      - "8080:8080"  # Flet app
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./config:/app/config
    environment:
      - PYTHONPATH=/app
      - DATABASE_PATH=/app/data/lab_db.db
      - LOG_LEVEL=INFO
    depends_on:
      - redis
    networks:
      - lab-network
    restart: unless-stopped
    command: ["python", "run_lab_app.py"]

  # API service
  lab-api:
    build: .
    container_name: lab-management-api
    ports:
      - "8000:8000"  # FastAPI
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./config:/app/config
    environment:
      - PYTHONPATH=/app
      - DATABASE_PATH=/app/data/lab_db.db
      - JWT_SECRET_KEY=your-secret-key-change-in-production
      - LOG_LEVEL=INFO
    depends_on:
      - redis
    networks:
      - lab-network
    restart: unless-stopped
    command: ["python", "-m", "uvicorn", "src.api.app:app", "--host", "0.0.0.0", "--port", "8000"]

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: lab-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - lab-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # PostgreSQL database (optional, for production)
  postgres:
    image: postgres:15-alpine
    container_name: lab-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=lab_management
      - POSTGRES_USER=lab_user
      - POSTGRES_PASSWORD=lab_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - lab-network
    restart: unless-stopped

  # Nginx reverse proxy (optional, for production)
  nginx:
    image: nginx:alpine
    container_name: lab-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - lab-app
      - lab-api
    networks:
      - lab-network
    restart: unless-stopped

volumes:
  redis_data:
  postgres_data:

networks:
  lab-network:
    driver: bridge
