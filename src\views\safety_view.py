import flet as ft
import json
from datetime import datetime

class SafetyView:
    """
    Safety management view for the Science Laboratory Management System.
    Allows users to manage safety incidents and checklists.
    """
    
    def __init__(self, controller):
        """
        Initialize the safety view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        self.incidents = []
        self.filtered_incidents = []
        self.checklists = []
        self.current_tab = "incidents"  # "incidents" or "checklists"
        
        # Create tabs
        self.tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            tabs=[
                ft.Tab(
                    text="Incidents",
                    icon=ft.icons.WARNING_AMBER_ROUNDED,
                    content=ft.Container(padding=10),
                ),
                ft.Tab(
                    text="Checklists",
                    icon=ft.icons.CHECKLIST,
                    content=ft.Container(padding=10),
                ),
            ],
            on_change=self.tab_changed,
        )
        
        # Create incidents filter controls
        self.incident_search_field = ft.TextField(
            label="Search",
            prefix_icon=ft.icons.SEARCH,
            on_change=self.filter_incidents,
            expand=True,
        )
        
        self.severity_dropdown = ft.Dropdown(
            label="Severity",
            on_change=self.filter_incidents,
            options=[
                ft.dropdown.Option("All"),
                ft.dropdown.Option("Low"),
                ft.dropdown.Option("Medium"),
                ft.dropdown.Option("High"),
                ft.dropdown.Option("Critical"),
            ],
            value="All",
            width=150,
        )
        
        self.status_dropdown = ft.Dropdown(
            label="Status",
            on_change=self.filter_incidents,
            options=[
                ft.dropdown.Option("All"),
                ft.dropdown.Option("Open"),
                ft.dropdown.Option("In Progress"),
                ft.dropdown.Option("Resolved"),
            ],
            value="All",
            width=150,
        )
        
        # Create incidents data table
        self.incidents_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("Title")),
                ft.DataColumn(ft.Text("Severity")),
                ft.DataColumn(ft.Text("Status")),
                ft.DataColumn(ft.Text("Reported By")),
                ft.DataColumn(ft.Text("Reported At")),
                ft.DataColumn(ft.Text("Actions")),
            ],
            rows=[],
        )
        
        # Create checklists data table
        self.checklists_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("Title")),
                ft.DataColumn(ft.Text("Items")),
                ft.DataColumn(ft.Text("Created By")),
                ft.DataColumn(ft.Text("Created At")),
                ft.DataColumn(ft.Text("Actions")),
            ],
            rows=[],
        )
        
        # Create form fields for adding/editing incidents
        self.incident_id_field = ft.TextField(visible=False)
        self.incident_title_field = ft.TextField(label="Title", required=True)
        self.incident_description_field = ft.TextField(
            label="Description",
            multiline=True,
            min_lines=3,
            max_lines=5,
            required=True,
        )
        self.incident_severity_field = ft.Dropdown(
            label="Severity",
            required=True,
            options=[
                ft.dropdown.Option("Low"),
                ft.dropdown.Option("Medium"),
                ft.dropdown.Option("High"),
                ft.dropdown.Option("Critical"),
            ],
        )
        self.incident_status_field = ft.Dropdown(
            label="Status",
            required=True,
            options=[
                ft.dropdown.Option("Open"),
                ft.dropdown.Option("In Progress"),
                ft.dropdown.Option("Resolved"),
            ],
        )
        self.incident_resolution_field = ft.TextField(
            label="Resolution",
            multiline=True,
            min_lines=2,
            max_lines=4,
        )
        
        # Create dialog for adding/editing incidents
        self.incident_dialog = ft.AlertDialog(
            title=ft.Text("Report Incident"),
            content=ft.Column(
                [
                    self.incident_id_field,
                    self.incident_title_field,
                    self.incident_description_field,
                    self.incident_severity_field,
                    self.incident_status_field,
                    self.incident_resolution_field,
                ],
                tight=True,
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                width=500,
                height=400,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=self.close_incident_dialog),
                ft.TextButton("Save", on_click=self.save_incident),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Create form fields for adding/editing checklists
        self.checklist_id_field = ft.TextField(visible=False)
        self.checklist_title_field = ft.TextField(label="Title", required=True)
        self.checklist_description_field = ft.TextField(
            label="Description",
            multiline=True,
            min_lines=2,
            max_lines=4,
        )
        
        # Create dialog for adding/editing checklists
        self.checklist_dialog = ft.AlertDialog(
            title=ft.Text("Create Checklist"),
            content=ft.Column(
                [
                    self.checklist_id_field,
                    self.checklist_title_field,
                    self.checklist_description_field,
                    ft.Divider(),
                    ft.Text("Checklist Items", weight=ft.FontWeight.BOLD),
                    ft.Container(
                        content=ft.Column(
                            [],
                            spacing=5,
                        ),
                        id="checklist_items_container",
                    ),
                    ft.ElevatedButton(
                        "Add Item",
                        icon=ft.icons.ADD,
                        on_click=self.add_checklist_item,
                    ),
                ],
                tight=True,
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                width=500,
                height=500,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=self.close_checklist_dialog),
                ft.TextButton("Save", on_click=self.save_checklist),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Create dialog for viewing incident details
        self.incident_view_dialog = ft.AlertDialog(
            title=ft.Text("Incident Details"),
            content=ft.Column(
                [
                    ft.Text("Loading..."),
                ],
                scroll=ft.ScrollMode.AUTO,
                width=500,
                height=400,
            ),
            actions=[
                ft.TextButton("Close", on_click=self.close_view_dialog),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Create dialog for viewing checklist details
        self.checklist_view_dialog = ft.AlertDialog(
            title=ft.Text("Checklist Details"),
            content=ft.Column(
                [
                    ft.Text("Loading..."),
                ],
                scroll=ft.ScrollMode.AUTO,
                width=500,
                height=400,
            ),
            actions=[
                ft.TextButton("Close", on_click=self.close_view_dialog),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Create dialog for running a checklist
        self.run_checklist_dialog = ft.AlertDialog(
            title=ft.Text("Run Checklist"),
            content=ft.Column(
                [
                    ft.Text("Loading..."),
                ],
                scroll=ft.ScrollMode.AUTO,
                width=500,
                height=500,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=self.close_run_checklist_dialog),
                ft.TextButton("Submit", on_click=self.submit_checklist_run),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
    
    def build(self):
        """
        Build and return the safety view.
        
        Returns:
            ft.Container: The safety view container
        """
        # Load safety data
        self.load_safety_data()
        
        # Create incidents header row
        incidents_header_row = ft.Row(
            [
                ft.Text("Safety Incidents", size=20, weight=ft.FontWeight.BOLD),
                ft.ElevatedButton(
                    "Report Incident",
                    icon=ft.icons.ADD,
                    on_click=self.show_add_incident_dialog,
                ),
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        )
        
        # Create incidents filters row
        incidents_filters_row = ft.Row(
            [
                self.incident_search_field,
                self.severity_dropdown,
                self.status_dropdown,
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            wrap=True,
        )
        
        # Create incidents table container
        incidents_table_container = ft.Container(
            content=self.incidents_table,
            border=ft.border.all(1, ft.colors.GREY_300),
            border_radius=5,
            padding=10,
            expand=True,
        )
        
        # Create incidents tab content
        incidents_content = ft.Column(
            [
                incidents_header_row,
                ft.Divider(),
                incidents_filters_row,
                ft.Container(height=10),
                incidents_table_container,
            ],
            spacing=10,
            expand=True,
        )
        
        # Create checklists header row
        checklists_header_row = ft.Row(
            [
                ft.Text("Safety Checklists", size=20, weight=ft.FontWeight.BOLD),
                ft.ElevatedButton(
                    "Create Checklist",
                    icon=ft.icons.ADD,
                    on_click=self.show_add_checklist_dialog,
                ),
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        )
        
        # Create checklists table container
        checklists_table_container = ft.Container(
            content=self.checklists_table,
            border=ft.border.all(1, ft.colors.GREY_300),
            border_radius=5,
            padding=10,
            expand=True,
        )
        
        # Create checklists tab content
        checklists_content = ft.Column(
            [
                checklists_header_row,
                ft.Divider(),
                checklists_table_container,
            ],
            spacing=10,
            expand=True,
        )
        
        # Set tab contents
        self.tabs.tabs[0].content = incidents_content
        self.tabs.tabs[1].content = checklists_content
        
        # Return the complete view
        return ft.Container(
            content=ft.Column(
                [
                    ft.Text("Safety Management", size=24, weight=ft.FontWeight.BOLD),
                    ft.Container(height=10),
                    self.tabs,
                ],
                spacing=10,
                expand=True,
            ),
            padding=20,
            expand=True,
        )
    
    def load_safety_data(self):
        """Load safety data from the database."""
        # Query incidents
        self.incidents = self.controller.db.execute_query("""
            SELECT i.*, u.full_name as reporter_name
            FROM safety_incidents i
            JOIN users u ON i.reported_by = u.id
            ORDER BY i.reported_at DESC
        """)
        self.filtered_incidents = self.incidents.copy()
        
        # Query checklists
        self.checklists = self.controller.db.execute_query("""
            SELECT c.*, u.full_name as creator_name
            FROM safety_checklists c
            JOIN users u ON c.created_by = u.id
            ORDER BY c.created_at DESC
        """)
        
        # Update the tables
        self.update_incidents_table()
        self.update_checklists_table()
    
    def update_incidents_table(self):
        """Update the incidents table with filtered incidents."""
        # Clear existing rows
        self.incidents_table.rows.clear()
        
        # Add rows for filtered incidents
        for incident in self.filtered_incidents:
            # Get severity color
            severity_color = self.get_severity_color(incident["severity"])
            
            # Get status color
            status_color = self.get_status_color(incident["status"])
            
            # Format reported at
            reported_at = self.format_datetime(incident["reported_at"])
            
            # Create row
            self.incidents_table.rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(incident["title"])),
                        ft.DataCell(ft.Text(incident["severity"], color=severity_color)),
                        ft.DataCell(ft.Text(incident["status"], color=status_color)),
                        ft.DataCell(ft.Text(incident["reporter_name"])),
                        ft.DataCell(ft.Text(reported_at)),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.icons.VISIBILITY,
                                        tooltip="View",
                                        on_click=lambda e, inc_id=incident["id"]: self.show_incident_view_dialog(e, inc_id),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.EDIT,
                                        tooltip="Edit",
                                        on_click=lambda e, inc_id=incident["id"]: self.show_edit_incident_dialog(e, inc_id),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.DELETE,
                                        tooltip="Delete",
                                        on_click=lambda e, inc_id=incident["id"]: self.delete_incident(e, inc_id),
                                    ),
                                ]
                            )
                        ),
                    ]
                )
            )
        
        # Update the page
        self.controller.page.update()
    
    def update_checklists_table(self):
        """Update the checklists table."""
        # Clear existing rows
        self.checklists_table.rows.clear()
        
        # Add rows for checklists
        for checklist in self.checklists:
            # Count items
            items_count = 0
            if checklist["items"]:
                try:
                    items = json.loads(checklist["items"])
                    items_count = len(items)
                except:
                    pass
            
            # Format created at
            created_at = self.format_datetime(checklist["created_at"])
            
            # Create row
            self.checklists_table.rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(checklist["title"])),
                        ft.DataCell(ft.Text(f"{items_count} items")),
                        ft.DataCell(ft.Text(checklist["creator_name"])),
                        ft.DataCell(ft.Text(created_at)),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.icons.PLAY_ARROW,
                                        tooltip="Run Checklist",
                                        on_click=lambda e, cl_id=checklist["id"]: self.show_run_checklist_dialog(e, cl_id),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.VISIBILITY,
                                        tooltip="View",
                                        on_click=lambda e, cl_id=checklist["id"]: self.show_checklist_view_dialog(e, cl_id),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.EDIT,
                                        tooltip="Edit",
                                        on_click=lambda e, cl_id=checklist["id"]: self.show_edit_checklist_dialog(e, cl_id),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.DELETE,
                                        tooltip="Delete",
                                        on_click=lambda e, cl_id=checklist["id"]: self.delete_checklist(e, cl_id),
                                    ),
                                ]
                            )
                        ),
                    ]
                )
            )
        
        # Update the page
        self.controller.page.update()
    
    def tab_changed(self, e):
        """
        Handle tab change.
        
        Args:
            e: The change event
        """
        self.current_tab = "incidents" if e.control.selected_index == 0 else "checklists"
    
    def filter_incidents(self, e=None):
        """
        Filter incidents based on search and dropdown values.
        
        Args:
            e: The change event (optional)
        """
        search_text = self.incident_search_field.value.lower() if self.incident_search_field.value else ""
        severity = self.severity_dropdown.value
        status = self.status_dropdown.value
        
        # Filter incidents
        self.filtered_incidents = []
        for incident in self.incidents:
            # Apply search filter
            if search_text and search_text not in incident["title"].lower() and search_text not in incident["description"].lower():
                continue
            
            # Apply severity filter
            if severity != "All" and incident["severity"] != severity:
                continue
            
            # Apply status filter
            if status != "All" and incident["status"] != status:
                continue
            
            # Add incident to filtered list
            self.filtered_incidents.append(incident)
        
        # Update the incidents table
        self.update_incidents_table()
    
    def show_add_incident_dialog(self, e):
        """
        Show dialog for adding a new incident.
        
        Args:
            e: The click event
        """
        # Clear form fields
        self.incident_id_field.value = ""
        self.incident_title_field.value = ""
        self.incident_description_field.value = ""
        self.incident_severity_field.value = "Medium"
        self.incident_status_field.value = "Open"
        self.incident_resolution_field.value = ""
        
        # Set dialog title
        self.incident_dialog.title = ft.Text("Report Incident")
        
        # Show the dialog
        self.controller.page.dialog = self.incident_dialog
        self.incident_dialog.open = True
        self.controller.page.update()
    
    def show_edit_incident_dialog(self, e, incident_id):
        """
        Show dialog for editing an incident.
        
        Args:
            e: The click event
            incident_id: ID of the incident to edit
        """
        # Find the incident
        incident = next((inc for inc in self.incidents if inc["id"] == incident_id), None)
        
        if not incident:
            return
        
        # Set form fields
        self.incident_id_field.value = str(incident["id"])
        self.incident_title_field.value = incident["title"]
        self.incident_description_field.value = incident["description"]
        self.incident_severity_field.value = incident["severity"]
        self.incident_status_field.value = incident["status"]
        self.incident_resolution_field.value = incident["resolution"] or ""
        
        # Set dialog title
        self.incident_dialog.title = ft.Text("Edit Incident")
        
        # Show the dialog
        self.controller.page.dialog = self.incident_dialog
        self.incident_dialog.open = True
        self.controller.page.update()
    
    def show_incident_view_dialog(self, e, incident_id):
        """
        Show dialog for viewing incident details.
        
        Args:
            e: The click event
            incident_id: ID of the incident to view
        """
        # Find the incident
        incident = next((inc for inc in self.incidents if inc["id"] == incident_id), None)
        
        if not incident:
            return
        
        # Format dates
        reported_at = self.format_datetime(incident["reported_at"])
        resolved_at = self.format_datetime(incident["resolved_at"]) if incident["resolved_at"] else "Not resolved"
        
        # Create view content
        content = ft.Column(
            [
                ft.Text(incident["title"], size=20, weight=ft.FontWeight.BOLD),
                ft.Row(
                    [
                        ft.Text("Severity:", weight=ft.FontWeight.BOLD),
                        ft.Text(incident["severity"], color=self.get_severity_color(incident["severity"])),
                    ],
                ),
                ft.Row(
                    [
                        ft.Text("Status:", weight=ft.FontWeight.BOLD),
                        ft.Text(incident["status"], color=self.get_status_color(incident["status"])),
                    ],
                ),
                ft.Text(f"Reported by: {incident['reporter_name']}"),
                ft.Text(f"Reported at: {reported_at}"),
                ft.Divider(),
                ft.Text("Description", weight=ft.FontWeight.BOLD),
                ft.Text(incident["description"]),
                ft.Container(height=10),
                ft.Text("Resolution", weight=ft.FontWeight.BOLD),
                ft.Text(incident["resolution"] or "No resolution provided"),
                ft.Text(f"Resolved at: {resolved_at}"),
            ],
            scroll=ft.ScrollMode.AUTO,
            spacing=5,
        )
        
        # Add edit and delete buttons if user is the reporter or an administrator
        actions = [
            ft.TextButton("Close", on_click=self.close_view_dialog),
        ]
        
        if incident["reported_by"] == self.controller.current_user["id"] or self.controller.current_user["role"] == "administrator":
            actions = [
                ft.TextButton("Delete", on_click=lambda e, inc_id=incident_id: self.delete_incident_from_view(e, inc_id)),
                ft.TextButton("Edit", on_click=lambda e, inc_id=incident_id: self.edit_incident_from_view(e, inc_id)),
                ft.TextButton("Close", on_click=self.close_view_dialog),
            ]
        
        # Update dialog content
        self.incident_view_dialog.title = ft.Text("Incident Details")
        self.incident_view_dialog.content = content
        self.incident_view_dialog.actions = actions
        
        # Show the dialog
        self.controller.page.dialog = self.incident_view_dialog
        self.incident_view_dialog.open = True
        self.controller.page.update()
    
    def close_incident_dialog(self, e):
        """
        Close the incident dialog.
        
        Args:
            e: The click event
        """
        self.incident_dialog.open = False
        self.controller.page.update()
    
    def close_view_dialog(self, e):
        """
        Close the view dialog.
        
        Args:
            e: The click event
        """
        if hasattr(e.control, "dialog_id"):
            if e.control.dialog_id == "incident":
                self.incident_view_dialog.open = False
            else:
                self.checklist_view_dialog.open = False
        else:
            self.incident_view_dialog.open = False
            self.checklist_view_dialog.open = False
        
        self.controller.page.update()
    
    def edit_incident_from_view(self, e, incident_id):
        """
        Edit an incident from the view dialog.
        
        Args:
            e: The click event
            incident_id: ID of the incident to edit
        """
        # Close the view dialog
        self.incident_view_dialog.open = False
        
        # Show the edit dialog
        self.show_edit_incident_dialog(e, incident_id)
    
    def delete_incident_from_view(self, e, incident_id):
        """
        Delete an incident from the view dialog.
        
        Args:
            e: The click event
            incident_id: ID of the incident to delete
        """
        # Close the view dialog
        self.incident_view_dialog.open = False
        
        # Delete the incident
        self.delete_incident(e, incident_id)
    
    def save_incident(self, e):
        """
        Save the incident.
        
        Args:
            e: The click event
        """
        # Validate required fields
        if not self.incident_title_field.value or not self.incident_description_field.value or not self.incident_severity_field.value or not self.incident_status_field.value:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Please fill in all required fields"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
            return
        
        # Prepare incident data
        incident_data = {
            "title": self.incident_title_field.value,
            "description": self.incident_description_field.value,
            "severity": self.incident_severity_field.value,
            "status": self.incident_status_field.value,
            "resolution": self.incident_resolution_field.value if self.incident_resolution_field.value else None,
        }
        
        # Set resolved_at if status is "Resolved"
        if incident_data["status"] == "Resolved":
            incident_data["resolved_at"] = datetime.now().isoformat()
        else:
            incident_data["resolved_at"] = None
        
        # Add or update incident
        if self.incident_id_field.value:
            # Update existing incident
            incident_id = int(self.incident_id_field.value)
            
            # Build the update query
            query_parts = []
            params = []
            
            for key, value in incident_data.items():
                query_parts.append(f"{key} = ?")
                params.append(value)
            
            query = f"UPDATE safety_incidents SET {', '.join(query_parts)} WHERE id = ?"
            params.append(incident_id)
            
            # Execute the update
            self.controller.db.execute_query(query, tuple(params))
            
            # Log the update
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "update",
                "safety_incident",
                incident_id,
                f"Updated safety incident: {incident_data['title']}"
            )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Incident updated successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
        else:
            # Add new incident
            incident_data["reported_by"] = self.controller.current_user["id"]
            incident_data["reported_at"] = datetime.now().isoformat()
            
            # Build the insert query
            keys = list(incident_data.keys())
            placeholders = ["?"] * len(keys)
            
            query = f"INSERT INTO safety_incidents ({', '.join(keys)}) VALUES ({', '.join(placeholders)})"
            params = tuple(incident_data.values())
            
            # Execute the insert
            incident_id = self.controller.db.execute_insert(query, params)
            
            # Log the insert
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "create",
                "safety_incident",
                incident_id,
                f"Reported new safety incident: {incident_data['title']}"
            )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Incident reported successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
        
        # Close the dialog
        self.incident_dialog.open = False
        
        # Reload safety data
        self.load_safety_data()
        
        # Update the page
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def delete_incident(self, e, incident_id):
        """
        Delete an incident.
        
        Args:
            e: The click event
            incident_id: ID of the incident to delete
        """
        # Find the incident
        incident = next((inc for inc in self.incidents if inc["id"] == incident_id), None)
        
        if not incident:
            return
        
        # Show confirmation dialog
        def confirm_delete(e):
            # Delete the incident
            self.controller.db.execute_query(
                "DELETE FROM safety_incidents WHERE id = ?",
                (incident_id,)
            )
            
            # Log the delete
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "delete",
                "safety_incident",
                incident_id,
                f"Deleted safety incident: {incident['title']}"
            )
            
            # Close the dialog
            confirm_dialog.open = False
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Incident deleted successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
            self.controller.page.snack_bar.open = True
            
            # Reload safety data
            self.load_safety_data()
            
            # Update the page
            self.controller.page.update()
        
        # Create confirmation dialog
        confirm_dialog = ft.AlertDialog(
            title=ft.Text("Confirm Delete"),
            content=ft.Text(f"Are you sure you want to delete the incident '{incident['title']}'?"),
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(confirm_dialog, "open", False)),
                ft.TextButton("Delete", on_click=confirm_delete),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = confirm_dialog
        confirm_dialog.open = True
        self.controller.page.update()
    
    def show_add_checklist_dialog(self, e):
        """
        Show dialog for adding a new checklist.
        
        Args:
            e: The click event
        """
        # Clear form fields
        self.checklist_id_field.value = ""
        self.checklist_title_field.value = ""
        self.checklist_description_field.value = ""
        
        # Clear checklist items
        items_container = self.checklist_dialog.content.controls[5]
        items_container.content.controls.clear()
        
        # Add a few empty items
        for _ in range(3):
            self.add_checklist_item_to_container(items_container.content)
        
        # Set dialog title
        self.checklist_dialog.title = ft.Text("Create Checklist")
        
        # Show the dialog
        self.controller.page.dialog = self.checklist_dialog
        self.checklist_dialog.open = True
        self.controller.page.update()
    
    def show_edit_checklist_dialog(self, e, checklist_id):
        """
        Show dialog for editing a checklist.
        
        Args:
            e: The click event
            checklist_id: ID of the checklist to edit
        """
        # Find the checklist
        checklist = next((cl for cl in self.checklists if cl["id"] == checklist_id), None)
        
        if not checklist:
            return
        
        # Set form fields
        self.checklist_id_field.value = str(checklist["id"])
        self.checklist_title_field.value = checklist["title"]
        self.checklist_description_field.value = checklist["description"] or ""
        
        # Clear checklist items
        items_container = self.checklist_dialog.content.controls[5]
        items_container.content.controls.clear()
        
        # Add existing items
        if checklist["items"]:
            try:
                items = json.loads(checklist["items"])
                for item in items:
                    self.add_checklist_item_to_container(items_container.content, item)
            except:
                # Add a few empty items if parsing fails
                for _ in range(3):
                    self.add_checklist_item_to_container(items_container.content)
        else:
            # Add a few empty items
            for _ in range(3):
                self.add_checklist_item_to_container(items_container.content)
        
        # Set dialog title
        self.checklist_dialog.title = ft.Text("Edit Checklist")
        
        # Show the dialog
        self.controller.page.dialog = self.checklist_dialog
        self.checklist_dialog.open = True
        self.controller.page.update()
    
    def show_checklist_view_dialog(self, e, checklist_id):
        """
        Show dialog for viewing checklist details.
        
        Args:
            e: The click event
            checklist_id: ID of the checklist to view
        """
        # Find the checklist
        checklist = next((cl for cl in self.checklists if cl["id"] == checklist_id), None)
        
        if not checklist:
            return
        
        # Format created at
        created_at = self.format_datetime(checklist["created_at"])
        
        # Create items list
        items_list = ft.Column(
            spacing=5,
        )
        
        if checklist["items"]:
            try:
                items = json.loads(checklist["items"])
                for i, item in enumerate(items):
                    items_list.controls.append(
                        ft.Container(
                            content=ft.Row(
                                [
                                    ft.Text(f"{i+1}. ", weight=ft.FontWeight.BOLD),
                                    ft.Text(item),
                                ],
                            ),
                            padding=5,
                            border=ft.border.all(1, ft.colors.GREY_300),
                            border_radius=5,
                        )
                    )
            except:
                items_list.controls.append(ft.Text("Error loading checklist items"))
        else:
            items_list.controls.append(ft.Text("No items in this checklist"))
        
        # Create view content
        content = ft.Column(
            [
                ft.Text(checklist["title"], size=20, weight=ft.FontWeight.BOLD),
                ft.Text(f"Created by: {checklist['creator_name']}"),
                ft.Text(f"Created at: {created_at}"),
                ft.Divider(),
                ft.Text("Description", weight=ft.FontWeight.BOLD),
                ft.Text(checklist["description"] or "No description provided"),
                ft.Container(height=10),
                ft.Text("Checklist Items", weight=ft.FontWeight.BOLD),
                items_list,
            ],
            scroll=ft.ScrollMode.AUTO,
            spacing=5,
        )
        
        # Add edit and delete buttons if user is the creator or an administrator
        actions = [
            ft.TextButton("Close", on_click=self.close_view_dialog),
        ]
        
        if checklist["created_by"] == self.controller.current_user["id"] or self.controller.current_user["role"] == "administrator":
            actions = [
                ft.TextButton("Delete", on_click=lambda e, cl_id=checklist_id: self.delete_checklist_from_view(e, cl_id)),
                ft.TextButton("Edit", on_click=lambda e, cl_id=checklist_id: self.edit_checklist_from_view(e, cl_id)),
                ft.TextButton("Close", on_click=self.close_view_dialog),
            ]
        
        # Update dialog content
        self.checklist_view_dialog.title = ft.Text("Checklist Details")
        self.checklist_view_dialog.content = content
        self.checklist_view_dialog.actions = actions
        
        # Show the dialog
        self.controller.page.dialog = self.checklist_view_dialog
        self.checklist_view_dialog.open = True
        self.controller.page.update()
    
    def show_run_checklist_dialog(self, e, checklist_id):
        """
        Show dialog for running a checklist.
        
        Args:
            e: The click event
            checklist_id: ID of the checklist to run
        """
        # Find the checklist
        checklist = next((cl for cl in self.checklists if cl["id"] == checklist_id), None)
        
        if not checklist:
            return
        
        # Create items list with checkboxes
        items_list = ft.Column(
            spacing=10,
        )
        
        if checklist["items"]:
            try:
                items = json.loads(checklist["items"])
                for i, item in enumerate(items):
                    items_list.controls.append(
                        ft.Container(
                            content=ft.Row(
                                [
                                    ft.Checkbox(label=item, value=False),
                                    ft.TextField(
                                        label="Notes",
                                        multiline=True,
                                        min_lines=1,
                                        max_lines=2,
                                        expand=True,
                                    ),
                                ],
                                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                            ),
                            padding=5,
                            border=ft.border.all(1, ft.colors.GREY_300),
                            border_radius=5,
                        )
                    )
            except:
                items_list.controls.append(ft.Text("Error loading checklist items"))
        else:
            items_list.controls.append(ft.Text("No items in this checklist"))
        
        # Create run content
        content = ft.Column(
            [
                ft.Text(checklist["title"], size=20, weight=ft.FontWeight.BOLD),
                ft.Text(checklist["description"] or ""),
                ft.Divider(),
                ft.Text("Check each item as you complete it:", weight=ft.FontWeight.BOLD),
                items_list,
            ],
            scroll=ft.ScrollMode.AUTO,
            spacing=10,
        )
        
        # Store checklist ID for submission
        self.run_checklist_dialog.data = checklist_id
        
        # Update dialog content
        self.run_checklist_dialog.title = ft.Text("Run Checklist")
        self.run_checklist_dialog.content = content
        
        # Show the dialog
        self.controller.page.dialog = self.run_checklist_dialog
        self.run_checklist_dialog.open = True
        self.controller.page.update()
    
    def close_checklist_dialog(self, e):
        """
        Close the checklist dialog.
        
        Args:
            e: The click event
        """
        self.checklist_dialog.open = False
        self.controller.page.update()
    
    def close_run_checklist_dialog(self, e):
        """
        Close the run checklist dialog.
        
        Args:
            e: The click event
        """
        self.run_checklist_dialog.open = False
        self.controller.page.update()
    
    def edit_checklist_from_view(self, e, checklist_id):
        """
        Edit a checklist from the view dialog.
        
        Args:
            e: The click event
            checklist_id: ID of the checklist to edit
        """
        # Close the view dialog
        self.checklist_view_dialog.open = False
        
        # Show the edit dialog
        self.show_edit_checklist_dialog(e, checklist_id)
    
    def delete_checklist_from_view(self, e, checklist_id):
        """
        Delete a checklist from the view dialog.
        
        Args:
            e: The click event
            checklist_id: ID of the checklist to delete
        """
        # Close the view dialog
        self.checklist_view_dialog.open = False
        
        # Delete the checklist
        self.delete_checklist(e, checklist_id)
    
    def add_checklist_item(self, e):
        """
        Add a new item to the checklist.
        
        Args:
            e: The click event
        """
        # Get the items container
        items_container = self.checklist_dialog.content.controls[5].content
        
        # Add a new item
        self.add_checklist_item_to_container(items_container)
        
        # Update the page
        self.controller.page.update()
    
    def add_checklist_item_to_container(self, container, item_text=""):
        """
        Add a checklist item to a container.
        
        Args:
            container: The container to add the item to
            item_text (str, optional): The item text. Defaults to "".
        """
        # Create a unique ID for the item
        item_id = f"item_{len(container.controls)}"
        
        # Create the item row
        item_row = ft.Row(
            [
                ft.TextField(
                    value=item_text,
                    label="Item",
                    expand=True,
                ),
                ft.IconButton(
                    icon=ft.icons.DELETE,
                    tooltip="Remove Item",
                    on_click=lambda e, row_id=item_id: self.remove_checklist_item(e, row_id),
                ),
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        )
        
        # Set the ID on the row
        item_row.id = item_id
        
        # Add the row to the container
        container.controls.append(item_row)
    
    def remove_checklist_item(self, e, item_id):
        """
        Remove a checklist item.
        
        Args:
            e: The click event
            item_id: ID of the item to remove
        """
        # Get the items container
        items_container = self.checklist_dialog.content.controls[5].content
        
        # Find and remove the item
        for i, item in enumerate(items_container.controls):
            if item.id == item_id:
                items_container.controls.pop(i)
                break
        
        # Update the page
        self.controller.page.update()
    
    def save_checklist(self, e):
        """
        Save the checklist.
        
        Args:
            e: The click event
        """
        # Validate required fields
        if not self.checklist_title_field.value:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Please enter a title for the checklist"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
            return
        
        # Get the items container
        items_container = self.checklist_dialog.content.controls[5].content
        
        # Collect items
        items = []
        for item_row in items_container.controls:
            item_text = item_row.controls[0].value
            if item_text:
                items.append(item_text)
        
        # Validate items
        if not items:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Please add at least one item to the checklist"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
            return
        
        # Prepare checklist data
        checklist_data = {
            "title": self.checklist_title_field.value,
            "description": self.checklist_description_field.value if self.checklist_description_field.value else None,
            "items": json.dumps(items),
        }
        
        # Add or update checklist
        if self.checklist_id_field.value:
            # Update existing checklist
            checklist_id = int(self.checklist_id_field.value)
            
            # Build the update query
            query_parts = []
            params = []
            
            for key, value in checklist_data.items():
                query_parts.append(f"{key} = ?")
                params.append(value)
            
            query = f"UPDATE safety_checklists SET {', '.join(query_parts)} WHERE id = ?"
            params.append(checklist_id)
            
            # Execute the update
            self.controller.db.execute_query(query, tuple(params))
            
            # Log the update
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "update",
                "safety_checklist",
                checklist_id,
                f"Updated safety checklist: {checklist_data['title']}"
            )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Checklist updated successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
        else:
            # Add new checklist
            checklist_data["created_by"] = self.controller.current_user["id"]
            checklist_data["created_at"] = datetime.now().isoformat()
            
            # Build the insert query
            keys = list(checklist_data.keys())
            placeholders = ["?"] * len(keys)
            
            query = f"INSERT INTO safety_checklists ({', '.join(keys)}) VALUES ({', '.join(placeholders)})"
            params = tuple(checklist_data.values())
            
            # Execute the insert
            checklist_id = self.controller.db.execute_insert(query, params)
            
            # Log the insert
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "create",
                "safety_checklist",
                checklist_id,
                f"Created new safety checklist: {checklist_data['title']}"
            )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Checklist created successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
        
        # Close the dialog
        self.checklist_dialog.open = False
        
        # Reload safety data
        self.load_safety_data()
        
        # Update the page
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def delete_checklist(self, e, checklist_id):
        """
        Delete a checklist.
        
        Args:
            e: The click event
            checklist_id: ID of the checklist to delete
        """
        # Find the checklist
        checklist = next((cl for cl in self.checklists if cl["id"] == checklist_id), None)
        
        if not checklist:
            return
        
        # Show confirmation dialog
        def confirm_delete(e):
            # Delete the checklist
            self.controller.db.execute_query(
                "DELETE FROM safety_checklists WHERE id = ?",
                (checklist_id,)
            )
            
            # Log the delete
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "delete",
                "safety_checklist",
                checklist_id,
                f"Deleted safety checklist: {checklist['title']}"
            )
            
            # Close the dialog
            confirm_dialog.open = False
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Checklist deleted successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
            self.controller.page.snack_bar.open = True
            
            # Reload safety data
            self.load_safety_data()
            
            # Update the page
            self.controller.page.update()
        
        # Create confirmation dialog
        confirm_dialog = ft.AlertDialog(
            title=ft.Text("Confirm Delete"),
            content=ft.Text(f"Are you sure you want to delete the checklist '{checklist['title']}'?"),
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(confirm_dialog, "open", False)),
                ft.TextButton("Delete", on_click=confirm_delete),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = confirm_dialog
        confirm_dialog.open = True
        self.controller.page.update()
    
    def submit_checklist_run(self, e):
        """
        Submit a checklist run.
        
        Args:
            e: The click event
        """
        # Get the checklist ID
        checklist_id = self.run_checklist_dialog.data
        
        # Find the checklist
        checklist = next((cl for cl in self.checklists if cl["id"] == checklist_id), None)
        
        if not checklist:
            return
        
        # Get the items list
        items_list = self.run_checklist_dialog.content.controls[4]
        
        # Collect results
        results = []
        all_checked = True
        
        for item_container in items_list.controls:
            if isinstance(item_container, ft.Container) and isinstance(item_container.content, ft.Row):
                row = item_container.content
                if len(row.controls) >= 2:
                    checkbox = row.controls[0]
                    notes_field = row.controls[1]
                    
                    if not checkbox.value:
                        all_checked = False
                    
                    results.append({
                        "item": checkbox.label,
                        "checked": checkbox.value,
                        "notes": notes_field.value,
                    })
        
        # Check if all items are checked
        if not all_checked:
            # Show confirmation dialog
            def confirm_submit(e):
                # Close the confirmation dialog
                confirm_dialog.open = False
                
                # Submit the results
                self.save_checklist_results(checklist, results)
            
            # Create confirmation dialog
            confirm_dialog = ft.AlertDialog(
                title=ft.Text("Incomplete Checklist"),
                content=ft.Text("Not all items are checked. Do you want to submit anyway?"),
                actions=[
                    ft.TextButton("Cancel", on_click=lambda e: setattr(confirm_dialog, "open", False)),
                    ft.TextButton("Submit Anyway", on_click=confirm_submit),
                ],
                actions_alignment=ft.MainAxisAlignment.END,
            )
            
            # Show the dialog
            self.controller.page.dialog = confirm_dialog
            confirm_dialog.open = True
            self.controller.page.update()
        else:
            # Submit the results
            self.save_checklist_results(checklist, results)
    
    def save_checklist_results(self, checklist, results):
        """
        Save checklist run results.
        
        Args:
            checklist (dict): The checklist
            results (list): The results
        """
        # In a real application, we would save the results to a database
        # For this example, we'll just show a success message
        
        # Close the run dialog
        self.run_checklist_dialog.open = False
        
        # Show success message
        self.controller.page.snack_bar = ft.SnackBar(
            content=ft.Text("Checklist completed successfully"),
            bgcolor=ft.colors.GREEN_400,
        )
        self.controller.page.snack_bar.open = True
        
        # Log the checklist run
        self.controller.db.log_audit(
            self.controller.current_user["id"],
            "complete",
            "safety_checklist",
            checklist["id"],
            f"Completed safety checklist: {checklist['title']}"
        )
        
        # Update the page
        self.controller.page.update()
    
    def format_datetime(self, timestamp):
        """
        Format a datetime for display.
        
        Args:
            timestamp (str): The timestamp to format
            
        Returns:
            str: Formatted datetime
        """
        if not timestamp:
            return "N/A"
            
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime("%m/%d/%Y %I:%M %p")
        except:
            return timestamp
    
    def get_severity_color(self, severity):
        """
        Get the color for a severity level.
        
        Args:
            severity (str): The severity level
            
        Returns:
            str: The color for the severity
        """
        if severity == "Low":
            return ft.colors.GREEN
        elif severity == "Medium":
            return ft.colors.ORANGE
        elif severity == "High":
            return ft.colors.RED
        elif severity == "Critical":
            return ft.colors.RED_900
        else:
            return ft.colors.BLACK
    
    def get_status_color(self, status):
        """
        Get the color for a status.
        
        Args:
            status (str): The status
            
        Returns:
            str: The color for the status
        """
        if status == "Open":
            return ft.colors.RED
        elif status == "In Progress":
            return ft.colors.ORANGE
        elif status == "Resolved":
            return ft.colors.GREEN
        else:
            return ft.colors.BLACK