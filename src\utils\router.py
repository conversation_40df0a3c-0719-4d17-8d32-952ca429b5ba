import logging
import importlib
from urllib.parse import parse_qs

class Router:
    """
    Navigation and routing system for the Science Laboratory Management System.
    Handles route registration, navigation, and parameter parsing.
    """
    
    def __init__(self, controller):
        """
        Initialize the router.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        self.logger = logging.getLogger("router")
        
        # Dictionary to store routes
        self.routes = {}
        
        # Dictionary to store view instances
        self.view_instances = {}
        
        # Navigation history
        self.history = []
        
        # Current route
        self.current_route = None
        self.current_params = None
        
        # Maximum history size
        self.max_history = 20
    
    def register_route(self, name, module_path, class_name, requires_auth=True):
        """
        Register a route.
        
        Args:
            name (str): The route name
            module_path (str): Path to the view module
            class_name (str): Name of the view class
            requires_auth (bool, optional): Whether the route requires authentication
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if route already exists
            if name in self.routes:
                self.logger.warning(f"Route already exists: {name}")
                return False
            
            # Register route
            self.routes[name] = {
                "module_path": module_path,
                "class_name": class_name,
                "requires_auth": requires_auth
            }
            
            self.logger.info(f"Registered route: {name}")
            
            return True
        
        except Exception as e:
            self.logger.error(f"Error registering route: {str(e)}")
            return False
    
    def register_routes(self, routes):
        """
        Register multiple routes.
        
        Args:
            routes (dict): Dictionary of routes
            
        Returns:
            int: Number of successfully registered routes
        """
        success_count = 0
        
        for name, route_info in routes.items():
            if self.register_route(
                name,
                route_info["module_path"],
                route_info["class_name"],
                route_info.get("requires_auth", True)
            ):
                success_count += 1
        
        return success_count
    
    def get_view(self, route_name):
        """
        Get a view instance for a route.
        
        Args:
            route_name (str): The route name
            
        Returns:
            object: The view instance or None if not found
        """
        try:
            # Check if route exists
            if route_name not in self.routes:
                self.logger.error(f"Route not found: {route_name}")
                return None
            
            # Check if view instance already exists
            if route_name in self.view_instances:
                return self.view_instances[route_name]
            
            # Get route info
            route = self.routes[route_name]
            
            # Import the module
            module = importlib.import_module(route["module_path"])
            
            # Get the class
            view_class = getattr(module, route["class_name"])
            
            # Create an instance
            view_instance = view_class(self.controller)
            
            # Store the instance
            self.view_instances[route_name] = view_instance
            
            return view_instance
        
        except Exception as e:
            self.logger.error(f"Error getting view for route {route_name}: {str(e)}")
            return None
    
    def navigate_to(self, route_name, params=None):
        """
        Navigate to a route.
        
        Args:
            route_name (str): The route name
            params (dict, optional): Parameters for the route
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if route exists
            if route_name not in self.routes:
                self.logger.error(f"Route not found: {route_name}")
                return False
            
            # Get route info
            route = self.routes[route_name]
            
            # Check authentication
            if route["requires_auth"] and not self.controller.current_user:
                self.logger.warning(f"Authentication required for route: {route_name}")
                
                # Store the requested route for after login
                self.controller.auth.set_redirect_route(route_name, params)
                
                # Navigate to login
                return self.navigate_to("login")
            
            # Get view instance
            view = self.get_view(route_name)
            
            if not view:
                return False
            
            # Add current route to history
            if self.current_route:
                self.add_to_history(self.current_route, self.current_params)
            
            # Set current route
            self.current_route = route_name
            self.current_params = params
            
            # Update breadcrumb
            self.controller.update_breadcrumb(route_name)
            
            # Update navigation rail
            self.controller.update_nav_rail(route_name)
            
            # Build view
            content = view.build(params)
            
            # Update content
            self.controller.content.content = content
            
            # Update status
            self.controller.update_status(f"Navigated to {route_name}")
            
            # Update page
            self.controller.page.update()
            
            # Log navigation
            self.logger.info(f"Navigated to {route_name}")
            
            # Log in audit trail if user is logged in
            if self.controller.current_user:
                self.controller.db.log_audit(
                    self.controller.current_user["id"],
                    "view",
                    route_name,
                    None,
                    f"Navigated to {route_name}"
                )
            
            return True
        
        except Exception as e:
            self.logger.error(f"Error navigating to route {route_name}: {str(e)}")
            return False
    
    def navigate_back(self):
        """
        Navigate back to the previous route.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if history is empty
            if not self.history:
                # If no history, go to dashboard
                return self.navigate_to("dashboard")
            
            # Get the last route from history
            route_name, params = self.history.pop()
            
            # Navigate to it
            return self.navigate_to(route_name, params)
        
        except Exception as e:
            self.logger.error(f"Error navigating back: {str(e)}")
            return False
    
    def add_to_history(self, route_name, params=None):
        """
        Add a route to the navigation history.
        
        Args:
            route_name (str): The route name
            params (dict, optional): Parameters for the route
        """
        # Add to history
        self.history.append((route_name, params))
        
        # Limit history size
        if len(self.history) > self.max_history:
            self.history = self.history[-self.max_history:]
    
    def clear_history(self):
        """Clear the navigation history."""
        self.history = []
    
    def parse_url_params(self, url):
        """
        Parse parameters from a URL.
        
        Args:
            url (str): The URL to parse
            
        Returns:
            dict: Dictionary of parameters
        """
        try:
            # Split URL into path and query
            if "?" in url:
                path, query = url.split("?", 1)
            else:
                path, query = url, ""
            
            # Parse query parameters
            params = parse_qs(query)
            
            # Convert lists to single values for simple parameters
            for key, value in params.items():
                if len(value) == 1:
                    params[key] = value[0]
            
            return params
        
        except Exception as e:
            self.logger.error(f"Error parsing URL parameters: {str(e)}")
            return {}
    
    def build_url(self, route_name, params=None):
        """
        Build a URL for a route with parameters.
        
        Args:
            route_name (str): The route name
            params (dict, optional): Parameters for the route
            
        Returns:
            str: The URL
        """
        try:
            # Start with route name
            url = route_name
            
            # Add parameters if provided
            if params:
                query_parts = []
                
                for key, value in params.items():
                    if isinstance(value, list):
                        # Handle list values
                        for item in value:
                            query_parts.append(f"{key}={item}")
                    else:
                        # Handle single values
                        query_parts.append(f"{key}={value}")
                
                if query_parts:
                    url += "?" + "&".join(query_parts)
            
            return url
        
        except Exception as e:
            self.logger.error(f"Error building URL: {str(e)}")
            return route_name
    
    def handle_url_navigation(self, url):
        """
        Handle navigation from a URL.
        
        Args:
            url (str): The URL to navigate to
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Split URL into path and query
            if "?" in url:
                path, query = url.split("?", 1)
            else:
                path, query = url, ""
            
            # Parse query parameters
            params = self.parse_url_params(url)
            
            # Navigate to route
            return self.navigate_to(path, params)
        
        except Exception as e:
            self.logger.error(f"Error handling URL navigation: {str(e)}")
            return False