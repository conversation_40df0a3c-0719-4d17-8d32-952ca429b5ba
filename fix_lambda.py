import re

# Read the file
with open('lab_management_app_with_auth.py', 'r', encoding='utf-8') as file:
    content = file.read()

# Replace lambda e: with lambda _: for setattr cases
modified_content = re.sub(r'on_click=lambda e: setattr\(([^,]+), "open", False\)', 
                         r'on_click=lambda _: setattr(\1, "open", False)', 
                         content)

# Write the modified content back to the file
with open('lab_management_app_with_auth.py', 'w', encoding='utf-8') as file:
    file.write(modified_content)

print("File updated successfully!")