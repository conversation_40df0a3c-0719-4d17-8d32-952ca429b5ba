import flet as ft
from datetime import datetime
import re

class RegistrationView:
    """
    Registration view for the Science Laboratory Management System.
    Allows new users to create an account.
    """
    
    def __init__(self, controller):
        """
        Initialize the registration view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        self.visible = True
        
        # Create registration form controls
        self.username_field = ft.TextField(
            label="Username",
            autofocus=True,
            width=300,
            helper_text="Choose a unique username (minimum 5 characters)",
        )
        
        self.full_name_field = ft.TextField(
            label="Full Name",
            width=300,
            helper_text="Enter your full name",
        )
        
        self.email_field = ft.TextField(
            label="Email",
            width=300,
            helper_text="Enter a valid email address",
            keyboard_type=ft.KeyboardType.EMAIL,
        )
        
        self.password_field = ft.TextField(
            label="Password",
            password=True,
            can_reveal_password=True,
            width=300,
            helper_text="Minimum 8 characters with letters and numbers",
        )
        
        self.confirm_password_field = ft.TextField(
            label="Confirm Password",
            password=True,
            can_reveal_password=True,
            width=300,
        )
        
        self.department_field = ft.TextField(
            label="Department",
            width=300,
            helper_text="Enter your department or research group",
        )
        
        self.error_text = ft.Text(
            color=ft.colors.RED_500,
            size=12,
            visible=False,
        )
        
        self.register_button = ft.ElevatedButton(
            text="Register",
            width=300,
            on_click=self.register_clicked,
        )
        
        self.login_link = ft.TextButton(
            text="Already have an account? Login",
            on_click=self.login_clicked,
        )
    
    def build(self):
        """
        Build and return the registration view.
        
        Returns:
            ft.Container: The registration view container
        """
        return ft.Container(
            content=ft.Column(
                [
                    ft.Container(height=50),
                    ft.Text("Create an Account", size=28, weight=ft.FontWeight.BOLD),
                    ft.Container(height=10),
                    ft.Text("Register for the Science Laboratory Management System", size=16),
                    ft.Container(height=30),
                    self.username_field,
                    self.full_name_field,
                    self.email_field,
                    self.password_field,
                    self.confirm_password_field,
                    self.department_field,
                    self.error_text,
                    ft.Container(height=10),
                    self.register_button,
                    self.login_link,
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            ),
            alignment=ft.alignment.center,
            expand=True,
            visible=self.visible,
        )
    
    def validate_form(self):
        """
        Validate the registration form fields.
        
        Returns:
            bool: True if valid, False otherwise
        """
        # Check for empty fields
        if (not self.username_field.value or 
            not self.full_name_field.value or 
            not self.email_field.value or 
            not self.password_field.value or 
            not self.confirm_password_field.value):
            self.error_text.value = "Please fill in all required fields."
            self.error_text.visible = True
            return False
        
        # Validate username length
        if len(self.username_field.value) < 5:
            self.error_text.value = "Username must be at least 5 characters long."
            self.error_text.visible = True
            return False
        
        # Validate email format using regex
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, self.email_field.value):
            self.error_text.value = "Please enter a valid email address."
            self.error_text.visible = True
            return False
        
        # Validate password strength
        if len(self.password_field.value) < 8:
            self.error_text.value = "Password must be at least 8 characters long."
            self.error_text.visible = True
            return False
        
        # Check if password contains both letters and numbers
        if not (any(c.isalpha() for c in self.password_field.value) and 
                any(c.isdigit() for c in self.password_field.value)):
            self.error_text.value = "Password must contain both letters and numbers."
            self.error_text.visible = True
            return False
        
        # Check if passwords match
        if self.password_field.value != self.confirm_password_field.value:
            self.error_text.value = "Passwords do not match."
            self.error_text.visible = True
            return False
        
        # Check if username already exists
        user = self.controller.db.execute_query(
            "SELECT id FROM users WHERE username = ?",
            (self.username_field.value,)
        )
        
        if user and len(user) > 0:
            self.error_text.value = "Username already exists. Please choose another one."
            self.error_text.visible = True
            return False
        
        # Check if email already exists
        user = self.controller.db.execute_query(
            "SELECT id FROM users WHERE email = ?",
            (self.email_field.value,)
        )
        
        if user and len(user) > 0:
            self.error_text.value = "Email already registered. Please use another email or reset your password."
            self.error_text.visible = True
            return False
        
        return True
    
    def register_clicked(self, e):
        """
        Handle registration button click.
        
        Args:
            e: The click event
        """
        # Validate the form
        if not self.validate_form():
            self.controller.page.update()
            return
        
        # Create new user
        hashed_password = self.controller.security.hash_password(self.password_field.value)
        
        new_user = {
            "username": self.username_field.value,
            "password": hashed_password,
            "full_name": self.full_name_field.value,
            "email": self.email_field.value,
            "department": self.department_field.value,
            "role": "user",  # Default role
            "created_at": datetime.now().isoformat(),
            "last_login": None,
            "status": "pending",  # New accounts pending approval
        }
        
        # Build the insert query
        keys = list(new_user.keys())
        placeholders = ["?"] * len(keys)
        
        query = f"INSERT INTO users ({', '.join(keys)}) VALUES ({', '.join(placeholders)})"
        params = tuple(new_user.values())
        
        # Execute the insert
        user_id = self.controller.db.execute_insert(query, params)
        
        # Show success message
        self.controller.page.snack_bar = ft.SnackBar(
            content=ft.Text("Registration successful! Your account is pending approval."),
            bgcolor=ft.colors.GREEN_400,
        )
        self.controller.page.snack_bar.open = True
        
        # Reset form fields
        self.username_field.value = ""
        self.full_name_field.value = ""
        self.email_field.value = ""
        self.password_field.value = ""
        self.confirm_password_field.value = ""
        self.department_field.value = ""
        self.error_text.visible = False
        
        # Navigate to login view
        self.controller.show_login_view()
        
        # Update the page
        self.controller.page.update()
    
    def login_clicked(self, e):
        """
        Handle login link click.
        
        Args:
            e: The click event
        """
        self.controller.show_login_view()