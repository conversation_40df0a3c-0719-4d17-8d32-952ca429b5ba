import flet as ft
import json
import os
from datetime import datetime

class SettingsView:
    """
    Settings view for the Science Laboratory Management System.
    Allows users to configure system settings, preferences, and lab configurations.
    """
    
    def __init__(self, controller):
        """
        Initialize the settings view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        self.settings = {}
        self.current_tab = "system"  # "system", "user", "lab", or "notifications"
        
        # Create tabs
        self.tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            tabs=[
                ft.Tab(
                    text="System Settings",
                    icon=ft.icons.SETTINGS,
                    content=ft.Container(padding=10),
                ),
                ft.Tab(
                    text="User Preferences",
                    icon=ft.icons.PERSON,
                    content=ft.Container(padding=10),
                ),
                ft.Tab(
                    text="Lab Configuration",
                    icon=ft.icons.SCIENCE,
                    content=ft.Container(padding=10),
                ),
                ft.Tab(
                    text="Notifications",
                    icon=ft.icons.NOTIFICATIONS,
                    content=ft.Container(padding=10),
                ),
            ],
            on_change=self.tab_changed,
        )
        
        # System settings controls
        self.db_backup_path_field = ft.TextField(
            label="Database Backup Path",
            expand=True,
        )
        
        self.auto_backup_dropdown = ft.Dropdown(
            label="Automatic Backup Frequency",
            options=[
                ft.dropdown.Option("daily"),
                ft.dropdown.Option("weekly"),
                ft.dropdown.Option("monthly"),
                ft.dropdown.Option("never"),
            ],
            width=200,
        )
        
        self.backup_now_button = ft.ElevatedButton(
            text="Backup Now",
            icon=ft.icons.BACKUP,
            on_click=self.backup_now_clicked,
        )
        
        self.log_retention_dropdown = ft.Dropdown(
            label="Log Retention Period",
            options=[
                ft.dropdown.Option("30 days"),
                ft.dropdown.Option("60 days"),
                ft.dropdown.Option("90 days"),
                ft.dropdown.Option("180 days"),
                ft.dropdown.Option("365 days"),
            ],
            width=200,
        )
        
        self.clear_logs_button = ft.ElevatedButton(
            text="Clear Old Logs",
            icon=ft.icons.DELETE_SWEEP,
            on_click=self.clear_logs_clicked,
        )
        
        # User preferences controls
        self.theme_dropdown = ft.Dropdown(
            label="Theme",
            options=[
                ft.dropdown.Option("light"),
                ft.dropdown.Option("dark"),
                ft.dropdown.Option("system"),
            ],
            width=200,
        )
        
        self.date_format_dropdown = ft.Dropdown(
            label="Date Format",
            options=[
                ft.dropdown.Option("MM/DD/YYYY"),
                ft.dropdown.Option("DD/MM/YYYY"),
                ft.dropdown.Option("YYYY-MM-DD"),
            ],
            width=200,
        )
        
        self.time_format_dropdown = ft.Dropdown(
            label="Time Format",
            options=[
                ft.dropdown.Option("12-hour"),
                ft.dropdown.Option("24-hour"),
            ],
            width=200,
        )
        
        self.items_per_page_dropdown = ft.Dropdown(
            label="Items Per Page",
            options=[
                ft.dropdown.Option("10"),
                ft.dropdown.Option("25"),
                ft.dropdown.Option("50"),
                ft.dropdown.Option("100"),
            ],
            width=200,
        )
        
        # Lab configuration controls
        self.lab_rooms_list = ft.ListView(
            spacing=2,
            padding=10,
            auto_scroll=True,
            height=200,
        )
        
        self.add_room_button = ft.ElevatedButton(
            text="Add Room",
            icon=ft.icons.ADD,
            on_click=self.add_room_clicked,
        )
        
        self.equipment_categories_list = ft.ListView(
            spacing=2,
            padding=10,
            auto_scroll=True,
            height=200,
        )
        
        self.add_category_button = ft.ElevatedButton(
            text="Add Category",
            icon=ft.icons.ADD,
            on_click=self.add_category_clicked,
        )
        
        # Notification settings controls
        self.email_notifications_switch = ft.Switch(
            label="Email Notifications",
            value=True,
        )
        
        self.email_address_field = ft.TextField(
            label="Email Address",
            expand=True,
        )
        
        self.inventory_alerts_switch = ft.Switch(
            label="Inventory Alerts",
            value=True,
        )
        
        self.experiment_reminders_switch = ft.Switch(
            label="Experiment Reminders",
            value=True,
        )
        
        self.schedule_reminders_switch = ft.Switch(
            label="Schedule Reminders",
            value=True,
        )
        
        self.safety_alerts_switch = ft.Switch(
            label="Safety Alerts",
            value=True,
        )
        
        # Save button
        self.save_button = ft.ElevatedButton(
            text="Save Settings",
            icon=ft.icons.SAVE,
            on_click=self.save_settings_clicked,
        )
    
    def build(self):
        """
        Build and return the settings view.
        
        Returns:
            ft.Container: The settings view container
        """
        # Load settings
        self.load_settings()
        
        # Build system settings tab
        system_settings_content = ft.Column(
            [
                ft.Text("Database Settings", size=18, weight=ft.FontWeight.BOLD),
                ft.Row(
                    [
                        self.db_backup_path_field,
                        ft.IconButton(
                            icon=ft.icons.FOLDER_OPEN,
                            tooltip="Browse",
                            on_click=self.browse_backup_path,
                        ),
                    ],
                ),
                ft.Row(
                    [
                        self.auto_backup_dropdown,
                        self.backup_now_button,
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Divider(),
                ft.Text("Log Settings", size=18, weight=ft.FontWeight.BOLD),
                ft.Row(
                    [
                        self.log_retention_dropdown,
                        self.clear_logs_button,
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
            ],
            spacing=10,
        )
        
        # Build user preferences tab
        user_preferences_content = ft.Column(
            [
                ft.Text("Display Settings", size=18, weight=ft.FontWeight.BOLD),
                self.theme_dropdown,
                ft.Row(
                    [
                        self.date_format_dropdown,
                        self.time_format_dropdown,
                    ],
                ),
                ft.Divider(),
                ft.Text("List Settings", size=18, weight=ft.FontWeight.BOLD),
                self.items_per_page_dropdown,
            ],
            spacing=10,
        )
        
        # Build lab configuration tab
        lab_configuration_content = ft.Column(
            [
                ft.Text("Lab Rooms", size=18, weight=ft.FontWeight.BOLD),
                ft.Container(
                    content=self.lab_rooms_list,
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                ),
                self.add_room_button,
                ft.Divider(),
                ft.Text("Equipment Categories", size=18, weight=ft.FontWeight.BOLD),
                ft.Container(
                    content=self.equipment_categories_list,
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                ),
                self.add_category_button,
            ],
            spacing=10,
        )
        
        # Build notifications tab
        notifications_content = ft.Column(
            [
                ft.Text("Email Settings", size=18, weight=ft.FontWeight.BOLD),
                self.email_notifications_switch,
                self.email_address_field,
                ft.Divider(),
                ft.Text("Alert Types", size=18, weight=ft.FontWeight.BOLD),
                self.inventory_alerts_switch,
                self.experiment_reminders_switch,
                self.schedule_reminders_switch,
                self.safety_alerts_switch,
            ],
            spacing=10,
        )
        
        # Set tab contents
        self.tabs.tabs[0].content = system_settings_content
        self.tabs.tabs[1].content = user_preferences_content
        self.tabs.tabs[2].content = lab_configuration_content
        self.tabs.tabs[3].content = notifications_content
        
        # Return the complete view
        return ft.Container(
            content=ft.Column(
                [
                    ft.Text("Settings", size=24, weight=ft.FontWeight.BOLD),
                    ft.Container(height=10),
                    self.tabs,
                    ft.Container(height=20),
                    ft.Row(
                        [
                            self.save_button,
                        ],
                        alignment=ft.MainAxisAlignment.END,
                    ),
                ],
                spacing=10,
                expand=True,
            ),
            padding=20,
            expand=True,
        )
    
    def load_settings(self):
        """Load settings from the database."""
        # Load system settings
        system_settings = self.controller.db.execute_query(
            "SELECT * FROM system_settings WHERE id = 1"
        )
        
        if system_settings and len(system_settings) > 0:
            self.db_backup_path_field.value = system_settings[0].get("db_backup_path", "")
            self.auto_backup_dropdown.value = system_settings[0].get("auto_backup_frequency", "weekly")
            self.log_retention_dropdown.value = system_settings[0].get("log_retention_period", "90 days")
        
        # Load user preferences
        if self.controller.current_user:
            user_prefs = self.controller.db.execute_query(
                "SELECT preferences FROM users WHERE id = ?",
                (self.controller.current_user["id"],)
            )
            
            if user_prefs and len(user_prefs) > 0 and user_prefs[0]["preferences"]:
                try:
                    prefs = json.loads(user_prefs[0]["preferences"])
                    self.theme_dropdown.value = prefs.get("theme", "light")
                    self.date_format_dropdown.value = prefs.get("date_format", "MM/DD/YYYY")
                    self.time_format_dropdown.value = prefs.get("time_format", "12-hour")
                    self.items_per_page_dropdown.value = prefs.get("items_per_page", "25")
                    
                    # Load notification preferences
                    self.email_notifications_switch.value = prefs.get("email_notifications", True)
                    self.email_address_field.value = prefs.get("email_address", self.controller.current_user["email"])
                    self.inventory_alerts_switch.value = prefs.get("inventory_alerts", True)
                    self.experiment_reminders_switch.value = prefs.get("experiment_reminders", True)
                    self.schedule_reminders_switch.value = prefs.get("schedule_reminders", True)
                    self.safety_alerts_switch.value = prefs.get("safety_alerts", True)
                except:
                    # Set defaults if preferences can't be parsed
                    self.set_default_preferences()
            else:
                # Set defaults if no preferences found
                self.set_default_preferences()
        
        # Load lab configuration
        self.load_lab_rooms()
        self.load_equipment_categories()
    
    def set_default_preferences(self):
        """Set default user preferences."""
        self.theme_dropdown.value = "light"
        self.date_format_dropdown.value = "MM/DD/YYYY"
        self.time_format_dropdown.value = "12-hour"
        self.items_per_page_dropdown.value = "25"
        
        self.email_notifications_switch.value = True
        self.email_address_field.value = self.controller.current_user["email"] if self.controller.current_user else ""
        self.inventory_alerts_switch.value = True
        self.experiment_reminders_switch.value = True
        self.schedule_reminders_switch.value = True
        self.safety_alerts_switch.value = True
    
    def load_lab_rooms(self):
        """Load lab rooms from the database."""
        # Clear existing items
        self.lab_rooms_list.controls.clear()
        
        # Query lab rooms
        rooms = self.controller.db.execute_query(
            "SELECT * FROM lab_rooms ORDER BY name"
        )
        
        # Add rooms to the list
        for room in rooms:
            self.lab_rooms_list.controls.append(
                self.create_list_item(room["name"], room["id"], "room")
            )
        
        # Update the page
        self.controller.page.update()
    
    def load_equipment_categories(self):
        """Load equipment categories from the database."""
        # Clear existing items
        self.equipment_categories_list.controls.clear()
        
        # Query categories
        categories = self.controller.db.execute_query(
            "SELECT * FROM equipment_categories ORDER BY name"
        )
        
        # Add categories to the list
        for category in categories:
            self.equipment_categories_list.controls.append(
                self.create_list_item(category["name"], category["id"], "category")
            )
        
        # Update the page
        self.controller.page.update()
    
    def create_list_item(self, name, item_id, item_type):
        """
        Create a list item with edit and delete buttons.
        
        Args:
            name (str): The item name
            item_id (int): The item ID
            item_type (str): The item type ("room" or "category")
            
        Returns:
            ft.Container: The list item container
        """
        return ft.Container(
            content=ft.Row(
                [
                    ft.Text(name, expand=True),
                    ft.IconButton(
                        icon=ft.icons.EDIT,
                        tooltip="Edit",
                        on_click=lambda e, id=item_id, type=item_type, n=name: self.edit_item(e, id, type, n),
                    ),
                    ft.IconButton(
                        icon=ft.icons.DELETE,
                        tooltip="Delete",
                        on_click=lambda e, id=item_id, type=item_type: self.delete_item(e, id, type),
                    ),
                ],
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            ),
            padding=5,
            border=ft.border.all(1, ft.colors.GREY_300),
            border_radius=5,
            margin=ft.margin.only(bottom=5),
        )
    
    def tab_changed(self, e):
        """
        Handle tab change.
        
        Args:
            e: The change event
        """
        self.current_tab = ["system", "user", "lab", "notifications"][e.control.selected_index]
    
    def browse_backup_path(self, e):
        """
        Show dialog to browse for backup path.
        
        Args:
            e: The click event
        """
        # In a real application, this would open a file picker
        # For this example, we'll just show a dialog with a text field
        
        def save_path(e):
            self.db_backup_path_field.value = path_field.value
            dialog.open = False
            self.controller.page.update()
        
        path_field = ft.TextField(
            label="Backup Path",
            value=self.db_backup_path_field.value,
            width=400,
        )
        
        dialog = ft.AlertDialog(
            title=ft.Text("Select Backup Path"),
            content=path_field,
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(dialog, "open", False)),
                ft.TextButton("Save", on_click=save_path),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        self.controller.page.dialog = dialog
        dialog.open = True
        self.controller.page.update()
    
    def backup_now_clicked(self, e):
        """
        Handle backup now button click.
        
        Args:
            e: The click event
        """
        # In a real application, this would perform a database backup
        # For this example, we'll just show a success message
        
        self.controller.page.snack_bar = ft.SnackBar(
            content=ft.Text("Database backup completed successfully"),
            bgcolor=ft.colors.GREEN_400,
        )
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def clear_logs_clicked(self, e):
        """
        Handle clear logs button click.
        
        Args:
            e: The click event
        """
        # In a real application, this would clear old logs
        # For this example, we'll just show a success message
        
        self.controller.page.snack_bar = ft.SnackBar(
            content=ft.Text("Old logs cleared successfully"),
            bgcolor=ft.colors.GREEN_400,
        )
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def add_room_clicked(self, e):
        """
        Handle add room button click.
        
        Args:
            e: The click event
        """
        self.show_add_item_dialog("room")
    
    def add_category_clicked(self, e):
        """
        Handle add category button click.
        
        Args:
            e: The click event
        """
        self.show_add_item_dialog("category")
    
    def show_add_item_dialog(self, item_type):
        """
        Show dialog to add a new item.
        
        Args:
            item_type (str): The item type ("room" or "category")
        """
        def add_item(e):
            name = name_field.value
            
            if not name:
                return
            
            if item_type == "room":
                # Add room to database
                self.controller.db.execute_query(
                    "INSERT INTO lab_rooms (name) VALUES (?)",
                    (name,)
                )
                
                # Reload rooms
                self.load_lab_rooms()
            else:
                # Add category to database
                self.controller.db.execute_query(
                    "INSERT INTO equipment_categories (name) VALUES (?)",
                    (name,)
                )
                
                # Reload categories
                self.load_equipment_categories()
            
            # Close dialog
            dialog.open = False
            self.controller.page.update()
        
        name_field = ft.TextField(
            label="Name",
            autofocus=True,
            width=300,
        )
        
        dialog = ft.AlertDialog(
            title=ft.Text(f"Add {'Room' if item_type == 'room' else 'Category'}"),
            content=name_field,
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(dialog, "open", False)),
                ft.TextButton("Add", on_click=add_item),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        self.controller.page.dialog = dialog
        dialog.open = True
        self.controller.page.update()
    
    def edit_item(self, e, item_id, item_type, name):
        """
        Show dialog to edit an item.
        
        Args:
            e: The click event
            item_id (int): The item ID
            item_type (str): The item type ("room" or "category")
            name (str): The current item name
        """
        def save_item(e):
            new_name = name_field.value
            
            if not new_name:
                return
            
            if item_type == "room":
                # Update room in database
                self.controller.db.execute_query(
                    "UPDATE lab_rooms SET name = ? WHERE id = ?",
                    (new_name, item_id)
                )
                
                # Reload rooms
                self.load_lab_rooms()
            else:
                # Update category in database
                self.controller.db.execute_query(
                    "UPDATE equipment_categories SET name = ? WHERE id = ?",
                    (new_name, item_id)
                )
                
                # Reload categories
                self.load_equipment_categories()
            
            # Close dialog
            dialog.open = False
            self.controller.page.update()
        
        name_field = ft.TextField(
            label="Name",
            value=name,
            autofocus=True,
            width=300,
        )
        
        dialog = ft.AlertDialog(
            title=ft.Text(f"Edit {'Room' if item_type == 'room' else 'Category'}"),
            content=name_field,
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(dialog, "open", False)),
                ft.TextButton("Save", on_click=save_item),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        self.controller.page.dialog = dialog
        dialog.open = True
        self.controller.page.update()
    
    def delete_item(self, e, item_id, item_type):
        """
        Show confirmation dialog to delete an item.
        
        Args:
            e: The click event
            item_id (int): The item ID
            item_type (str): The item type ("room" or "category")
        """
        def confirm_delete(e):
            if item_type == "room":
                # Delete room from database
                self.controller.db.execute_query(
                    "DELETE FROM lab_rooms WHERE id = ?",
                    (item_id,)
                )
                
                # Reload rooms
                self.load_lab_rooms()
            else:
                # Delete category from database
                self.controller.db.execute_query(
                    "DELETE FROM equipment_categories WHERE id = ?",
                    (item_id,)
                )
                
                # Reload categories
                self.load_equipment_categories()
            
            # Close dialog
            dialog.open = False
            self.controller.page.update()
        
        dialog = ft.AlertDialog(
            title=ft.Text(f"Delete {'Room' if item_type == 'room' else 'Category'}"),
            content=ft.Text("Are you sure you want to delete this item?"),
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(dialog, "open", False)),
                ft.TextButton("Delete", on_click=confirm_delete),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        self.controller.page.dialog = dialog
        dialog.open = True
        self.controller.page.update()
    
    def save_settings_clicked(self, e):
        """
        Handle save settings button click.
        
        Args:
            e: The click event
        """
        # Save system settings
        self.controller.db.execute_query(
            """
            INSERT OR REPLACE INTO system_settings 
            (id, db_backup_path, auto_backup_frequency, log_retention_period, last_updated) 
            VALUES (?, ?, ?, ?, ?)
            """,
            (
                1,
                self.db_backup_path_field.value,
                self.auto_backup_dropdown.value,
                self.log_retention_dropdown.value,
                datetime.now().isoformat(),
            )
        )
        
        # Save user preferences
        if self.controller.current_user:
            prefs = {
                "theme": self.theme_dropdown.value,
                "date_format": self.date_format_dropdown.value,
                "time_format": self.time_format_dropdown.value,
                "items_per_page": self.items_per_page_dropdown.value,
                "email_notifications": self.email_notifications_switch.value,
                "email_address": self.email_address_field.value,
                "inventory_alerts": self.inventory_alerts_switch.value,
                "experiment_reminders": self.experiment_reminders_switch.value,
                "schedule_reminders": self.schedule_reminders_switch.value,
                "safety_alerts": self.safety_alerts_switch.value,
            }
            
            self.controller.db.execute_query(
                "UPDATE users SET preferences = ? WHERE id = ?",
                (json.dumps(prefs), self.controller.current_user["id"])
            )
            
            # Update current user in memory
            self.controller.current_user["preferences"] = json.dumps(prefs)
        
        # Show success message
        self.controller.page.snack_bar = ft.SnackBar(
            content=ft.Text("Settings saved successfully"),
            bgcolor=ft.colors.GREEN_400,
        )
        self.controller.page.snack_bar.open = True
        self.controller.page.update()