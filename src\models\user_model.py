from datetime import datetime, timedelta
import ipaddress

class UserModel:
    """
    Model for user-related database operations.
    """
    
    def __init__(self, db, security):
        """
        Initialize the user model.
        
        Args:
            db: Database instance
            security: Security utility instance
        """
        self.db = db
        self.security = security
    
    def authenticate(self, username, password, ip_address=None, user_agent=None):
        """
        Authenticate a user with username and password.
        
        Args:
            username (str): The username
            password (str): The password
            ip_address (str, optional): Client IP address
            user_agent (str, optional): Client user agent
            
        Returns:
            tuple: (user_data, error_message, requires_2fa) if successful, (None, error_message, False) otherwise
        """
        # Query the database for the user
        users = self.db.execute_query(
            "SELECT * FROM users WHERE username = ? AND is_active = 1",
            (username,)
        )
        
        if not users:
            return None, "Invalid username or password", False
        
        user = users[0]
        
        # Check if account is locked
        if user['lockout_until'] and datetime.fromisoformat(user['lockout_until']) > datetime.now():
            lockout_minutes = round((datetime.fromisoformat(user['lockout_until']) - datetime.now()).total_seconds() / 60)
            return None, f"Account is locked. Try again in {lockout_minutes} minutes", False
        
        # Verify password
        if not self.security.verify_password(password, user['password_hash']):
            # Increment failed login attempts
            new_attempts = (user['failed_login_attempts'] or 0) + 1
            
            # Check if we should lock the account
            if new_attempts >= self.security.max_failed_attempts:
                lockout_until = (datetime.now() + timedelta(minutes=self.security.lockout_duration)).isoformat()
                self.db.execute_query(
                    "UPDATE users SET failed_login_attempts = ?, lockout_until = ? WHERE id = ?",
                    (new_attempts, lockout_until, user['id'])
                )
                
                # Log the lockout
                self.db.log_audit(
                    user['id'],
                    "lockout",
                    "user",
                    user['id'],
                    f"Account locked due to too many failed login attempts: {username}"
                )
                
                return None, f"Too many failed login attempts. Account locked for {self.security.lockout_duration} minutes", False
            else:
                # Just update the failed attempts
                self.db.execute_query(
                    "UPDATE users SET failed_login_attempts = ? WHERE id = ?",
                    (new_attempts, user['id'])
                )
            
            return None, "Invalid username or password", False
        
        # Check if 2FA is required
        requires_2fa = bool(user['totp_enabled'])
        
        # If 2FA is not required, complete the login process
        if not requires_2fa:
            self._complete_login(user, ip_address, user_agent)
            return user, None, False
        
        # If 2FA is required, return the user but indicate 2FA is needed
        return user, None, True
    
    def verify_2fa(self, user_id, token, ip_address=None, user_agent=None):
        """
        Verify a 2FA token and complete the login process.
        
        Args:
            user_id (int): The user ID
            token (str): The 2FA token
            ip_address (str, optional): Client IP address
            user_agent (str, optional): Client user agent
            
        Returns:
            tuple: (user_data, error_message) if successful, (None, error_message) otherwise
        """
        # Get the user
        users = self.db.execute_query(
            "SELECT * FROM users WHERE id = ? AND is_active = 1",
            (user_id,)
        )
        
        if not users:
            return None, "User not found"
        
        user = users[0]
        
        # Verify the token
        if not self.security.verify_totp(user['totp_secret'], token):
            return None, "Invalid verification code"
        
        # Complete the login process
        self._complete_login(user, ip_address, user_agent)
        
        return user, None
    
    def _complete_login(self, user, ip_address=None, user_agent=None):
        """
        Complete the login process after successful authentication.
        
        Args:
            user (dict): The user data
            ip_address (str, optional): Client IP address
            user_agent (str, optional): Client user agent
        """
        # Reset failed login attempts
        self.db.execute_query(
            "UPDATE users SET failed_login_attempts = 0, lockout_until = NULL, last_login = ? WHERE id = ?",
            (datetime.now().isoformat(), user['id'])
        )
        
        # Create a session
        token = self.security.generate_session_token(user['id'], user['username'], user['role'])
        expires_at = (datetime.now() + timedelta(hours=self.security.token_expiry)).isoformat()
        
        self.db.execute_insert(
            "INSERT INTO sessions (user_id, token, expires_at, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)",
            (user['id'], token, expires_at, ip_address, user_agent)
        )
        
        # Log the login
        self.db.log_audit(
            user['id'],
            "login",
            "user",
            user['id'],
            f"User logged in: {user['username']}"
        )
        
        return token
    
    def get_user_by_token(self, token):
        """
        Get a user by session token.
        
        Args:
            token (str): The session token
            
        Returns:
            dict: User data if found and token is valid, None otherwise
        """
        # Verify the token
        payload = self.security.verify_session_token(token)
        if not payload:
            return None
        
        # Check if the session exists and is active
        sessions = self.db.execute_query(
            "SELECT * FROM sessions WHERE token = ? AND is_active = 1 AND expires_at > ?",
            (token, datetime.now().isoformat())
        )
        
        if not sessions:
            return None
        
        # Get the user
        return self.get_user_by_id(payload['user_id'])
    
    def logout(self, token):
        """
        Log out a user by invalidating their session token.
        
        Args:
            token (str): The session token
            
        Returns:
            bool: True if logout successful, False otherwise
        """
        # Invalidate the session
        self.db.execute_query(
            "UPDATE sessions SET is_active = 0 WHERE token = ?",
            (token,)
        )
        
        # Get user ID from token
        payload = self.security.verify_session_token(token)
        if payload:
            # Log the logout
            self.db.log_audit(
                payload['user_id'],
                "logout",
                "user",
                payload['user_id'],
                f"User logged out: {payload['username']}"
            )
        
        return True
    
    def get_all_users(self):
        """
        Get all users.
        
        Returns:
            list: List of all users
        """
        return self.db.execute_query(
            """SELECT id, username, full_name, email, role, created_at, last_login, 
            is_active, failed_login_attempts, lockout_until, totp_enabled, 
            require_password_change FROM users"""
        )
    
    def get_user_by_id(self, user_id):
        """
        Get a user by ID.
        
        Args:
            user_id (int): The user ID
            
        Returns:
            dict: User data if found, None otherwise
        """
        users = self.db.execute_query(
            """SELECT id, username, full_name, email, role, created_at, last_login, 
            is_active, failed_login_attempts, lockout_until, totp_enabled, 
            require_password_change FROM users WHERE id = ?""",
            (user_id,)
        )
        
        return users[0] if users else None
    
    def get_user_by_username(self, username):
        """
        Get a user by username.
        
        Args:
            username (str): The username
            
        Returns:
            dict: User data if found, None otherwise
        """
        users = self.db.execute_query(
            """SELECT id, username, full_name, email, role, created_at, last_login, 
            is_active, failed_login_attempts, lockout_until, totp_enabled, 
            require_password_change FROM users WHERE username = ?""",
            (username,)
        )
        
        return users[0] if users else None
    
    def get_user_by_email(self, email):
        """
        Get a user by email.
        
        Args:
            email (str): The email
            
        Returns:
            dict: User data if found, None otherwise
        """
        users = self.db.execute_query(
            """SELECT id, username, full_name, email, role, created_at, last_login, 
            is_active, failed_login_attempts, lockout_until, totp_enabled, 
            require_password_change FROM users WHERE email = ?""",
            (email,)
        )
        
        return users[0] if users else None
    
    def create_user(self, username, password, full_name, email, role, created_by=None):
        """
        Create a new user.
        
        Args:
            username (str): The username
            password (str): The password
            full_name (str): The user's full name
            email (str): The user's email
            role (str): The user's role
            created_by (int, optional): ID of the user creating this user
            
        Returns:
            tuple: (user_id, error_message) if successful, (None, error_message) if failed
        """
        # Check if username already exists
        if self.get_user_by_username(username):
            return None, "Username already exists"
        
        # Check if email already exists
        if self.get_user_by_email(email):
            return None, "Email already exists"
        
        # Check password complexity
        is_valid, error_message = self.security.check_password_complexity(password)
        if not is_valid:
            return None, error_message
        
        # Hash the password
        password_hash = self.security.hash_password(password)
        
        # Insert the user
        user_id = self.db.execute_insert(
            "INSERT INTO users (username, password_hash, full_name, email, role) VALUES (?, ?, ?, ?, ?)",
            (username, password_hash, full_name, email, role)
        )
        
        # Log the creation
        if created_by:
            self.db.log_audit(
                created_by,
                "create",
                "user",
                user_id,
                f"User created: {username}"
            )
        
        return user_id, None
    
    def update_user(self, user_id, full_name=None, email=None, role=None, is_active=None, updated_by=None):
        """
        Update a user.
        
        Args:
            user_id (int): The user ID
            full_name (str, optional): The user's full name
            email (str, optional): The user's email
            role (str, optional): The user's role
            is_active (bool, optional): Whether the user is active
            updated_by (int, optional): ID of the user making the update
            
        Returns:
            tuple: (True, None) if successful, (False, error_message) if failed
        """
        # Check if user exists
        user = self.get_user_by_id(user_id)
        if not user:
            return False, "User not found"
        
        # Check if email already exists
        if email and email != user['email']:
            existing_user = self.get_user_by_email(email)
            if existing_user and existing_user['id'] != user_id:
                return False, "Email already in use"
        
        # Build the update query
        query_parts = []
        params = []
        
        if full_name is not None:
            query_parts.append("full_name = ?")
            params.append(full_name)
        
        if email is not None:
            query_parts.append("email = ?")
            params.append(email)
        
        if role is not None:
            query_parts.append("role = ?")
            params.append(role)
        
        if is_active is not None:
            query_parts.append("is_active = ?")
            params.append(1 if is_active else 0)
        
        if not query_parts:
            return False, "No changes to make"
        
        query = f"UPDATE users SET {', '.join(query_parts)} WHERE id = ?"
        params.append(user_id)
        
        # Execute the update
        self.db.execute_query(query, tuple(params))
        
        # Log the update
        if updated_by:
            self.db.log_audit(
                updated_by,
                "update",
                "user",
                user_id,
                f"User updated: {user['username']}"
            )
        
        return True, None
    
    def change_password(self, user_id, new_password, current_password=None, admin_override=False, updated_by=None):
        """
        Change a user's password.
        
        Args:
            user_id (int): The user ID
            new_password (str): The new password
            current_password (str, optional): The current password (required if not admin_override)
            admin_override (bool, optional): Whether to bypass current password verification
            updated_by (int, optional): ID of the user making the change
            
        Returns:
            tuple: (True, None) if successful, (False, error_message) if failed
        """
        # Get the user
        users = self.db.execute_query(
            "SELECT * FROM users WHERE id = ?",
            (user_id,)
        )
        
        if not users:
            return False, "User not found"
        
        user = users[0]
        
        # Verify current password if not admin override
        if not admin_override and current_password:
            if not self.security.verify_password(current_password, user['password_hash']):
                return False, "Current password is incorrect"
        
        # Check password complexity
        is_valid, error_message = self.security.check_password_complexity(new_password)
        if not is_valid:
            return False, error_message
        
        # Hash the new password
        password_hash = self.security.hash_password(new_password)
        
        # Update the password
        self.db.execute_query(
            "UPDATE users SET password_hash = ?, require_password_change = 0 WHERE id = ?",
            (password_hash, user_id)
        )
        
        # Log the password change
        if updated_by:
            self.db.log_audit(
                updated_by,
                "update",
                "user",
                user_id,
                f"Password changed for user: {user['username']}"
            )
        
        return True, None
    
    def request_password_reset(self, email):
        """
        Request a password reset for a user.
        
        Args:
            email (str): The user's email
            
        Returns:
            tuple: (token, expiry, user) if successful, (None, None, None) if failed
        """
        # Get the user by email
        user = self.get_user_by_email(email)
        if not user:
            return None, None, None
        
        # Generate a password reset token
        token, expiry = self.security.generate_password_reset_token(user['id'])
        
        # Save the token and expiry
        self.db.execute_query(
            "UPDATE users SET password_reset_token = ?, password_reset_expiry = ? WHERE id = ?",
            (token, expiry, user['id'])
        )
        
        # Log the password reset request
        self.db.log_audit(
            user['id'],
            "request",
            "password_reset",
            user['id'],
            f"Password reset requested for user: {user['username']}"
        )
        
        return token, expiry, user
    
    def reset_password(self, token, new_password):
        """
        Reset a user's password using a reset token.
        
        Args:
            token (str): The password reset token
            new_password (str): The new password
            
        Returns:
            tuple: (True, None) if successful, (False, error_message) if failed
        """
        # Find the user with this token
        users = self.db.execute_query(
            "SELECT * FROM users WHERE password_reset_token = ?",
            (token,)
        )
        
        if not users:
            return False, "Invalid or expired token"
        
        user = users[0]
        
        # Check if token is expired
        if not user['password_reset_expiry'] or datetime.fromisoformat(user['password_reset_expiry']) < datetime.now():
            return False, "Token has expired"
        
        # Check password complexity
        is_valid, error_message = self.security.check_password_complexity(new_password)
        if not is_valid:
            return False, error_message
        
        # Hash the new password
        password_hash = self.security.hash_password(new_password)
        
        # Update the password and clear the reset token
        self.db.execute_query(
            "UPDATE users SET password_hash = ?, password_reset_token = NULL, password_reset_expiry = NULL WHERE id = ?",
            (password_hash, user['id'])
        )
        
        # Log the password reset
        self.db.log_audit(
            user['id'],
            "reset",
            "password",
            user['id'],
            f"Password reset completed for user: {user['username']}"
        )
        
        return True, None
    
    def setup_2fa(self, user_id):
        """
        Set up two-factor authentication for a user.
        
        Args:
            user_id (int): The user ID
            
        Returns:
            tuple: (secret, uri) if successful, (None, None) if failed
        """
        # Get the user
        user = self.get_user_by_id(user_id)
        if not user:
            return None, None
        
        # Generate a TOTP secret
        secret = self.security.generate_totp_secret()
        
        # Save the secret
        self.db.execute_query(
            "UPDATE users SET totp_secret = ? WHERE id = ?",
            (secret, user_id)
        )
        
        # Generate the URI for QR code
        uri = self.security.get_totp_uri(secret, user['username'])
        
        return secret, uri
    
    def enable_2fa(self, user_id, token):
        """
        Enable two-factor authentication for a user after verification.
        
        Args:
            user_id (int): The user ID
            token (str): The verification token
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Get the user
        users = self.db.execute_query(
            "SELECT * FROM users WHERE id = ?",
            (user_id,)
        )
        
        if not users:
            return False
        
        user = users[0]
        
        # Verify the token
        if not user['totp_secret'] or not self.security.verify_totp(user['totp_secret'], token):
            return False
        
        # Enable 2FA
        self.db.execute_query(
            "UPDATE users SET totp_enabled = 1 WHERE id = ?",
            (user_id,)
        )
        
        # Log the 2FA enablement
        self.db.log_audit(
            user_id,
            "enable",
            "2fa",
            user_id,
            f"Two-factor authentication enabled for user: {user['username']}"
        )
        
        return True
    
    def disable_2fa(self, user_id, admin_override=False, admin_id=None):
        """
        Disable two-factor authentication for a user.
        
        Args:
            user_id (int): The user ID
            admin_override (bool, optional): Whether this is an admin override
            admin_id (int, optional): ID of the admin making the change
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Get the user
        user = self.get_user_by_id(user_id)
        if not user:
            return False
        
        # Disable 2FA
        self.db.execute_query(
            "UPDATE users SET totp_enabled = 0 WHERE id = ?",
            (user_id,)
        )
        
        # Log the 2FA disablement
        log_user_id = admin_id if admin_override and admin_id else user_id
        action_detail = "by admin" if admin_override else "by user"
        
        self.db.log_audit(
            log_user_id,
            "disable",
            "2fa",
            user_id,
            f"Two-factor authentication disabled for user: {user['username']} {action_detail}"
        )
        
        return True
    
    def delete_user(self, user_id, deleted_by=None):
        """
        Delete a user.
        
        Args:
            user_id (int): The user ID
            deleted_by (int, optional): ID of the user performing the deletion
            
        Returns:
            bool: True if deletion successful, False otherwise
        """
        # Get the user
        user = self.get_user_by_id(user_id)
        if not user:
            return False
        
        # Instead of actually deleting, just set is_active to 0
        self.db.execute_query(
            "UPDATE users SET is_active = 0 WHERE id = ?",
            (user_id,)
        )
        
        # Invalidate all sessions
        self.db.execute_query(
            "UPDATE sessions SET is_active = 0 WHERE user_id = ?",
            (user_id,)
        )
        
        # Log the deletion
        if deleted_by:
            self.db.log_audit(
                deleted_by,
                "delete",
                "user",
                user_id,
                f"User deactivated: {user['username']}"
            )
        
        return True