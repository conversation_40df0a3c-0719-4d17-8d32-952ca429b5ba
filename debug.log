2025-05-23 15:22:14,446 - debug_runner - INFO - Starting application in debug mode
2025-05-23 15:22:14,447 - debug_runner - INFO - Project root: c:\Users\<USER>\Desktop\Science Laboratory Management system
2025-05-23 15:22:14,447 - debug_runner - INFO - Python path: ['c:\\Users\\<USER>\\Desktop\\Science Laboratory Management system', 'c:\\Users\\<USER>\\Desktop\\Science Laboratory Management system', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\PythonCodingPack\\python38.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\PythonCodingPack\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\PythonCodingPack\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\PythonCodingPack', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\PythonCodingPack\\lib\\site-packages', 'c:\\users\\<USER>\\desktop\\cbm', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\PythonCodingPack\\lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\PythonCodingPack\\lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\PythonCodingPack\\lib\\site-packages\\Pythonwin']
2025-05-27 13:33:25,911 - debug_runner - INFO - Starting application in debug mode
2025-05-27 13:33:25,918 - debug_runner - INFO - Project root: C:\Users\<USER>\Desktop\Science Laboratory Management system
2025-05-27 13:33:25,918 - debug_runner - INFO - Python path: ['C:\\Users\\<USER>\\Desktop\\Science Laboratory Management system', 'C:\\Users\\<USER>\\Desktop\\Science Laboratory Management system', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\PythonCodingPack\\python38.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\PythonCodingPack\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\PythonCodingPack\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\PythonCodingPack', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\PythonCodingPack\\lib\\site-packages', 'c:\\users\\<USER>\\desktop\\cbm', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\PythonCodingPack\\lib\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\PythonCodingPack\\lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\PythonCodingPack\\lib\\site-packages\\Pythonwin']
2025-05-27 13:34:09,519 - debug_runner - INFO - Loading configuration from: C:\Users\<USER>\Desktop\Science Laboratory Management system\config\app_config.json
2025-05-27 13:34:09,519 - debug_runner - INFO - Created logs directory: C:\Users\<USER>\Desktop\Science Laboratory Management system\logs
2025-05-27 13:34:09,519 - debug_runner - INFO - Running test app
2025-05-27 13:34:09,529 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-27 13:34:09,536 - flet - INFO - Assets path configured: C:\Users\<USER>\Desktop\Science Laboratory Management system\assets
2025-05-27 13:34:09,536 - flet - DEBUG - Creating new PubSubHub instance
2025-05-27 13:34:09,536 - flet - DEBUG - Creating new PubSubHub instance
2025-05-27 13:34:09,536 - flet - INFO - Starting up TCP server on localhost:5431
2025-05-27 13:34:09,557 - flet - INFO - Flet app has started...
2025-05-27 13:34:09,569 - flet - INFO - App URL: tcp://localhost:5431
2025-05-27 13:34:09,569 - flet_desktop - INFO - Starting Flet View app...
2025-05-27 13:34:09,571 - flet_desktop - INFO - Looking for Flet executable at: C:\Users\<USER>\AppData\Local\Programs\PythonCodingPack\lib\site-packages\flet_desktop\app\flet\flet.exe
2025-05-27 13:34:09,571 - flet_desktop - INFO - Flet View found in: C:\Users\<USER>\AppData\Local\Programs\PythonCodingPack\lib\site-packages\flet_desktop\app\flet\flet.exe
2025-05-27 13:34:10,067 - flet - DEBUG - Connected new TCP client
2025-05-27 13:34:10,073 - flet - DEBUG - _on_message: {"action":"registerWebClient","payload":{"pageName":"","pageRoute":"/","pageWidth":"1265.6","pageHeight":"682.4","windowWidth":"1280.0","windowHeight":"720.0","windowTop":"9.6","windowLeft":"9.6","isPWA":"false","isWeb":"false","isDebug":"false","platform":"windows","platformBrightness":"dark","media":"{\"padding\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0},\"view_padding\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0},\"view_insets\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0}}","sessionId":""}}
2025-05-27 13:34:10,076 - flet - DEBUG - __send: {"action":"registerWebClient","payload":{"session":{"id":"","controls":{"page":{"i":"page","t":"page","p":"","c":[],"route":"/","width":"1265.6","height":"682.4","windowwidth":"1280.0","windowheight":"720.0","windowtop":"9.6","windowleft":"9.6","pwa":"false","web":"false","debug":"false","platform":"windows","platformBrightness":"dark","media":"{\"padding\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0},\"view_padding\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0},\"view_insets\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0}}"}}},"error":"","appInactive":false}}
2025-05-27 13:34:10,080 - flet - DEBUG - _process_command: get ['page', 'route'] {}
2025-05-27 13:34:10,080 - flet - DEBUG - _process_command: get ['page', 'pwa'] {}
2025-05-27 13:34:10,081 - flet - DEBUG - _process_command: get ['page', 'web'] {}
2025-05-27 13:34:10,081 - flet - DEBUG - _process_command: get ['page', 'debug'] {}
2025-05-27 13:34:10,081 - flet - DEBUG - _process_command: get ['page', 'platform'] {}
2025-05-27 13:34:10,081 - flet - DEBUG - _process_command: get ['page', 'platformBrightness'] {}
2025-05-27 13:34:10,083 - flet - DEBUG - _process_command: get ['page', 'media'] {}
2025-05-27 13:34:10,083 - flet - DEBUG - _process_command: get ['page', 'width'] {}
2025-05-27 13:34:10,084 - flet - DEBUG - _process_command: get ['page', 'height'] {}
2025-05-27 13:34:10,084 - flet - DEBUG - _process_command: get ['page', 'windowWidth'] {}
2025-05-27 13:34:10,084 - flet - DEBUG - _process_command: get ['page', 'windowHeight'] {}
2025-05-27 13:34:10,084 - flet - DEBUG - _process_command: get ['page', 'windowTop'] {}
2025-05-27 13:34:10,084 - flet - DEBUG - _process_command: get ['page', 'windowLeft'] {}
2025-05-27 13:34:10,084 - flet - DEBUG - _process_command: get ['page', 'clientIP'] {}
2025-05-27 13:34:10,085 - flet - DEBUG - _process_command: get ['page', 'clientUserAgent'] {}
2025-05-27 13:34:10,086 - flet - INFO - App session started
2025-05-27 13:34:10,142 - flet - DEBUG - _process_command: set ['page'] {'title': 'Science Laboratory Management System - Debug Mode'}
2025-05-27 13:34:10,142 - flet - DEBUG - sent to TCP: 602
2025-05-27 13:34:10,194 - flet - DEBUG - _process_command: add [] {'to': 'page', 'at': '0'}
2025-05-27 13:34:10,195 - flet - DEBUG - _process_command: add [] {'to': 'page', 'at': '1'}
2025-05-27 13:34:10,195 - flet - DEBUG - __send: {"action":"pageControlsBatch","payload":[{"action":"updateControlProps","payload":{"props":[{"i":"page","title":"Science Laboratory Management System - Debug Mode"}]}},{"action":"addPageControls","payload":{"controls":[{"t":"view","i":"_1","p":"page","c":["_2"],"at":"0"},{"t":"text","i":"_2","p":"_1","c":[],"value":"Science Laboratory Management System - Debug Mode"}],"trimIDs":[]}},{"action":"addPageControls","payload":{"controls":[{"t":"offstage","i":"_3","p":"page","c":[],"at":"1"}],"trimIDs":[]}}]}
2025-05-27 13:34:10,203 - flet - DEBUG - sent to TCP: 511
2025-05-27 13:34:10,198 - flet - DEBUG - _process_command: add [] {'to': '_1', 'at': '1'}
2025-05-27 13:34:10,207 - flet - DEBUG - __send: {"action":"pageControlsBatch","payload":[{"action":"addPageControls","payload":{"controls":[{"t":"text","i":"_4","p":"_1","c":[],"at":"1","value":"Project root: C:\\Users\\<USER>\\Desktop\\Science Laboratory Management system"}],"trimIDs":[]}}]}
2025-05-27 13:34:10,208 - flet - DEBUG - _process_command: add [] {'to': '_1', 'at': '2'}
2025-05-27 13:34:10,209 - flet - DEBUG - sent to TCP: 248
2025-05-27 13:34:10,209 - flet - DEBUG - __send: {"action":"pageControlsBatch","payload":[{"action":"addPageControls","payload":{"controls":[{"t":"text","i":"_5","p":"_1","c":[],"at":"2","value":"Configuration file: C:\\Users\\<USER>\\Desktop\\Science Laboratory Management system\\config\\app_config.json"}],"trimIDs":[]}}]}
2025-05-27 13:34:10,212 - flet - DEBUG - _process_command: add [] {'to': '_1', 'at': '3'}
2025-05-27 13:34:10,212 - flet - DEBUG - sent to TCP: 279
2025-05-27 13:34:10,213 - flet - DEBUG - __send: {"action":"pageControlsBatch","payload":[{"action":"addPageControls","payload":{"controls":[{"t":"elevatedbutton","i":"_6","p":"_1","c":[],"at":"3","style":"{\"padding\":{},\"side\":{},\"shape\":{},\"text_style\":{}}","text":"Test Button"}],"trimIDs":[]}}]}
2025-05-27 13:34:10,214 - flet - DEBUG - sent to TCP: 261
2025-05-27 13:34:55,852 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"_6","eventName":"click","eventData":""}}
2025-05-27 13:34:55,852 - flet - DEBUG - page.on_event_async: _6 click 
2025-05-27 13:34:55,854 - flet - DEBUG - _process_command: add [] {'to': '_1', 'at': '4'}
2025-05-27 13:34:55,854 - flet - DEBUG - __send: {"action":"pageControlsBatch","payload":[{"action":"addPageControls","payload":{"controls":[{"t":"text","i":"_7","p":"_1","c":[],"at":"4","value":"Button clicked!"}],"trimIDs":[]}}]}
2025-05-27 13:34:55,854 - flet - DEBUG - sent to TCP: 186
2025-05-27 13:35:00,248 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"app_lifecycle_state_change","eventData":"inactive"}}
2025-05-27 13:35:00,249 - flet - DEBUG - page.on_event_async: page app_lifecycle_state_change inactive
2025-05-27 13:35:00,251 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","windowwidth":"1280.0","windowheight":"720.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"false","windowfullscreen":"false"}]}}
2025-05-27 13:35:00,251 - flet - DEBUG - page.on_event_async: page change [{"i":"page","windowwidth":"1280.0","windowheight":"720.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"false","windowfullscreen":"false"}]
2025-05-27 13:35:00,258 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"window_event","eventData":"blur"}}
2025-05-27 13:35:00,258 - flet - DEBUG - page.on_event_async: page window_event blur
