2025-05-23 17:53:11,688 - simple_app - INFO - Starting simple application
2025-05-23 17:53:11,689 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-23 17:53:11,724 - flet - INFO - Assets path configured: C:\Users\<USER>\Desktop\Science Laboratory Management system\assets
2025-05-23 17:53:11,732 - flet - DEBUG - Creating new PubSubHub instance
2025-05-23 17:53:11,732 - flet - DEBUG - Creating new PubSubHub instance
2025-05-23 17:53:11,733 - flet - INFO - Starting up TCP server on localhost:6661
2025-05-23 17:53:11,755 - flet - INFO - Flet app has started...
2025-05-23 17:53:11,773 - flet - INFO - App URL: tcp://localhost:6661
2025-05-23 17:53:11,773 - flet_desktop - INFO - Starting Flet View app...
2025-05-23 17:53:11,803 - flet_desktop - INFO - Looking for Flet executable at: C:\Users\<USER>\AppData\Local\Programs\PythonCodingPack\lib\site-packages\flet_desktop\app\flet\flet.exe
2025-05-23 17:53:11,804 - flet_desktop - INFO - Flet View found in: C:\Users\<USER>\AppData\Local\Programs\PythonCodingPack\lib\site-packages\flet_desktop\app\flet\flet.exe
2025-05-23 17:53:13,123 - flet - DEBUG - Connected new TCP client
2025-05-23 17:53:13,239 - flet - DEBUG - _on_message: {"action":"registerWebClient","payload":{"pageName":"","pageRoute":"/","pageWidth":"1265.6","pageHeight":"682.4","windowWidth":"1280.0","windowHeight":"720.0","windowTop":"9.6","windowLeft":"9.6","isPWA":"false","isWeb":"false","isDebug":"false","platform":"windows","platformBrightness":"dark","media":"{\"padding\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0},\"view_padding\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0},\"view_insets\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0}}","sessionId":""}}
2025-05-23 17:53:13,240 - flet - DEBUG - __send: {"action":"registerWebClient","payload":{"session":{"id":"","controls":{"page":{"i":"page","t":"page","p":"","c":[],"route":"/","width":"1265.6","height":"682.4","windowwidth":"1280.0","windowheight":"720.0","windowtop":"9.6","windowleft":"9.6","pwa":"false","web":"false","debug":"false","platform":"windows","platformBrightness":"dark","media":"{\"padding\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0},\"view_padding\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0},\"view_insets\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0}}"}}},"error":"","appInactive":false}}
2025-05-23 17:53:13,240 - flet - DEBUG - _process_command: get ['page', 'route'] {}
2025-05-23 17:53:13,240 - flet - DEBUG - _process_command: get ['page', 'pwa'] {}
2025-05-23 17:53:13,242 - flet - DEBUG - _process_command: get ['page', 'web'] {}
2025-05-23 17:53:13,242 - flet - DEBUG - _process_command: get ['page', 'debug'] {}
2025-05-23 17:53:13,242 - flet - DEBUG - _process_command: get ['page', 'platform'] {}
2025-05-23 17:53:13,242 - flet - DEBUG - _process_command: get ['page', 'platformBrightness'] {}
2025-05-23 17:53:13,242 - flet - DEBUG - _process_command: get ['page', 'media'] {}
2025-05-23 17:53:13,242 - flet - DEBUG - _process_command: get ['page', 'width'] {}
2025-05-23 17:53:13,242 - flet - DEBUG - _process_command: get ['page', 'height'] {}
2025-05-23 17:53:13,242 - flet - DEBUG - _process_command: get ['page', 'windowWidth'] {}
2025-05-23 17:53:13,242 - flet - DEBUG - _process_command: get ['page', 'windowHeight'] {}
2025-05-23 17:53:13,242 - flet - DEBUG - _process_command: get ['page', 'windowTop'] {}
2025-05-23 17:53:13,242 - flet - DEBUG - _process_command: get ['page', 'windowLeft'] {}
2025-05-23 17:53:13,242 - flet - DEBUG - _process_command: get ['page', 'clientIP'] {}
2025-05-23 17:53:13,242 - flet - DEBUG - _process_command: get ['page', 'clientUserAgent'] {}
2025-05-23 17:53:13,242 - flet - INFO - App session started
2025-05-23 17:53:13,260 - flet - DEBUG - _process_command: set ['page'] {'theme': '{"color_scheme_seed":"blue","page_transitions":{},"system_overlay_style":{},"visual_density":"comfortable"}', 'thememode': 'light', 'title': 'Simple Science Laboratory Management System', 'windowheight': '600', 'windowminheight': '300', 'windowminwidth': '400', 'windowwidth': '800'}
2025-05-23 17:53:13,261 - flet - DEBUG - sent to TCP: 602
2025-05-23 17:53:13,271 - flet - DEBUG - _process_command: add [] {'to': 'page', 'at': '0'}
2025-05-23 17:53:13,292 - flet - DEBUG - _process_command: add [] {'to': 'page', 'at': '1'}
2025-05-23 17:53:13,296 - flet - DEBUG - __send: {"action":"pageControlsBatch","payload":[{"action":"updateControlProps","payload":{"props":[{"i":"page","theme":"{\"color_scheme_seed\":\"blue\",\"page_transitions\":{},\"system_overlay_style\":{},\"visual_density\":\"comfortable\"}","thememode":"light","title":"Simple Science Laboratory Management System","windowheight":"600","windowminheight":"300","windowminwidth":"400","windowwidth":"800"}]}},{"action":"addPageControls","payload":{"controls":[{"t":"view","i":"_1","p":"page","c":["_2","_3","_4","_5"],"at":"0","padding":"10"},{"t":"text","i":"_2","p":"_1","c":[],"size":"24","value":"Simple Science Laboratory Management System","weight":"bold"},{"t":"divider","i":"_3","p":"_1","c":[]},{"t":"text","i":"_4","p":"_1","c":[],"value":"Current time: 2025-05-23 17:53:13"},{"t":"divider","i":"_5","p":"_1","c":[]}],"trimIDs":[]}},{"action":"addPageControls","payload":{"controls":[{"t":"offstage","i":"_6","p":"page","c":[],"at":"1"}],"trimIDs":[]}}]}
2025-05-23 17:53:13,299 - flet - DEBUG - sent to TCP: 959
2025-05-23 17:53:13,303 - flet - DEBUG - _process_command: add [] {'to': '_1', 'at': '4'}
2025-05-23 17:53:13,303 - flet - DEBUG - __send: {"action":"pageControlsBatch","payload":[{"action":"addPageControls","payload":{"controls":[{"t":"card","i":"_7","p":"_1","c":["_8"],"at":"4"},{"t":"container","i":"_8","p":"_7","c":["_9"],"n":"content","padding":"20"},{"t":"column","i":"_9","p":"_8","c":["_10","_11","_12","_13"],"n":"content","spacing":"10"},{"t":"text","i":"_10","p":"_9","c":[],"size":"20","value":"Login","weight":"bold"},{"t":"textfield","i":"_11","p":"_9","c":[],"autofocus":"true","label":"Username"},{"t":"textfield","i":"_12","p":"_9","c":[],"label":"Password","password":"true"},{"t":"elevatedbutton","i":"_13","p":"_9","c":[],"style":"{\"padding\":{},\"side\":{},\"shape\":{},\"text_style\":{}}","text":"Login"}],"trimIDs":[]}}]}
2025-05-23 17:53:13,311 - flet - DEBUG - sent to TCP: 712
2025-05-23 17:53:13,327 - flet - DEBUG - _process_command: add [] {'to': '_1', 'at': '5'}
2025-05-23 17:53:13,329 - flet - DEBUG - _process_command: add [] {'to': '_1', 'at': '6'}
2025-05-23 17:53:13,334 - flet - DEBUG - __send: {"action":"pageControlsBatch","payload":[{"action":"addPageControls","payload":{"controls":[{"t":"text","i":"_14","p":"_1","c":[],"at":"5","size":"20","value":"Inventory","weight":"bold"}],"trimIDs":[]}},{"action":"addPageControls","payload":{"controls":[{"t":"datatable","i":"_15","p":"_1","c":["_16","_18","_20","_22","_24","_33","_42"],"at":"6"},{"t":"datacolumn","i":"_16","p":"_15","c":["_17"]},{"t":"text","i":"_17","p":"_16","c":[],"n":"label","value":"ID"},{"t":"datacolumn","i":"_18","p":"_15","c":["_19"]},{"t":"text","i":"_19","p":"_18","c":[],"n":"label","value":"Name"},{"t":"datacolumn","i":"_20","p":"_15","c":["_21"]},{"t":"text","i":"_21","p":"_20","c":[],"n":"label","value":"Category"},{"t":"datacolumn","i":"_22","p":"_15","c":["_23"]},{"t":"text","i":"_23","p":"_22","c":[],"n":"label","value":"Quantity"},{"t":"datarow","i":"_24","p":"_15","c":["_25","_27","_29","_31"]},{"t":"datacell","i":"_25","p":"_24","c":["_26"]},{"t":"text","i":"_26","p":"_25","c":[],"value":"1"},{"t":"datacell","i":"_27","p":"_24","c":["_28"]},{"t":"text","i":"_28","p":"_27","c":[],"value":"Microscope"},{"t":"datacell","i":"_29","p":"_24","c":["_30"]},{"t":"text","i":"_30","p":"_29","c":[],"value":"Equipment"},{"t":"datacell","i":"_31","p":"_24","c":["_32"]},{"t":"text","i":"_32","p":"_31","c":[],"value":"5"},{"t":"datarow","i":"_33","p":"_15","c":["_34","_36","_38","_40"]},{"t":"datacell","i":"_34","p":"_33","c":["_35"]},{"t":"text","i":"_35","p":"_34","c":[],"value":"2"},{"t":"datacell","i":"_36","p":"_33","c":["_37"]},{"t":"text","i":"_37","p":"_36","c":[],"value":"Test Tubes"},{"t":"datacell","i":"_38","p":"_33","c":["_39"]},{"t":"text","i":"_39","p":"_38","c":[],"value":"Glassware"},{"t":"datacell","i":"_40","p":"_33","c":["_41"]},{"t":"text","i":"_41","p":"_40","c":[],"value":"100"},{"t":"datarow","i":"_42","p":"_15","c":["_43","_45","_47","_49"]},{"t":"datacell","i":"_43","p":"_42","c":["_44"]},{"t":"text","i":"_44","p":"_43","c":[],"value":"3"},{"t":"datacell","i":"_45","p":"_42","c":["_46"]},{"t":"text","i":"_46","p":"_45","c":[],"value":"Bunsen Burner"},{"t":"datacell","i":"_47","p":"_42","c":["_48"]},{"t":"text","i":"_48","p":"_47","c":[],"value":"Equipment"},{"t":"datacell","i":"_49","p":"_42","c":["_50"]},{"t":"text","i":"_50","p":"_49","c":[],"value":"10"}],"trimIDs":[]}}]}
2025-05-23 17:53:13,335 - flet - DEBUG - sent to TCP: 2315
2025-05-23 17:53:13,455 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"_11","eventName":"focus","eventData":""}}
2025-05-23 17:53:13,456 - flet - DEBUG - page.on_event_async: _11 focus 
2025-05-23 17:53:13,500 - flet - DEBUG - _process_command: add [] {'to': '_1', 'at': '7'}
2025-05-23 17:53:13,500 - flet - DEBUG - _process_command: add [] {'to': '_1', 'at': '8'}
2025-05-23 17:53:13,501 - flet - DEBUG - __send: {"action":"pageControlsBatch","payload":[{"action":"addPageControls","payload":{"controls":[{"t":"text","i":"_51","p":"_1","c":[],"at":"7","size":"20","value":"Inventory Chart","weight":"bold"}],"trimIDs":[]}},{"action":"addPageControls","payload":{"controls":[{"t":"container","i":"_52","p":"_1","c":["_53"],"at":"8","border":"{\"l\":null,\"t\":null,\"r\":null,\"b\":{\"w\":1,\"c\":\"grey400\",\"sa\":null}}","height":"300","padding":"10"},{"t":"barchart","i":"_53","p":"_52","c":["_54","_56","_58","_60","_67"],"border":"{\"l\":{\"w\":3,\"c\":\"grey\",\"sa\":null},\"t\":null,\"r\":null,\"b\":{\"w\":3,\"c\":\"grey\",\"sa\":null}}","expand":"1","horizontalgridlines":"{\"interval\":25,\"color\":\"grey300\",\"width\":1}","interactive":"true","maxy":"100","n":"content","tooltipbgcolor":"grey800"},{"t":"group","i":"_54","p":"_53","c":["_55"],"x":"0"},{"t":"bar_chart_rod","i":"_55","p":"_54","c":[],"color":"blue","fromy":"0","tooltip":"Microscopes","toy":"5","width":"40"},{"t":"group","i":"_56","p":"_53","c":["_57"],"x":"1"},{"t":"bar_chart_rod","i":"_57","p":"_56","c":[],"color":"green","fromy":"0","tooltip":"Test Tubes","toy":"100","width":"40"},{"t":"group","i":"_58","p":"_53","c":["_59"],"x":"2"},{"t":"bar_chart_rod","i":"_59","p":"_58","c":[],"color":"amber","fromy":"0","tooltip":"Bunsen Burners","toy":"10","width":"40"},{"t":"axis","i":"_60","p":"_53","c":["_61","_63","_65"],"n":"l"},{"t":"l","i":"_61","p":"_60","c":["_62"],"n":"l","value":"0"},{"t":"text","i":"_62","p":"_61","c":[],"value":"0"},{"t":"l","i":"_63","p":"_60","c":["_64"],"n":"l","value":"50"},{"t":"text","i":"_64","p":"_63","c":[],"value":"50"},{"t":"l","i":"_65","p":"_60","c":["_66"],"n":"l","value":"100"},{"t":"text","i":"_66","p":"_65","c":[],"value":"100"},{"t":"axis","i":"_67","p":"_53","c":["_68","_70","_72"],"n":"b"},{"t":"l","i":"_68","p":"_67","c":["_69"],"n":"l","value":"0"},{"t":"text","i":"_69","p":"_68","c":[],"value":"Microscopes"},{"t":"l","i":"_70","p":"_67","c":["_71"],"n":"l","value":"1"},{"t":"text","i":"_71","p":"_70","c":[],"value":"Test Tubes"},{"t":"l","i":"_72","p":"_67","c":["_73"],"n":"l","value":"2"},{"t":"text","i":"_73","p":"_72","c":[],"value":"Bunsen Burners"}],"trimIDs":[]}}]}
2025-05-23 17:53:13,508 - flet - DEBUG - _process_command: add [] {'to': '_1', 'at': '9'}
2025-05-23 17:53:13,509 - flet - DEBUG - sent to TCP: 2210
2025-05-23 17:53:13,509 - flet - DEBUG - __send: {"action":"pageControlsBatch","payload":[{"action":"addPageControls","payload":{"controls":[{"t":"container","i":"_74","p":"_1","c":["_75"],"at":"9","bgcolor":"blue50","padding":"10"},{"t":"row","i":"_75","p":"_74","c":["_76","_77","_78"],"n":"content","spacing":"10"},{"t":"text","i":"_76","p":"_75","c":[],"value":"\u00a9 2025 Science Laboratory Management System"},{"t":"container","i":"_77","p":"_75","c":[],"expand":"1"},{"t":"text","i":"_78","p":"_75","c":[],"value":"Version: 1.0.0"}],"trimIDs":[]}}]}
2025-05-23 17:53:13,510 - flet - DEBUG - sent to TCP: 512
2025-05-23 17:53:13,641 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","width":"785.6","height":"562.4","windowwidth":"800.0","windowheight":"600.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"false","windowfullscreen":"false"}]}}
2025-05-23 17:53:13,642 - flet - DEBUG - page.on_event_async: page change [{"i":"page","width":"785.6","height":"562.4","windowwidth":"800.0","windowheight":"600.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"false","windowfullscreen":"false"}]
2025-05-23 17:53:13,642 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"resized","eventData":"{\"width\":785.6,\"height\":562.4}"}}
2025-05-23 17:53:13,642 - flet - DEBUG - page.on_event_async: page resized {"width":785.6,"height":562.4}
2025-05-23 17:53:19,843 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","windowwidth":"800.0","windowheight":"600.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"false","windowfullscreen":"false"}]}}
2025-05-23 17:53:19,844 - flet - DEBUG - page.on_event_async: page change [{"i":"page","windowwidth":"800.0","windowheight":"600.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"false","windowfullscreen":"false"}]
2025-05-23 17:53:19,848 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"window_event","eventData":"blur"}}
2025-05-23 17:53:19,849 - flet - DEBUG - page.on_event_async: page window_event blur
2025-05-23 17:53:44,277 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","windowwidth":"800.0","windowheight":"600.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"true","windowfullscreen":"false"}]}}
2025-05-23 17:53:44,279 - flet - DEBUG - page.on_event_async: page change [{"i":"page","windowwidth":"800.0","windowheight":"600.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"true","windowfullscreen":"false"}]
2025-05-23 17:53:44,290 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"window_event","eventData":"focus"}}
2025-05-23 17:53:44,290 - flet - DEBUG - page.on_event_async: page window_event focus
2025-05-23 17:53:46,706 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","windowwidth":"1550.4","windowheight":"830.4","windowtop":"-7.2","windowleft":"-7.2","windowminimized":"false","windowmaximized":"true","windowfocused":"true","windowfullscreen":"false"}]}}
2025-05-23 17:53:46,707 - flet - DEBUG - page.on_event_async: page change [{"i":"page","windowwidth":"1550.4","windowheight":"830.4","windowtop":"-7.2","windowleft":"-7.2","windowminimized":"false","windowmaximized":"true","windowfocused":"true","windowfullscreen":"false"}]
2025-05-23 17:53:46,707 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"window_event","eventData":"maximize"}}
2025-05-23 17:53:46,709 - flet - DEBUG - page.on_event_async: page window_event maximize
2025-05-23 17:53:46,945 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","width":"1536.0","height":"792.8","windowwidth":"1550.4","windowheight":"830.4","windowtop":"-7.2","windowleft":"-7.2","windowminimized":"false","windowmaximized":"true","windowfocused":"true","windowfullscreen":"false"}]}}
2025-05-23 17:53:46,949 - flet - DEBUG - page.on_event_async: page change [{"i":"page","width":"1536.0","height":"792.8","windowwidth":"1550.4","windowheight":"830.4","windowtop":"-7.2","windowleft":"-7.2","windowminimized":"false","windowmaximized":"true","windowfocused":"true","windowfullscreen":"false"}]
2025-05-23 17:53:46,951 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"resized","eventData":"{\"width\":1536.0,\"height\":792.8}"}}
2025-05-23 17:53:46,951 - flet - DEBUG - page.on_event_async: page resized {"width":1536.0,"height":792.8}
2025-05-23 17:53:52,889 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"_11","eventName":"blur","eventData":""}}
2025-05-23 17:53:52,891 - flet - DEBUG - page.on_event_async: _11 blur 
2025-05-23 17:53:52,932 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"app_lifecycle_state_change","eventData":"inactive"}}
2025-05-23 17:53:52,933 - flet - DEBUG - page.on_event_async: page app_lifecycle_state_change inactive
2025-05-23 17:53:52,934 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","windowwidth":"1550.4","windowheight":"830.4","windowtop":"-7.2","windowleft":"-7.2","windowminimized":"false","windowmaximized":"true","windowfocused":"false","windowfullscreen":"false"}]}}
2025-05-23 17:53:52,934 - flet - DEBUG - page.on_event_async: page change [{"i":"page","windowwidth":"1550.4","windowheight":"830.4","windowtop":"-7.2","windowleft":"-7.2","windowminimized":"false","windowmaximized":"true","windowfocused":"false","windowfullscreen":"false"}]
2025-05-23 17:53:52,940 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"window_event","eventData":"blur"}}
2025-05-23 17:53:52,941 - flet - DEBUG - page.on_event_async: page window_event blur
2025-05-23 17:53:58,918 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"app_lifecycle_state_change","eventData":"resume"}}
2025-05-23 17:53:58,919 - flet - DEBUG - page.on_event_async: page app_lifecycle_state_change resume
2025-05-23 17:53:58,920 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"_11","eventName":"focus","eventData":""}}
2025-05-23 17:53:58,920 - flet - DEBUG - page.on_event_async: _11 focus 
2025-05-23 17:53:58,942 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","windowwidth":"1550.4","windowheight":"830.4","windowtop":"-7.2","windowleft":"-7.2","windowminimized":"false","windowmaximized":"true","windowfocused":"true","windowfullscreen":"false"}]}}
2025-05-23 17:53:58,944 - flet - DEBUG - page.on_event_async: page change [{"i":"page","windowwidth":"1550.4","windowheight":"830.4","windowtop":"-7.2","windowleft":"-7.2","windowminimized":"false","windowmaximized":"true","windowfocused":"true","windowfullscreen":"false"}]
2025-05-23 17:53:58,947 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"window_event","eventData":"focus"}}
2025-05-23 17:53:58,947 - flet - DEBUG - page.on_event_async: page window_event focus
2025-05-23 17:54:00,112 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","windowwidth":"800.0","windowheight":"600.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"true","windowfullscreen":"false"}]}}
2025-05-23 17:54:00,113 - flet - DEBUG - page.on_event_async: page change [{"i":"page","windowwidth":"800.0","windowheight":"600.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"true","windowfullscreen":"false"}]
2025-05-23 17:54:00,113 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"window_event","eventData":"unmaximize"}}
2025-05-23 17:54:00,113 - flet - DEBUG - page.on_event_async: page window_event unmaximize
2025-05-23 17:54:00,348 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","width":"785.6","height":"562.4","windowwidth":"800.0","windowheight":"600.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"true","windowfullscreen":"false"}]}}
2025-05-23 17:54:00,349 - flet - DEBUG - page.on_event_async: page change [{"i":"page","width":"785.6","height":"562.4","windowwidth":"800.0","windowheight":"600.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"true","windowfullscreen":"false"}]
2025-05-23 17:54:00,350 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"resized","eventData":"{\"width\":785.6,\"height\":562.4}"}}
2025-05-23 17:54:00,350 - flet - DEBUG - page.on_event_async: page resized {"width":785.6,"height":562.4}
2025-05-23 17:54:04,231 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"_11","eventName":"blur","eventData":""}}
2025-05-23 17:54:04,232 - flet - DEBUG - page.on_event_async: _11 blur 
2025-05-23 17:54:04,349 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"app_lifecycle_state_change","eventData":"inactive"}}
2025-05-23 17:54:04,349 - flet - DEBUG - page.on_event_async: page app_lifecycle_state_change inactive
2025-05-23 17:54:04,400 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"app_lifecycle_state_change","eventData":"hide"}}
2025-05-23 17:54:04,400 - flet - DEBUG - page.on_event_async: page app_lifecycle_state_change hide
2025-05-23 17:54:06,824 - flet_desktop - DEBUG - Flet View process 24712
2025-05-23 17:54:06,824 - flet - DEBUG - Closing connection...
2025-05-23 17:54:06,825 - flet - DEBUG - Disconnecting all pages...
2025-05-23 17:54:06,825 - flet - DEBUG - page.on_event_async: page disconnect 
2025-05-23 17:54:06,825 - flet - DEBUG - Shutting down thread pool...
2025-05-23 17:57:06,284 - simple_app - INFO - Starting simple application
2025-05-23 17:57:06,286 - asyncio - DEBUG - Using proactor: IocpProactor
2025-05-23 17:57:06,288 - flet - INFO - Assets path configured: C:\Users\<USER>\Desktop\Science Laboratory Management system\assets
2025-05-23 17:57:06,291 - flet - DEBUG - Creating new PubSubHub instance
2025-05-23 17:57:06,293 - flet - DEBUG - Creating new PubSubHub instance
2025-05-23 17:57:06,293 - flet - INFO - Starting up TCP server on localhost:7179
2025-05-23 17:57:06,320 - flet - INFO - Flet app has started...
2025-05-23 17:57:06,338 - flet - INFO - App URL: tcp://localhost:7179
2025-05-23 17:57:06,338 - flet_desktop - INFO - Starting Flet View app...
2025-05-23 17:57:06,343 - flet_desktop - INFO - Looking for Flet executable at: C:\Users\<USER>\AppData\Local\Programs\PythonCodingPack\lib\site-packages\flet_desktop\app\flet\flet.exe
2025-05-23 17:57:06,343 - flet_desktop - INFO - Flet View found in: C:\Users\<USER>\AppData\Local\Programs\PythonCodingPack\lib\site-packages\flet_desktop\app\flet\flet.exe
2025-05-23 17:57:06,818 - flet - DEBUG - Connected new TCP client
2025-05-23 17:57:06,834 - flet - DEBUG - _on_message: {"action":"registerWebClient","payload":{"pageName":"","pageRoute":"/","pageWidth":"1265.6","pageHeight":"682.4","windowWidth":"1280.0","windowHeight":"720.0","windowTop":"9.6","windowLeft":"9.6","isPWA":"false","isWeb":"false","isDebug":"false","platform":"windows","platformBrightness":"dark","media":"{\"padding\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0},\"view_padding\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0},\"view_insets\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0}}","sessionId":""}}
2025-05-23 17:57:06,834 - flet - DEBUG - __send: {"action":"registerWebClient","payload":{"session":{"id":"","controls":{"page":{"i":"page","t":"page","p":"","c":[],"route":"/","width":"1265.6","height":"682.4","windowwidth":"1280.0","windowheight":"720.0","windowtop":"9.6","windowleft":"9.6","pwa":"false","web":"false","debug":"false","platform":"windows","platformBrightness":"dark","media":"{\"padding\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0},\"view_padding\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0},\"view_insets\":{\"top\":0.0,\"right\":0.0,\"bottom\":0.0,\"left\":0.0}}"}}},"error":"","appInactive":false}}
2025-05-23 17:57:06,835 - flet - DEBUG - _process_command: get ['page', 'route'] {}
2025-05-23 17:57:06,836 - flet - DEBUG - _process_command: get ['page', 'pwa'] {}
2025-05-23 17:57:06,836 - flet - DEBUG - _process_command: get ['page', 'web'] {}
2025-05-23 17:57:06,836 - flet - DEBUG - _process_command: get ['page', 'debug'] {}
2025-05-23 17:57:06,836 - flet - DEBUG - _process_command: get ['page', 'platform'] {}
2025-05-23 17:57:06,836 - flet - DEBUG - _process_command: get ['page', 'platformBrightness'] {}
2025-05-23 17:57:06,836 - flet - DEBUG - _process_command: get ['page', 'media'] {}
2025-05-23 17:57:06,836 - flet - DEBUG - _process_command: get ['page', 'width'] {}
2025-05-23 17:57:06,837 - flet - DEBUG - _process_command: get ['page', 'height'] {}
2025-05-23 17:57:06,837 - flet - DEBUG - _process_command: get ['page', 'windowWidth'] {}
2025-05-23 17:57:06,837 - flet - DEBUG - _process_command: get ['page', 'windowHeight'] {}
2025-05-23 17:57:06,837 - flet - DEBUG - _process_command: get ['page', 'windowTop'] {}
2025-05-23 17:57:06,837 - flet - DEBUG - _process_command: get ['page', 'windowLeft'] {}
2025-05-23 17:57:06,837 - flet - DEBUG - _process_command: get ['page', 'clientIP'] {}
2025-05-23 17:57:06,837 - flet - DEBUG - _process_command: get ['page', 'clientUserAgent'] {}
2025-05-23 17:57:06,838 - flet - INFO - App session started
2025-05-23 17:57:06,856 - flet - DEBUG - _process_command: set ['page'] {'theme': '{"color_scheme_seed":"blue","page_transitions":{},"system_overlay_style":{},"visual_density":"comfortable"}', 'thememode': 'light', 'title': 'Simple Science Laboratory Management System', 'windowheight': '600', 'windowminheight': '300', 'windowminwidth': '400', 'windowwidth': '800'}
2025-05-23 17:57:06,857 - flet - DEBUG - _process_command: add [] {'to': 'page', 'at': '0'}
2025-05-23 17:57:06,938 - flet - DEBUG - _process_command: add [] {'to': 'page', 'at': '1'}
2025-05-23 17:57:06,953 - flet - DEBUG - __send: {"action":"pageControlsBatch","payload":[{"action":"updateControlProps","payload":{"props":[{"i":"page","theme":"{\"color_scheme_seed\":\"blue\",\"page_transitions\":{},\"system_overlay_style\":{},\"visual_density\":\"comfortable\"}","thememode":"light","title":"Simple Science Laboratory Management System","windowheight":"600","windowminheight":"300","windowminwidth":"400","windowwidth":"800"}]}},{"action":"addPageControls","payload":{"controls":[{"t":"view","i":"_1","p":"page","c":["_2","_3","_4","_5"],"at":"0","padding":"10"},{"t":"text","i":"_2","p":"_1","c":[],"size":"24","value":"Simple Science Laboratory Management System","weight":"bold"},{"t":"divider","i":"_3","p":"_1","c":[]},{"t":"text","i":"_4","p":"_1","c":[],"value":"Current time: 2025-05-23 17:57:06"},{"t":"divider","i":"_5","p":"_1","c":[]}],"trimIDs":[]}},{"action":"addPageControls","payload":{"controls":[{"t":"offstage","i":"_6","p":"page","c":[],"at":"1"}],"trimIDs":[]}}]}
2025-05-23 17:57:07,004 - flet - DEBUG - _process_command: add [] {'to': '_1', 'at': '4'}
2025-05-23 17:57:07,004 - flet - DEBUG - sent to TCP: 602
2025-05-23 17:57:07,005 - flet - DEBUG - __send: {"action":"pageControlsBatch","payload":[{"action":"addPageControls","payload":{"controls":[{"t":"card","i":"_7","p":"_1","c":["_8"],"at":"4"},{"t":"container","i":"_8","p":"_7","c":["_9"],"n":"content","padding":"20"},{"t":"column","i":"_9","p":"_8","c":["_10","_11","_12","_13"],"n":"content","spacing":"10"},{"t":"text","i":"_10","p":"_9","c":[],"size":"20","value":"Login","weight":"bold"},{"t":"textfield","i":"_11","p":"_9","c":[],"autofocus":"true","label":"Username"},{"t":"textfield","i":"_12","p":"_9","c":[],"label":"Password","password":"true"},{"t":"elevatedbutton","i":"_13","p":"_9","c":[],"style":"{\"padding\":{},\"side\":{},\"shape\":{},\"text_style\":{}}","text":"Login"}],"trimIDs":[]}}]}
2025-05-23 17:57:07,006 - flet - DEBUG - sent to TCP: 959
2025-05-23 17:57:07,087 - flet - DEBUG - _process_command: add [] {'to': '_1', 'at': '5'}
2025-05-23 17:57:07,104 - flet - DEBUG - sent to TCP: 712
2025-05-23 17:57:07,104 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"_11","eventName":"focus","eventData":""}}
2025-05-23 17:57:07,104 - flet - DEBUG - page.on_event_async: _11 focus 
2025-05-23 17:57:07,128 - flet - DEBUG - _process_command: add [] {'to': '_1', 'at': '6'}
2025-05-23 17:57:07,130 - flet - DEBUG - __send: {"action":"pageControlsBatch","payload":[{"action":"addPageControls","payload":{"controls":[{"t":"text","i":"_14","p":"_1","c":[],"at":"5","size":"20","value":"Inventory","weight":"bold"}],"trimIDs":[]}},{"action":"addPageControls","payload":{"controls":[{"t":"datatable","i":"_15","p":"_1","c":["_16","_18","_20","_22","_24","_33","_42"],"at":"6"},{"t":"datacolumn","i":"_16","p":"_15","c":["_17"]},{"t":"text","i":"_17","p":"_16","c":[],"n":"label","value":"ID"},{"t":"datacolumn","i":"_18","p":"_15","c":["_19"]},{"t":"text","i":"_19","p":"_18","c":[],"n":"label","value":"Name"},{"t":"datacolumn","i":"_20","p":"_15","c":["_21"]},{"t":"text","i":"_21","p":"_20","c":[],"n":"label","value":"Category"},{"t":"datacolumn","i":"_22","p":"_15","c":["_23"]},{"t":"text","i":"_23","p":"_22","c":[],"n":"label","value":"Quantity"},{"t":"datarow","i":"_24","p":"_15","c":["_25","_27","_29","_31"]},{"t":"datacell","i":"_25","p":"_24","c":["_26"]},{"t":"text","i":"_26","p":"_25","c":[],"value":"1"},{"t":"datacell","i":"_27","p":"_24","c":["_28"]},{"t":"text","i":"_28","p":"_27","c":[],"value":"Microscope"},{"t":"datacell","i":"_29","p":"_24","c":["_30"]},{"t":"text","i":"_30","p":"_29","c":[],"value":"Equipment"},{"t":"datacell","i":"_31","p":"_24","c":["_32"]},{"t":"text","i":"_32","p":"_31","c":[],"value":"5"},{"t":"datarow","i":"_33","p":"_15","c":["_34","_36","_38","_40"]},{"t":"datacell","i":"_34","p":"_33","c":["_35"]},{"t":"text","i":"_35","p":"_34","c":[],"value":"2"},{"t":"datacell","i":"_36","p":"_33","c":["_37"]},{"t":"text","i":"_37","p":"_36","c":[],"value":"Test Tubes"},{"t":"datacell","i":"_38","p":"_33","c":["_39"]},{"t":"text","i":"_39","p":"_38","c":[],"value":"Glassware"},{"t":"datacell","i":"_40","p":"_33","c":["_41"]},{"t":"text","i":"_41","p":"_40","c":[],"value":"100"},{"t":"datarow","i":"_42","p":"_15","c":["_43","_45","_47","_49"]},{"t":"datacell","i":"_43","p":"_42","c":["_44"]},{"t":"text","i":"_44","p":"_43","c":[],"value":"3"},{"t":"datacell","i":"_45","p":"_42","c":["_46"]},{"t":"text","i":"_46","p":"_45","c":[],"value":"Bunsen Burner"},{"t":"datacell","i":"_47","p":"_42","c":["_48"]},{"t":"text","i":"_48","p":"_47","c":[],"value":"Equipment"},{"t":"datacell","i":"_49","p":"_42","c":["_50"]},{"t":"text","i":"_50","p":"_49","c":[],"value":"10"}],"trimIDs":[]}}]}
2025-05-23 17:57:07,156 - flet - DEBUG - _process_command: add [] {'to': '_1', 'at': '7'}
2025-05-23 17:57:07,156 - flet - DEBUG - sent to TCP: 2315
2025-05-23 17:57:07,166 - flet - DEBUG - _process_command: add [] {'to': '_1', 'at': '8'}
2025-05-23 17:57:07,167 - flet - DEBUG - __send: {"action":"pageControlsBatch","payload":[{"action":"addPageControls","payload":{"controls":[{"t":"text","i":"_51","p":"_1","c":[],"at":"7","size":"20","value":"Inventory Chart","weight":"bold"}],"trimIDs":[]}},{"action":"addPageControls","payload":{"controls":[{"t":"container","i":"_52","p":"_1","c":["_53"],"at":"8","border":"{\"l\":null,\"t\":null,\"r\":null,\"b\":{\"w\":1,\"c\":\"grey400\",\"sa\":null}}","height":"300","padding":"10"},{"t":"barchart","i":"_53","p":"_52","c":["_54","_56","_58","_60","_67"],"border":"{\"l\":{\"w\":3,\"c\":\"grey\",\"sa\":null},\"t\":null,\"r\":null,\"b\":{\"w\":3,\"c\":\"grey\",\"sa\":null}}","expand":"1","horizontalgridlines":"{\"interval\":25,\"color\":\"grey300\",\"width\":1}","interactive":"true","maxy":"100","n":"content","tooltipbgcolor":"grey800"},{"t":"group","i":"_54","p":"_53","c":["_55"],"x":"0"},{"t":"bar_chart_rod","i":"_55","p":"_54","c":[],"color":"blue","fromy":"0","tooltip":"Microscopes","toy":"5","width":"40"},{"t":"group","i":"_56","p":"_53","c":["_57"],"x":"1"},{"t":"bar_chart_rod","i":"_57","p":"_56","c":[],"color":"green","fromy":"0","tooltip":"Test Tubes","toy":"100","width":"40"},{"t":"group","i":"_58","p":"_53","c":["_59"],"x":"2"},{"t":"bar_chart_rod","i":"_59","p":"_58","c":[],"color":"amber","fromy":"0","tooltip":"Bunsen Burners","toy":"10","width":"40"},{"t":"axis","i":"_60","p":"_53","c":["_61","_63","_65"],"n":"l"},{"t":"l","i":"_61","p":"_60","c":["_62"],"n":"l","value":"0"},{"t":"text","i":"_62","p":"_61","c":[],"value":"0"},{"t":"l","i":"_63","p":"_60","c":["_64"],"n":"l","value":"50"},{"t":"text","i":"_64","p":"_63","c":[],"value":"50"},{"t":"l","i":"_65","p":"_60","c":["_66"],"n":"l","value":"100"},{"t":"text","i":"_66","p":"_65","c":[],"value":"100"},{"t":"axis","i":"_67","p":"_53","c":["_68","_70","_72"],"n":"b"},{"t":"l","i":"_68","p":"_67","c":["_69"],"n":"l","value":"0"},{"t":"text","i":"_69","p":"_68","c":[],"value":"Microscopes"},{"t":"l","i":"_70","p":"_67","c":["_71"],"n":"l","value":"1"},{"t":"text","i":"_71","p":"_70","c":[],"value":"Test Tubes"},{"t":"l","i":"_72","p":"_67","c":["_73"],"n":"l","value":"2"},{"t":"text","i":"_73","p":"_72","c":[],"value":"Bunsen Burners"}],"trimIDs":[]}}]}
2025-05-23 17:57:07,167 - flet - DEBUG - sent to TCP: 2210
2025-05-23 17:57:07,187 - flet - DEBUG - _process_command: add [] {'to': '_1', 'at': '9'}
2025-05-23 17:57:07,187 - flet - DEBUG - __send: {"action":"pageControlsBatch","payload":[{"action":"addPageControls","payload":{"controls":[{"t":"container","i":"_74","p":"_1","c":["_75"],"at":"9","bgcolor":"blue50","padding":"10"},{"t":"row","i":"_75","p":"_74","c":["_76","_77","_78"],"n":"content","spacing":"10"},{"t":"text","i":"_76","p":"_75","c":[],"value":"\u00a9 2025 Science Laboratory Management System"},{"t":"container","i":"_77","p":"_75","c":[],"expand":"1"},{"t":"text","i":"_78","p":"_75","c":[],"value":"Version: 1.0.0"}],"trimIDs":[]}}]}
2025-05-23 17:57:07,196 - flet - DEBUG - sent to TCP: 512
2025-05-23 17:57:07,334 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","width":"785.6","height":"562.4","windowwidth":"800.0","windowheight":"600.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"false","windowfullscreen":"false"}]}}
2025-05-23 17:57:07,335 - flet - DEBUG - page.on_event_async: page change [{"i":"page","width":"785.6","height":"562.4","windowwidth":"800.0","windowheight":"600.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"false","windowfullscreen":"false"}]
2025-05-23 17:57:07,338 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"resized","eventData":"{\"width\":785.6,\"height\":562.4}"}}
2025-05-23 17:57:07,338 - flet - DEBUG - page.on_event_async: page resized {"width":785.6,"height":562.4}
2025-05-23 17:57:09,448 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","windowwidth":"800.0","windowheight":"600.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"true","windowfullscreen":"false"}]}}
2025-05-23 17:57:09,448 - flet - DEBUG - page.on_event_async: page change [{"i":"page","windowwidth":"800.0","windowheight":"600.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"true","windowfullscreen":"false"}]
2025-05-23 17:57:09,448 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"window_event","eventData":"focus"}}
2025-05-23 17:57:09,449 - flet - DEBUG - page.on_event_async: page window_event focus
2025-05-23 17:57:09,934 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","windowwidth":"800.0","windowheight":"600.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"true","windowfullscreen":"false"}]}}
2025-05-23 17:57:09,935 - flet - DEBUG - page.on_event_async: page change [{"i":"page","windowwidth":"800.0","windowheight":"600.0","windowtop":"9.6","windowleft":"9.6","windowminimized":"false","windowmaximized":"false","windowfocused":"true","windowfullscreen":"false"}]
2025-05-23 17:57:09,935 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"window_event","eventData":"focus"}}
2025-05-23 17:57:09,935 - flet - DEBUG - page.on_event_async: page window_event focus
2025-05-23 17:57:14,110 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","windowwidth":"1550.4","windowheight":"830.4","windowtop":"-7.2","windowleft":"-7.2","windowminimized":"false","windowmaximized":"true","windowfocused":"true","windowfullscreen":"false"}]}}
2025-05-23 17:57:14,111 - flet - DEBUG - page.on_event_async: page change [{"i":"page","windowwidth":"1550.4","windowheight":"830.4","windowtop":"-7.2","windowleft":"-7.2","windowminimized":"false","windowmaximized":"true","windowfocused":"true","windowfullscreen":"false"}]
2025-05-23 17:57:14,111 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"window_event","eventData":"maximize"}}
2025-05-23 17:57:14,112 - flet - DEBUG - page.on_event_async: page window_event maximize
2025-05-23 17:57:14,332 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","width":"1536.0","height":"792.8","windowwidth":"1550.4","windowheight":"830.4","windowtop":"-7.2","windowleft":"-7.2","windowminimized":"false","windowmaximized":"true","windowfocused":"true","windowfullscreen":"false"}]}}
2025-05-23 17:57:14,332 - flet - DEBUG - page.on_event_async: page change [{"i":"page","width":"1536.0","height":"792.8","windowwidth":"1550.4","windowheight":"830.4","windowtop":"-7.2","windowleft":"-7.2","windowminimized":"false","windowmaximized":"true","windowfocused":"true","windowfullscreen":"false"}]
2025-05-23 17:57:14,333 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"resized","eventData":"{\"width\":1536.0,\"height\":792.8}"}}
2025-05-23 17:57:14,337 - flet - DEBUG - page.on_event_async: page resized {"width":1536.0,"height":792.8}
2025-05-23 17:57:20,694 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"_11","eventName":"click","eventData":""}}
2025-05-23 17:57:20,695 - flet - DEBUG - page.on_event_async: _11 click 
2025-05-23 17:57:22,210 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"_11","value":"a"}]}}
2025-05-23 17:57:22,211 - flet - DEBUG - page.on_event_async: page change [{"i":"_11","value":"a"}]
2025-05-23 17:57:22,652 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"_11","value":"ad"}]}}
2025-05-23 17:57:22,652 - flet - DEBUG - page.on_event_async: page change [{"i":"_11","value":"ad"}]
2025-05-23 17:57:23,393 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"_11","value":"adm"}]}}
2025-05-23 17:57:23,393 - flet - DEBUG - page.on_event_async: page change [{"i":"_11","value":"adm"}]
2025-05-23 17:57:23,706 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"_11","value":"admi"}]}}
2025-05-23 17:57:23,706 - flet - DEBUG - page.on_event_async: page change [{"i":"_11","value":"admi"}]
2025-05-23 17:57:23,991 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"_11","value":"admin"}]}}
2025-05-23 17:57:23,992 - flet - DEBUG - page.on_event_async: page change [{"i":"_11","value":"admin"}]
2025-05-23 17:57:25,146 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"_11","eventName":"blur","eventData":""}}
2025-05-23 17:57:25,146 - flet - DEBUG - page.on_event_async: _11 blur 
2025-05-23 17:57:25,146 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"_12","eventName":"focus","eventData":""}}
2025-05-23 17:57:25,147 - flet - DEBUG - page.on_event_async: _12 focus 
2025-05-23 17:57:26,055 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"_12","value":"a"}]}}
2025-05-23 17:57:26,056 - flet - DEBUG - page.on_event_async: page change [{"i":"_12","value":"a"}]
2025-05-23 17:57:26,411 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"_12","value":"ad"}]}}
2025-05-23 17:57:26,411 - flet - DEBUG - page.on_event_async: page change [{"i":"_12","value":"ad"}]
2025-05-23 17:57:26,979 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"_12","value":"adm"}]}}
2025-05-23 17:57:26,979 - flet - DEBUG - page.on_event_async: page change [{"i":"_12","value":"adm"}]
2025-05-23 17:57:28,740 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"_12","value":"admi"}]}}
2025-05-23 17:57:28,740 - flet - DEBUG - page.on_event_async: page change [{"i":"_12","value":"admi"}]
2025-05-23 17:57:29,023 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"_12","value":"admin"}]}}
2025-05-23 17:57:29,023 - flet - DEBUG - page.on_event_async: page change [{"i":"_12","value":"admin"}]
2025-05-23 17:57:31,606 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"_12","eventName":"blur","eventData":""}}
2025-05-23 17:57:31,606 - flet - DEBUG - page.on_event_async: _12 blur 
2025-05-23 17:57:31,748 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"_13","eventName":"click","eventData":""}}
2025-05-23 17:57:31,748 - flet - DEBUG - page.on_event_async: _13 click 
2025-05-23 17:57:31,757 - flet - DEBUG - _process_command: add [] {'to': '_1', 'at': '10'}
2025-05-23 17:57:31,758 - flet - DEBUG - __send: {"action":"pageControlsBatch","payload":[{"action":"addPageControls","payload":{"controls":[{"t":"text","i":"_79","p":"_1","c":[],"at":"10","value":"Login successful for admin"}],"trimIDs":[]}}]}
2025-05-23 17:57:31,767 - flet - DEBUG - sent to TCP: 199
2025-05-23 17:57:37,115 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"app_lifecycle_state_change","eventData":"inactive"}}
2025-05-23 17:57:37,115 - flet - DEBUG - page.on_event_async: page app_lifecycle_state_change inactive
2025-05-23 17:57:37,212 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","windowwidth":"159.20000000000073","windowheight":"27.200000000000728","windowtop":"-25600.0","windowleft":"-25600.0","windowminimized":"true","windowmaximized":"false","windowfocused":"false","windowfullscreen":"false"}]}}
2025-05-23 17:57:37,213 - flet - DEBUG - page.on_event_async: page change [{"i":"page","windowwidth":"159.20000000000073","windowheight":"27.200000000000728","windowtop":"-25600.0","windowleft":"-25600.0","windowminimized":"true","windowmaximized":"false","windowfocused":"false","windowfullscreen":"false"}]
2025-05-23 17:57:37,234 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"window_event","eventData":"minimize"}}
2025-05-23 17:57:37,235 - flet - DEBUG - page.on_event_async: page window_event minimize
2025-05-23 17:57:37,242 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","windowwidth":"159.20000000000073","windowheight":"27.200000000000728","windowtop":"-25600.0","windowleft":"-25600.0","windowminimized":"true","windowmaximized":"false","windowfocused":"false","windowfullscreen":"false"}]}}
2025-05-23 17:57:37,243 - flet - DEBUG - page.on_event_async: page change [{"i":"page","windowwidth":"159.20000000000073","windowheight":"27.200000000000728","windowtop":"-25600.0","windowleft":"-25600.0","windowminimized":"true","windowmaximized":"false","windowfocused":"false","windowfullscreen":"false"}]
2025-05-23 17:57:37,243 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"window_event","eventData":"blur"}}
2025-05-23 17:57:37,244 - flet - DEBUG - page.on_event_async: page window_event blur
2025-05-23 17:57:37,244 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","windowwidth":"159.20000000000073","windowheight":"27.200000000000728","windowtop":"-25600.0","windowleft":"-25600.0","windowminimized":"true","windowmaximized":"false","windowfocused":"false","windowfullscreen":"false"}]}}
2025-05-23 17:57:37,245 - flet - DEBUG - page.on_event_async: page change [{"i":"page","windowwidth":"159.20000000000073","windowheight":"27.200000000000728","windowtop":"-25600.0","windowleft":"-25600.0","windowminimized":"true","windowmaximized":"false","windowfocused":"false","windowfullscreen":"false"}]
2025-05-23 17:57:37,246 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"window_event","eventData":"focus"}}
2025-05-23 17:57:37,246 - flet - DEBUG - page.on_event_async: page window_event focus
2025-05-23 17:57:51,513 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"app_lifecycle_state_change","eventData":"resume"}}
2025-05-23 17:57:51,513 - flet - DEBUG - page.on_event_async: page app_lifecycle_state_change resume
2025-05-23 17:57:51,515 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","windowwidth":"1550.4","windowheight":"830.4","windowtop":"-7.2","windowleft":"-7.2","windowminimized":"true","windowmaximized":"false","windowfocused":"true","windowfullscreen":"false"}]}}
2025-05-23 17:57:51,516 - flet - DEBUG - page.on_event_async: page change [{"i":"page","windowwidth":"1550.4","windowheight":"830.4","windowtop":"-7.2","windowleft":"-7.2","windowminimized":"true","windowmaximized":"false","windowfocused":"true","windowfullscreen":"false"}]
2025-05-23 17:57:51,517 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"window_event","eventData":"focus"}}
2025-05-23 17:57:51,517 - flet - DEBUG - page.on_event_async: page window_event focus
2025-05-23 17:57:51,548 - flet - DEBUG - _on_message: {"action":"updateControlProps","payload":{"props":[{"i":"page","windowwidth":"1550.4","windowheight":"830.4","windowtop":"-7.2","windowleft":"-7.2","windowminimized":"false","windowmaximized":"true","windowfocused":"true","windowfullscreen":"false"}]}}
2025-05-23 17:57:51,549 - flet - DEBUG - page.on_event_async: page change [{"i":"page","windowwidth":"1550.4","windowheight":"830.4","windowtop":"-7.2","windowleft":"-7.2","windowminimized":"false","windowmaximized":"true","windowfocused":"true","windowfullscreen":"false"}]
2025-05-23 17:57:51,554 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"window_event","eventData":"maximize"}}
2025-05-23 17:57:51,555 - flet - DEBUG - page.on_event_async: page window_event maximize
2025-05-23 17:57:53,690 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"app_lifecycle_state_change","eventData":"inactive"}}
2025-05-23 17:57:53,691 - flet - DEBUG - page.on_event_async: page app_lifecycle_state_change inactive
2025-05-23 17:57:53,724 - flet - DEBUG - _on_message: {"action":"pageEventFromWeb","payload":{"eventTarget":"page","eventName":"app_lifecycle_state_change","eventData":"hide"}}
2025-05-23 17:57:53,725 - flet - DEBUG - page.on_event_async: page app_lifecycle_state_change hide
2025-05-23 17:57:54,015 - flet_desktop - DEBUG - Flet View process 9908
2025-05-23 17:57:54,015 - flet - DEBUG - Closing connection...
2025-05-23 17:57:54,015 - flet - DEBUG - Disconnecting all pages...
2025-05-23 17:57:54,015 - flet - DEBUG - page.on_event_async: page disconnect 
2025-05-23 17:57:54,017 - flet - DEBUG - Shutting down thread pool...
