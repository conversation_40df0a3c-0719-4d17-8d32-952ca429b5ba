#!/usr/bin/env python
"""
<PERSON>ript to migrate data from lab_system.db and other databases to the consolidated lab_db.db.
"""
import os
import os.path
import sys
import sqlite3
import logging
import shutil
from datetime import datetime

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(project_root, 'migration.log'))
    ]
)

logger = logging.getLogger('migration')

def backup_database(db_path):
    """
    Create a backup of a database file.
    
    Args:
        db_path (str): Path to the database file
        
    Returns:
        str: Path to the backup file
    """
    if not os.path.exists(db_path):
        logger.warning(f"Database file not found: {db_path}")
        return None
    
    # Create backup directory if it doesn't exist
    backup_dir = os.path.join(project_root, 'backups')
    os.makedirs(backup_dir, exist_ok=True)
    
    # Generate backup filename
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    backup_file = os.path.join(backup_dir, f"{os.path.basename(db_path)}.{timestamp}.bak")
    
    # Copy database file to backup
    shutil.copy2(db_path, backup_file)
    
    logger.info(f"Created backup: {backup_file}")
    
    return backup_file

def get_table_schema(conn, table_name):
    """
    Get the schema of a table.
    
    Args:
        conn (sqlite3.Connection): Database connection
        table_name (str): Name of the table
        
    Returns:
        str: SQL statement to create the table
    """
    cursor = conn.cursor()
    cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}'")
    result = cursor.fetchone()
    
    if result:
        return result[0]
    
    return None

def get_table_data(conn, table_name):
    """
    Get all data from a table.
    
    Args:
        conn (sqlite3.Connection): Database connection
        table_name (str): Name of the table
        
    Returns:
        list: List of rows in the table
    """
    cursor = conn.cursor()
    cursor.execute(f"SELECT * FROM {table_name}")
    return cursor.fetchall()

def get_table_column_names(conn, table_name):
    """
    Get the column names of a table.
    
    Args:
        conn (sqlite3.Connection): Database connection
        table_name (str): Name of the table
        
    Returns:
        list: List of column names
    """
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA table_info({table_name})")
    return [row[1] for row in cursor.fetchall()]

def get_tables(conn):
    """
    Get all tables in a database.
    
    Args:
        conn (sqlite3.Connection): Database connection
        
    Returns:
        list: List of table names
    """
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
    return [row[0] for row in cursor.fetchall()]

def migrate_database(source_db_path, target_db_path):
    """
    Migrate data from source database to target database.
    
    Args:
        source_db_path (str): Path to the source database
        target_db_path (str): Path to the target database
        
    Returns:
        bool: True if successful, False otherwise
    """
    if not os.path.exists(source_db_path):
        logger.warning(f"Source database not found: {source_db_path}")
        return False
    
    try:
        # Connect to source database
        source_conn = sqlite3.connect(source_db_path)
        source_conn.row_factory = sqlite3.Row
        
        # Connect to target database
        target_conn = sqlite3.connect(target_db_path)
        target_conn.row_factory = sqlite3.Row
        
        # Get all tables in source database
        tables = get_tables(source_conn)
        
        for table_name in tables:
            logger.info(f"Migrating table: {table_name}")
            
            # Get table schema
            schema = get_table_schema(source_conn, table_name)
            
            # Create table in target database if it doesn't exist
            target_cursor = target_conn.cursor()
            target_cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            if not target_cursor.fetchone():
                logger.info(f"Creating table: {table_name}")
                target_cursor.execute(schema)
            else:
                logger.info(f"Table already exists: {table_name}")
            
            # Get table data
            data = get_table_data(source_conn, table_name)
            
            if data:
                # Get column names
                column_names = get_table_column_names(source_conn, table_name)
                
                # Generate placeholders for INSERT statement
                placeholders = ', '.join(['?' for _ in column_names])
                
                # Insert data into target database
                logger.info(f"Inserting {len(data)} rows into {table_name}")
                
                for row in data:
                    try:
                        target_cursor.execute(f"INSERT OR IGNORE INTO {table_name} VALUES ({placeholders})", row)
                    except sqlite3.Error as e:
                        logger.error(f"Error inserting row into {table_name}: {str(e)}")
            
            # Commit changes
            target_conn.commit()
        
        logger.info(f"Migration from {source_db_path} to {target_db_path} completed successfully")
        
        # Close connections
        source_conn.close()
        target_conn.close()
        
        return True
    
    except sqlite3.Error as e:
        logger.error(f"Database error: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Error migrating database: {str(e)}")
        return False

def main():
    """
    Main function to migrate databases.
    """
    logger.info("Starting database migration")
    
    # Define database paths
    data_dir = os.path.join(project_root, 'data')
    lab_system_db = os.path.join(data_dir, 'lab_system.db')
    lab_db = os.path.join(data_dir, 'lab_db.db')
    
    # Create data directory if it doesn't exist
    os.makedirs(data_dir, exist_ok=True)
    
    # Check if source database exists
    if not os.path.exists(lab_system_db):
        logger.warning(f"Source database not found: {lab_system_db}")
        
        # Initialize the new database using the DatabaseManager
        from src.database.db_manager import DatabaseManager
        db_manager = DatabaseManager(db_path=lab_db)
        if db_manager.initialize_database():
            logger.info(f"Initialized new database: {lab_db}")
        else:
            logger.error(f"Failed to initialize new database: {lab_db}")
            return
    else:
        # Backup source database
        backup_database(lab_system_db)
        
        # Migrate data from lab_system.db to lab_db.db
        if migrate_database(lab_system_db, lab_db):
            logger.info(f"Migration from {lab_system_db} to {lab_db} completed successfully")
        else:
            logger.error(f"Migration from {lab_system_db} to {lab_db} failed")
    
    # Check for other database files
    for filename in os.listdir(data_dir):
        if filename.endswith('.db') and filename != 'lab_db.db' and filename != 'lab_system.db':
            source_db = os.path.join(data_dir, filename)
            
            # Backup source database
            backup_database(source_db)
            
            # Migrate data
            if migrate_database(source_db, lab_db):
                logger.info(f"Migration from {source_db} to {lab_db} completed successfully")
            else:
                logger.error(f"Migration from {source_db} to {lab_db} failed")
    
    logger.info("Database migration completed")

if __name__ == "__main__":
    main()