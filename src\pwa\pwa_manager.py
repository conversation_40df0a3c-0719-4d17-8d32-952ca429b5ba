"""
Progressive Web App (PWA) Manager for Science Laboratory Management System
Provides offline support, app-like experience, and installation capabilities
"""
import json
import os
from typing import Dict, Any, List


class PWAManager:
    """
    Manages Progressive Web App features including manifest, service worker,
    and offline capabilities.
    """

    def __init__(self, app_name: str = "Lab Management System"):
        """
        Initialize PWA manager.

        Args:
            app_name: Name of the application
        """
        self.app_name = app_name
        self.manifest_path = "static/manifest.json"
        self.service_worker_path = "static/sw.js"
        self.offline_pages = []

    def generate_manifest(self) -> Dict[str, Any]:
        """
        Generate PWA manifest file.

        Returns:
            Manifest configuration dictionary
        """
        manifest = {
            "name": "Science Laboratory Management System",
            "short_name": "Lab Manager",
            "description": "Complete laboratory management solution for inventory, experiments, and scheduling",
            "start_url": "/",
            "display": "standalone",
            "background_color": "#1976d2",
            "theme_color": "#1976d2",
            "orientation": "portrait-primary",
            "scope": "/",
            "lang": "en",
            "categories": ["productivity", "education", "science"],
            "icons": [
                {
                    "src": "/static/icons/icon-72x72.png",
                    "sizes": "72x72",
                    "type": "image/png",
                    "purpose": "maskable any"
                },
                {
                    "src": "/static/icons/icon-96x96.png",
                    "sizes": "96x96",
                    "type": "image/png",
                    "purpose": "maskable any"
                },
                {
                    "src": "/static/icons/icon-128x128.png",
                    "sizes": "128x128",
                    "type": "image/png",
                    "purpose": "maskable any"
                },
                {
                    "src": "/static/icons/icon-144x144.png",
                    "sizes": "144x144",
                    "type": "image/png",
                    "purpose": "maskable any"
                },
                {
                    "src": "/static/icons/icon-152x152.png",
                    "sizes": "152x152",
                    "type": "image/png",
                    "purpose": "maskable any"
                },
                {
                    "src": "/static/icons/icon-192x192.png",
                    "sizes": "192x192",
                    "type": "image/png",
                    "purpose": "maskable any"
                },
                {
                    "src": "/static/icons/icon-384x384.png",
                    "sizes": "384x384",
                    "type": "image/png",
                    "purpose": "maskable any"
                },
                {
                    "src": "/static/icons/icon-512x512.png",
                    "sizes": "512x512",
                    "type": "image/png",
                    "purpose": "maskable any"
                }
            ],
            "screenshots": [
                {
                    "src": "/static/screenshots/desktop-dashboard.png",
                    "sizes": "1280x720",
                    "type": "image/png",
                    "form_factor": "wide",
                    "label": "Dashboard view on desktop"
                },
                {
                    "src": "/static/screenshots/mobile-inventory.png",
                    "sizes": "390x844",
                    "type": "image/png",
                    "form_factor": "narrow",
                    "label": "Inventory management on mobile"
                }
            ],
            "shortcuts": [
                {
                    "name": "Dashboard",
                    "short_name": "Dashboard",
                    "description": "View system dashboard",
                    "url": "/dashboard",
                    "icons": [
                        {
                            "src": "/static/icons/dashboard-96x96.png",
                            "sizes": "96x96"
                        }
                    ]
                },
                {
                    "name": "Inventory",
                    "short_name": "Inventory",
                    "description": "Manage inventory items",
                    "url": "/inventory",
                    "icons": [
                        {
                            "src": "/static/icons/inventory-96x96.png",
                            "sizes": "96x96"
                        }
                    ]
                },
                {
                    "name": "Experiments",
                    "short_name": "Experiments",
                    "description": "Track experiments",
                    "url": "/experiments",
                    "icons": [
                        {
                            "src": "/static/icons/experiments-96x96.png",
                            "sizes": "96x96"
                        }
                    ]
                }
            ],
            "related_applications": [],
            "prefer_related_applications": False
        }

        return manifest

    def generate_service_worker(self) -> str:
        """
        Generate service worker JavaScript code.

        Returns:
            Service worker JavaScript code
        """
        sw_code = """
// Service Worker for Lab Management System
const CACHE_NAME = 'lab-management-v1.0.0';
const OFFLINE_URL = '/offline.html';

// Files to cache for offline use
const CACHE_FILES = [
    '/',
    '/offline.html',
    '/static/css/app.css',
    '/static/js/app.js',
    '/static/icons/icon-192x192.png',
    '/static/icons/icon-512x512.png',
    '/dashboard',
    '/inventory',
    '/experiments'
];

// Install event - cache essential files
self.addEventListener('install', (event) => {
    console.log('Service Worker: Installing...');

    event.waitUntil(
        caches.open(CACHE_NAME)
            .then((cache) => {
                console.log('Service Worker: Caching files');
                return cache.addAll(CACHE_FILES);
            })
            .then(() => {
                console.log('Service Worker: Installed successfully');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('Service Worker: Installation failed', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('Service Worker: Activating...');

    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated successfully');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', (event) => {
    // Skip non-GET requests
    if (event.request.method !== 'GET') {
        return;
    }

    // Skip API requests for now (could implement background sync)
    if (event.request.url.includes('/api/')) {
        return;
    }

    event.respondWith(
        caches.match(event.request)
            .then((cachedResponse) => {
                // Return cached version if available
                if (cachedResponse) {
                    console.log('Service Worker: Serving from cache', event.request.url);
                    return cachedResponse;
                }

                // Try to fetch from network
                return fetch(event.request)
                    .then((response) => {
                        // Don't cache non-successful responses
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }

                        // Clone response for caching
                        const responseToCache = response.clone();

                        caches.open(CACHE_NAME)
                            .then((cache) => {
                                cache.put(event.request, responseToCache);
                            });

                        return response;
                    })
                    .catch(() => {
                        // Network failed, try to serve offline page for navigation requests
                        if (event.request.mode === 'navigate') {
                            return caches.match(OFFLINE_URL);
                        }

                        // For other requests, return a generic offline response
                        return new Response('Offline', {
                            status: 503,
                            statusText: 'Service Unavailable',
                            headers: new Headers({
                                'Content-Type': 'text/plain'
                            })
                        });
                    });
            })
    );
});

// Background sync for data synchronization
self.addEventListener('sync', (event) => {
    console.log('Service Worker: Background sync triggered', event.tag);

    if (event.tag === 'background-sync-inventory') {
        event.waitUntil(syncInventoryData());
    } else if (event.tag === 'background-sync-experiments') {
        event.waitUntil(syncExperimentData());
    }
});

// Push notifications
self.addEventListener('push', (event) => {
    console.log('Service Worker: Push notification received');

    const options = {
        body: event.data ? event.data.text() : 'New notification from Lab Management System',
        icon: '/static/icons/icon-192x192.png',
        badge: '/static/icons/badge-72x72.png',
        vibrate: [200, 100, 200],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'View Details',
                icon: '/static/icons/checkmark.png'
            },
            {
                action: 'close',
                title: 'Close',
                icon: '/static/icons/xmark.png'
            }
        ]
    };

    event.waitUntil(
        self.registration.showNotification('Lab Management System', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
    console.log('Service Worker: Notification clicked');

    event.notification.close();

    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Helper functions for background sync
async function syncInventoryData() {
    try {
        // Get pending inventory updates from IndexedDB
        const pendingUpdates = await getPendingInventoryUpdates();

        for (const update of pendingUpdates) {
            try {
                const response = await fetch('/api/inventory', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(update.data)
                });

                if (response.ok) {
                    await removePendingUpdate(update.id);
                    console.log('Service Worker: Synced inventory update', update.id);
                }
            } catch (error) {
                console.error('Service Worker: Failed to sync inventory update', error);
            }
        }
    } catch (error) {
        console.error('Service Worker: Background sync failed', error);
    }
}

async function syncExperimentData() {
    try {
        // Similar implementation for experiment data sync
        console.log('Service Worker: Syncing experiment data...');
    } catch (error) {
        console.error('Service Worker: Experiment sync failed', error);
    }
}

async function getPendingInventoryUpdates() {
    // Implementation would use IndexedDB to get pending updates
    return [];
}

async function removePendingUpdate(id) {
    // Implementation would remove the update from IndexedDB
    console.log('Service Worker: Removed pending update', id);
}

console.log('Service Worker: Loaded successfully');
"""
        return sw_code

    def create_offline_page(self) -> str:
        """
        Create offline fallback page HTML.

        Returns:
            HTML content for offline page
        """
        offline_html = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Lab Management System</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1976d2, #42a5f5);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            text-align: center;
        }

        .offline-container {
            max-width: 400px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .offline-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .offline-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }

        .offline-message {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .retry-button {
            background: white;
            color: #1976d2;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .retry-button:hover {
            transform: translateY(-2px);
        }

        .features-list {
            margin-top: 2rem;
            text-align: left;
        }

        .features-list h3 {
            margin-bottom: 1rem;
        }

        .features-list ul {
            list-style: none;
            padding: 0;
        }

        .features-list li {
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .features-list li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4caf50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">🧪</div>
        <h1 class="offline-title">You're Offline</h1>
        <p class="offline-message">
            No internet connection detected. Some features may be limited,
            but you can still access cached data.
        </p>

        <button class="retry-button" onclick="window.location.reload()">
            Try Again
        </button>

        <div class="features-list">
            <h3>Available Offline:</h3>
            <ul>
                <li>View cached inventory data</li>
                <li>Access recent experiments</li>
                <li>Browse saved reports</li>
                <li>Use offline calculators</li>
            </ul>
        </div>
    </div>

    <script>
        // Check for connection and auto-reload when back online
        window.addEventListener('online', () => {
            window.location.reload();
        });

        // Show connection status
        function updateConnectionStatus() {
            if (navigator.onLine) {
                window.location.reload();
            }
        }

        // Check connection periodically
        setInterval(updateConnectionStatus, 5000);
    </script>
</body>
</html>
"""
        return offline_html

    def save_manifest(self):
        """Save manifest file to disk."""
        os.makedirs(os.path.dirname(self.manifest_path), exist_ok=True)

        manifest = self.generate_manifest()
        with open(self.manifest_path, 'w') as f:
            json.dump(manifest, f, indent=2)

    def save_service_worker(self):
        """Save service worker file to disk."""
        os.makedirs(os.path.dirname(self.service_worker_path), exist_ok=True)

        sw_code = self.generate_service_worker()
        with open(self.service_worker_path, 'w') as f:
            f.write(sw_code)

    def save_offline_page(self):
        """Save offline page to disk."""
        offline_path = "static/offline.html"
        os.makedirs(os.path.dirname(offline_path), exist_ok=True)

        offline_html = self.create_offline_page()
        with open(offline_path, 'w', encoding='utf-8') as f:
            f.write(offline_html)

    def setup_pwa(self):
        """Set up all PWA files."""
        print("🔧 Setting up Progressive Web App features...")

        self.save_manifest()
        print("✅ Manifest file created")

        self.save_service_worker()
        print("✅ Service worker created")

        self.save_offline_page()
        print("✅ Offline page created")

        print("🎉 PWA setup complete!")
        print("📱 Your app can now be installed and work offline!")


if __name__ == "__main__":
    pwa_manager = PWAManager()
    pwa_manager.setup_pwa()
