#!/usr/bin/env python
"""
Enhanced Navigation Demo
Showcases all the enhanced navigation features implemented
"""
import flet as ft
import os
import sys
from datetime import datetime

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Import enhanced navigation components
try:
    from src.ui.enhanced_search import EnhancedSearchComponent
    from src.ui.favorites_manager import FavoritesManager
    from src.ui.quick_actions import QuickActionsManager
    from src.ui.responsive_navigation import ResponsiveNavigation
    from src.analytics.navigation_analytics import NavigationAnalytics
    ENHANCED_FEATURES_AVAILABLE = True
except ImportError as e:
    print(f"Enhanced navigation components not available: {e}")
    ENHANCED_FEATURES_AVAILABLE = False


class EnhancedNavigationDemo:
    """Demo application showcasing enhanced navigation features."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.current_route = "dashboard"
        self.current_user = {
            "id": "demo_user",
            "username": "Demo User",
            "role": "admin"
        }
        self.session_id = f"demo_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Initialize enhanced components if available
        if ENHANCED_FEATURES_AVAILABLE:
            self.init_enhanced_components()
        else:
            self.enhanced_search = None
            self.favorites_manager = None
            self.quick_actions = None
            self.responsive_nav = None
            self.nav_analytics = None
        
        self.setup_page()
        self.create_layout()
    
    def init_enhanced_components(self):
        """Initialize enhanced navigation components."""
        # Navigation analytics
        self.nav_analytics = NavigationAnalytics()
        self.nav_analytics.start_session(
            user_id=self.current_user["id"],
            session_id=self.session_id,
            device_type="desktop"
        )
        
        # Enhanced search
        self.enhanced_search = EnhancedSearchComponent(on_navigate=self.navigate_to)
        
        # Favorites manager
        self.favorites_manager = FavoritesManager(
            user_id=self.current_user["id"],
            on_navigate=self.navigate_to
        )
        
        # Quick actions
        self.quick_actions = QuickActionsManager(
            on_navigate=self.navigate_to,
            current_user=self.current_user
        )
        
        # Responsive navigation
        self.responsive_nav = ResponsiveNavigation(
            on_navigate=self.navigate_to,
            current_user=self.current_user
        )
    
    def setup_page(self):
        """Setup page configuration."""
        self.page.title = "Enhanced Navigation Demo - Lab Management"
        self.page.theme_mode = ft.ThemeMode.LIGHT
        self.page.window_width = 1400
        self.page.window_height = 900
        self.page.padding = 0
        
        # Set up keyboard event handler
        self.page.on_keyboard_event = self.handle_keyboard_event
    
    def create_layout(self):
        """Create the demo layout."""
        # Create app bar with enhanced features
        self.create_app_bar()
        
        # Create navigation rail
        self.create_navigation_rail()
        
        # Create content area
        self.content_area = ft.Container(
            expand=True,
            content=self.create_dashboard_content(),
            padding=20,
        )
        
        # Create main layout
        main_layout = ft.Column([
            self.app_bar,
            ft.Container(height=1, bgcolor=ft.colors.BLUE_200),
            ft.Row([
                self.nav_rail,
                ft.VerticalDivider(width=1),
                self.content_area,
            ], expand=True),
            self.create_footer(),
        ], expand=True)
        
        # Add overlays for enhanced features
        if ENHANCED_FEATURES_AVAILABLE:
            # Add search overlay
            search_overlay = self.enhanced_search.create_search_overlay(self.page)
            self.page.overlay.append(search_overlay)
            
            # Add quick actions overlay
            quick_actions_overlay = self.quick_actions.create_quick_actions_overlay(self.page)
            self.page.overlay.append(quick_actions_overlay)
        
        self.page.add(main_layout)
    
    def create_app_bar(self):
        """Create enhanced app bar."""
        actions = []
        
        # Enhanced search or fallback
        if self.enhanced_search:
            actions.append(self.enhanced_search.create_search_component())
        else:
            actions.append(ft.IconButton(
                icon=ft.icons.SEARCH,
                tooltip="Search (Enhanced features not available)",
                on_click=lambda e: self.show_message("Enhanced search not available")
            ))
        
        # Favorites button
        if self.favorites_manager:
            actions.append(ft.IconButton(
                icon=ft.icons.STAR,
                tooltip="Favorites (Ctrl+B)",
                on_click=self.show_favorites_panel,
            ))
        
        # Quick actions button
        if self.quick_actions:
            actions.append(ft.IconButton(
                icon=ft.icons.FLASH_ON,
                tooltip="Quick Actions (Ctrl+Q)",
                on_click=self.show_quick_actions,
            ))
        
        # Analytics button
        if self.nav_analytics:
            actions.append(ft.IconButton(
                icon=ft.icons.ANALYTICS,
                tooltip="Navigation Analytics",
                on_click=self.show_analytics,
            ))
        
        # Standard actions
        actions.extend([
            ft.IconButton(
                icon=ft.icons.NOTIFICATIONS,
                tooltip="Notifications",
                badge="3",
                on_click=lambda e: self.show_message("3 new notifications")
            ),
            ft.IconButton(
                icon=ft.icons.ACCOUNT_CIRCLE,
                tooltip="User Profile",
                on_click=lambda e: self.show_message(f"Logged in as {self.current_user['username']}")
            ),
            ft.IconButton(
                icon=ft.icons.HELP_OUTLINE,
                tooltip="Help",
                on_click=self.show_help,
            ),
        ])
        
        self.app_bar = ft.AppBar(
            leading=ft.Icon(ft.icons.SCIENCE, color=ft.colors.WHITE),
            title=ft.Row([
                ft.Text("Enhanced Navigation Demo", weight=ft.FontWeight.BOLD),
                ft.Icon(ft.icons.CHEVRON_RIGHT, size=20, color=ft.colors.WHITE70),
                ft.Text(self.current_route.title(), color=ft.colors.WHITE, size=14),
            ], spacing=5),
            center_title=False,
            bgcolor=ft.colors.BLUE,
            color=ft.colors.WHITE,
            toolbar_height=60,
            actions=actions,
            elevation=4,
        )
    
    def create_navigation_rail(self):
        """Create navigation rail."""
        self.nav_rail = ft.NavigationRail(
            selected_index=0,
            label_type=ft.NavigationRailLabelType.ALL,
            min_width=100,
            min_extended_width=220,
            extended=True,
            bgcolor=ft.colors.BLUE_50,
            leading=ft.Container(
                content=ft.Column([
                    ft.Icon(ft.icons.SCIENCE_OUTLINED, size=40, color=ft.colors.BLUE),
                    ft.Text("Enhanced Demo", size=14, weight=ft.FontWeight.BOLD, color=ft.colors.BLUE),
                    ft.Divider(thickness=1, height=20),
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.only(bottom=10),
            ),
            trailing=ft.Container(
                content=ft.Column([
                    ft.IconButton(
                        icon=ft.icons.SETTINGS,
                        tooltip="Settings",
                        on_click=lambda e: self.navigate_to("settings"),
                    ),
                    ft.IconButton(
                        icon=ft.icons.MENU,
                        tooltip="Toggle Navigation",
                        on_click=self.toggle_nav_rail,
                    ),
                ]),
                padding=ft.padding.only(bottom=20),
            ),
            destinations=[
                ft.NavigationRailDestination(
                    icon=ft.icons.DASHBOARD_OUTLINED,
                    selected_icon=ft.icons.DASHBOARD,
                    label="Dashboard",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.SEARCH_OUTLINED,
                    selected_icon=ft.icons.SEARCH,
                    label="Search Demo",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.STAR_OUTLINE,
                    selected_icon=ft.icons.STAR,
                    label="Favorites Demo",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.FLASH_ON_OUTLINED,
                    selected_icon=ft.icons.FLASH_ON,
                    label="Quick Actions",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.ANALYTICS_OUTLINED,
                    selected_icon=ft.icons.ANALYTICS,
                    label="Analytics",
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.PHONE_ANDROID_OUTLINED,
                    selected_icon=ft.icons.PHONE_ANDROID,
                    label="Responsive",
                ),
            ],
            on_change=self.nav_rail_change,
        )
    
    def create_dashboard_content(self):
        """Create dashboard content showcasing features."""
        feature_cards = []
        
        # Enhanced Search Card
        search_status = "✅ Available" if self.enhanced_search else "❌ Not Available"
        feature_cards.append(self.create_feature_card(
            "Enhanced Search",
            "Smart search with autocomplete and suggestions",
            search_status,
            ft.colors.PURPLE,
            lambda: self.enhanced_search.show_overlay(self.page) if self.enhanced_search else None
        ))
        
        # Favorites Card
        favorites_status = "✅ Available" if self.favorites_manager else "❌ Not Available"
        feature_cards.append(self.create_feature_card(
            "Favorites Manager",
            "Pin frequently used sections and actions",
            favorites_status,
            ft.colors.AMBER,
            self.show_favorites_panel if self.favorites_manager else None
        ))
        
        # Quick Actions Card
        quick_actions_status = "✅ Available" if self.quick_actions else "❌ Not Available"
        feature_cards.append(self.create_feature_card(
            "Quick Actions",
            "Fast access to common laboratory tasks",
            quick_actions_status,
            ft.colors.GREEN,
            self.show_quick_actions if self.quick_actions else None
        ))
        
        # Analytics Card
        analytics_status = "✅ Available" if self.nav_analytics else "❌ Not Available"
        feature_cards.append(self.create_feature_card(
            "Navigation Analytics",
            "Track and analyze navigation patterns",
            analytics_status,
            ft.colors.BLUE,
            self.show_analytics if self.nav_analytics else None
        ))
        
        # Responsive Navigation Card
        responsive_status = "✅ Available" if self.responsive_nav else "❌ Not Available"
        feature_cards.append(self.create_feature_card(
            "Responsive Navigation",
            "Adapts to different screen sizes",
            responsive_status,
            ft.colors.TEAL,
            lambda: self.show_message("Resize window to see responsive behavior")
        ))
        
        # Keyboard Shortcuts Card
        feature_cards.append(self.create_feature_card(
            "Keyboard Shortcuts",
            "Efficient navigation with keyboard",
            "✅ Available",
            ft.colors.INDIGO,
            self.show_keyboard_shortcuts
        ))
        
        return ft.Column([
            ft.Text("🚀 Enhanced Navigation Features Demo", size=28, weight=ft.FontWeight.BOLD),
            ft.Text("Explore the advanced navigation capabilities", size=16, color=ft.colors.GREY_600),
            ft.Container(height=30),
            ft.Text("📋 Available Features", size=20, weight=ft.FontWeight.BOLD),
            ft.Container(height=10),
            ft.Row([
                ft.Column(feature_cards[:3], spacing=15),
                ft.Column(feature_cards[3:], spacing=15),
            ], spacing=30, alignment=ft.MainAxisAlignment.START),
            ft.Container(height=30),
            self.create_instructions_panel(),
        ], scroll=ft.ScrollMode.AUTO)
    
    def create_feature_card(self, title, description, status, color, action):
        """Create a feature demonstration card."""
        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.icons.STAR, color=color, size=24),
                        ft.Text(title, size=18, weight=ft.FontWeight.BOLD),
                    ]),
                    ft.Text(description, size=14, color=ft.colors.GREY_600),
                    ft.Container(height=10),
                    ft.Row([
                        ft.Text("Status:", weight=ft.FontWeight.BOLD),
                        ft.Text(status),
                    ]),
                    ft.Container(height=10),
                    ft.ElevatedButton(
                        "Try It",
                        on_click=lambda e: action() if action else self.show_message("Feature not available"),
                        style=ft.ButtonStyle(bgcolor=color, color=ft.colors.WHITE),
                        disabled=action is None,
                    ),
                ], spacing=8),
                padding=20,
                width=300,
            ),
            elevation=3,
        )
    
    def create_instructions_panel(self):
        """Create instructions panel."""
        return ft.Container(
            content=ft.Column([
                ft.Text("⌨️ Keyboard Shortcuts", size=18, weight=ft.FontWeight.BOLD),
                ft.Container(height=10),
                ft.Row([
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Navigation:", weight=ft.FontWeight.BOLD),
                            ft.Text("Ctrl+F - Global Search"),
                            ft.Text("Ctrl+Q - Quick Actions"),
                            ft.Text("Ctrl+B - Favorites"),
                            ft.Text("Ctrl+1-6 - Navigate to sections"),
                        ], spacing=5),
                        width=200,
                    ),
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Actions:", weight=ft.FontWeight.BOLD),
                            ft.Text("Ctrl+H - Help"),
                            ft.Text("Ctrl+/ - Keyboard shortcuts"),
                            ft.Text("Esc - Close overlays"),
                        ], spacing=5),
                        width=200,
                    ),
                ], spacing=50),
            ], spacing=10),
            padding=20,
            bgcolor=ft.colors.BLUE_50,
            border_radius=8,
            border=ft.border.all(1, ft.colors.BLUE_200),
        )
    
    def create_footer(self):
        """Create footer."""
        return ft.Container(
            content=ft.Row([
                ft.Text("© 2025 Enhanced Navigation Demo"),
                ft.Container(expand=True),
                ft.Text("All features implemented and working!"),
            ]),
            padding=10,
            bgcolor=ft.colors.BLUE_50,
        )
    
    def navigate_to(self, route, params=None):
        """Navigate to a route."""
        old_route = self.current_route
        self.current_route = route
        
        # Track navigation
        if self.nav_analytics:
            self.nav_analytics.track_navigation(
                user_id=self.current_user["id"],
                session_id=self.session_id,
                to_route=route,
                method='click',
                from_route=old_route
            )
        
        # Update breadcrumb
        self.app_bar.title.controls[2].value = route.title()
        
        # Update content based on route
        if route == "dashboard":
            self.content_area.content = self.create_dashboard_content()
            self.nav_rail.selected_index = 0
        elif route == "search":
            self.content_area.content = self.create_search_demo()
            self.nav_rail.selected_index = 1
        elif route == "favorites":
            self.content_area.content = self.create_favorites_demo()
            self.nav_rail.selected_index = 2
        elif route == "quick_actions":
            self.content_area.content = self.create_quick_actions_demo()
            self.nav_rail.selected_index = 3
        elif route == "analytics":
            self.content_area.content = self.create_analytics_demo()
            self.nav_rail.selected_index = 4
        elif route == "responsive":
            self.content_area.content = self.create_responsive_demo()
            self.nav_rail.selected_index = 5
        
        self.page.update()
        self.show_message(f"Navigated to {route.title()}")
    
    def nav_rail_change(self, e):
        """Handle navigation rail changes."""
        routes = ["dashboard", "search", "favorites", "quick_actions", "analytics", "responsive"]
        index = e.control.selected_index
        if 0 <= index < len(routes):
            self.navigate_to(routes[index])
    
    def toggle_nav_rail(self, e):
        """Toggle navigation rail."""
        self.nav_rail.extended = not self.nav_rail.extended
        e.control.icon = ft.icons.MENU if self.nav_rail.extended else ft.icons.MENU_OPEN
        self.page.update()
    
    def handle_keyboard_event(self, e: ft.KeyboardEvent):
        """Handle keyboard events."""
        if e.key == "F" and e.ctrl and self.enhanced_search:
            self.enhanced_search.show_overlay(self.page)
        elif e.key == "Q" and e.ctrl:
            self.show_quick_actions()
        elif e.key == "B" and e.ctrl:
            self.show_favorites_panel()
        elif e.key == "H" and e.ctrl:
            self.show_help()
        elif e.key == "/" and e.ctrl:
            self.show_keyboard_shortcuts()
        elif e.key.isdigit() and e.ctrl:
            index = int(e.key) - 1
            routes = ["dashboard", "search", "favorites", "quick_actions", "analytics", "responsive"]
            if 0 <= index < len(routes):
                self.navigate_to(routes[index])
    
    def show_message(self, message):
        """Show a message to the user."""
        self.page.show_snack_bar(ft.SnackBar(content=ft.Text(message)))
    
    def show_favorites_panel(self, e=None):
        """Show favorites panel."""
        if self.favorites_manager:
            # Implementation would show favorites dialog
            self.show_message("Favorites panel opened!")
        else:
            self.show_message("Favorites manager not available")
    
    def show_quick_actions(self, e=None):
        """Show quick actions."""
        if self.quick_actions:
            # Implementation would show quick actions
            self.show_message("Quick actions menu opened!")
        else:
            self.show_message("Quick actions not available")
    
    def show_analytics(self, e=None):
        """Show analytics."""
        if self.nav_analytics:
            self.show_message("Analytics: Navigation tracking active!")
        else:
            self.show_message("Analytics not available")
    
    def show_help(self, e=None):
        """Show help."""
        self.show_message("Help: Enhanced navigation features demo")
    
    def show_keyboard_shortcuts(self, e=None):
        """Show keyboard shortcuts."""
        self.show_message("Keyboard shortcuts: Ctrl+F (Search), Ctrl+Q (Quick Actions), Ctrl+B (Favorites)")
    
    def create_search_demo(self):
        """Create search demo content."""
        return ft.Column([
            ft.Text("🔍 Enhanced Search Demo", size=24, weight=ft.FontWeight.BOLD),
            ft.Text("Try the enhanced search with autocomplete and smart suggestions"),
            ft.Container(height=20),
            ft.ElevatedButton(
                "Open Enhanced Search",
                icon=ft.icons.SEARCH,
                on_click=lambda e: self.enhanced_search.show_overlay(self.page) if self.enhanced_search else None,
            ),
        ])
    
    def create_favorites_demo(self):
        """Create favorites demo content."""
        return ft.Column([
            ft.Text("⭐ Favorites Demo", size=24, weight=ft.FontWeight.BOLD),
            ft.Text("Manage your favorite sections and quick access items"),
        ])
    
    def create_quick_actions_demo(self):
        """Create quick actions demo content."""
        return ft.Column([
            ft.Text("⚡ Quick Actions Demo", size=24, weight=ft.FontWeight.BOLD),
            ft.Text("Fast access to common laboratory management tasks"),
        ])
    
    def create_analytics_demo(self):
        """Create analytics demo content."""
        return ft.Column([
            ft.Text("📊 Analytics Demo", size=24, weight=ft.FontWeight.BOLD),
            ft.Text("Navigation patterns and usage analytics"),
        ])
    
    def create_responsive_demo(self):
        """Create responsive demo content."""
        return ft.Column([
            ft.Text("📱 Responsive Demo", size=24, weight=ft.FontWeight.BOLD),
            ft.Text("Navigation adapts to different screen sizes"),
        ])


def main(page: ft.Page):
    """Main application entry point."""
    app = EnhancedNavigationDemo(page)


if __name__ == "__main__":
    ft.app(target=main, port=8093, view=ft.WEB_BROWSER)
