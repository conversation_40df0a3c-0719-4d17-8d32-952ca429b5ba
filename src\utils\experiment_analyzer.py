import json
import math
import statistics
from datetime import datetime, timedelta

class ExperimentAnalyzer:
    """
    Utility class for analyzing experiment data and generating insights.
    """
    
    def __init__(self, experiment_model):
        """
        Initialize the experiment analyzer.
        
        Args:
            experiment_model: The experiment model instance
        """
        self.experiment_model = experiment_model
    
    def analyze_experiment(self, experiment_id):
        """
        Analyze an experiment and generate insights.
        
        Args:
            experiment_id (int): The experiment ID
            
        Returns:
            dict: Analysis results
        """
        # Get the experiment with all data
        experiment = self.experiment_model.get_experiment_by_id(experiment_id, include_data=True)
        if not experiment:
            return None
        
        # Get measurements
        measurements = self.experiment_model.get_measurements(experiment_id)
        
        # Group measurements by name
        measurement_groups = {}
        for measurement in measurements:
            name = measurement["name"]
            if name not in measurement_groups:
                measurement_groups[name] = []
            measurement_groups[name].append(measurement)
        
        # Analyze each measurement group
        analysis_results = {
            "experiment_id": experiment_id,
            "title": experiment["title"],
            "status": experiment["status"],
            "measurement_analysis": {},
            "time_analysis": self._analyze_time_data(experiment, measurements),
            "collaboration_analysis": self._analyze_collaboration(experiment_id),
            "summary": self._generate_summary(experiment, measurements, measurement_groups)
        }
        
        # Analyze each measurement group
        for name, group in measurement_groups.items():
            analysis_results["measurement_analysis"][name] = self._analyze_measurement_group(group)
        
        return analysis_results
    
    def _analyze_measurement_group(self, measurements):
        """
        Analyze a group of measurements with the same name.
        
        Args:
            measurements (list): List of measurements
            
        Returns:
            dict: Analysis results for the measurement group
        """
        if not measurements:
            return {}
        
        # Extract values
        values = [m["value"] for m in measurements]
        
        # Calculate statistics
        result = {
            "count": len(values),
            "min": min(values),
            "max": max(values),
            "range": max(values) - min(values),
            "mean": statistics.mean(values) if values else 0,
            "unit": measurements[0]["unit"] or "",
            "first_recorded": measurements[0]["timestamp"],
            "last_recorded": measurements[-1]["timestamp"]
        }
        
        # Calculate median and standard deviation if we have enough values
        if len(values) >= 2:
            result["median"] = statistics.median(values)
            result["standard_deviation"] = statistics.stdev(values)
            result["variance"] = statistics.variance(values)
            
            # Calculate coefficient of variation (CV)
            if result["mean"] != 0:
                result["cv"] = (result["standard_deviation"] / result["mean"]) * 100
            else:
                result["cv"] = 0
        
        # Detect outliers using the IQR method if we have enough values
        if len(values) >= 4:
            values_sorted = sorted(values)
            q1 = statistics.quantiles(values_sorted, n=4)[0]
            q3 = statistics.quantiles(values_sorted, n=4)[2]
            iqr = q3 - q1
            lower_bound = q1 - (1.5 * iqr)
            upper_bound = q3 + (1.5 * iqr)
            
            outliers = [v for v in values if v < lower_bound or v > upper_bound]
            result["outliers"] = outliers
            result["outlier_count"] = len(outliers)
            result["has_outliers"] = len(outliers) > 0
        
        return result
    
    def _analyze_time_data(self, experiment, measurements):
        """
        Analyze time-related data for an experiment.
        
        Args:
            experiment (dict): The experiment data
            measurements (list): List of measurements
            
        Returns:
            dict: Time analysis results
        """
        # Parse timestamps
        created_at = self._parse_timestamp(experiment["created_at"])
        last_updated = self._parse_timestamp(experiment["last_updated"])
        
        # Calculate duration
        duration = None
        if created_at and last_updated:
            duration = (last_updated - created_at).total_seconds()
        
        # Analyze measurement frequency
        measurement_timestamps = []
        for measurement in measurements:
            timestamp = self._parse_timestamp(measurement["timestamp"])
            if timestamp:
                measurement_timestamps.append(timestamp)
        
        measurement_timestamps.sort()
        
        # Calculate time intervals between measurements
        intervals = []
        for i in range(1, len(measurement_timestamps)):
            interval = (measurement_timestamps[i] - measurement_timestamps[i-1]).total_seconds()
            intervals.append(interval)
        
        # Calculate statistics on intervals
        interval_stats = {}
        if intervals:
            interval_stats = {
                "count": len(intervals),
                "min": min(intervals) if intervals else 0,
                "max": max(intervals) if intervals else 0,
                "mean": statistics.mean(intervals) if intervals else 0
            }
            
            if len(intervals) >= 2:
                interval_stats["median"] = statistics.median(intervals)
                interval_stats["standard_deviation"] = statistics.stdev(intervals)
        
        # Determine if measurements are regular or irregular
        is_regular = False
        if interval_stats and "standard_deviation" in interval_stats and interval_stats["mean"] > 0:
            # If the coefficient of variation is less than 20%, consider it regular
            cv = (interval_stats["standard_deviation"] / interval_stats["mean"]) * 100
            is_regular = cv < 20
        
        return {
            "created_at": experiment["created_at"],
            "last_updated": experiment["last_updated"],
            "duration_seconds": duration,
            "duration_formatted": self._format_duration(duration) if duration else None,
            "measurement_count": len(measurements),
            "first_measurement": measurement_timestamps[0].isoformat() if measurement_timestamps else None,
            "last_measurement": measurement_timestamps[-1].isoformat() if measurement_timestamps else None,
            "measurement_span_seconds": (measurement_timestamps[-1] - measurement_timestamps[0]).total_seconds() if len(measurement_timestamps) >= 2 else 0,
            "measurement_intervals": interval_stats,
            "is_regular_interval": is_regular
        }
    
    def _analyze_collaboration(self, experiment_id):
        """
        Analyze collaboration data for an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            
        Returns:
            dict: Collaboration analysis results
        """
        # Get collaborators
        collaborators = self.experiment_model.get_experiment_collaborators(experiment_id)
        
        # Get comments
        comments = self.experiment_model.get_comments(experiment_id)
        
        # Get measurements and group by creator
        measurements = self.experiment_model.get_measurements(experiment_id)
        measurements_by_creator = {}
        for measurement in measurements:
            creator_id = measurement["created_by"]
            if creator_id not in measurements_by_creator:
                measurements_by_creator[creator_id] = []
            measurements_by_creator[creator_id].append(measurement)
        
        # Get files and group by uploader
        files = self.experiment_model.get_files(experiment_id)
        files_by_uploader = {}
        for file in files:
            uploader_id = file["uploaded_by"]
            if uploader_id not in files_by_uploader:
                files_by_uploader[uploader_id] = []
            files_by_uploader[uploader_id].append(file)
        
        # Analyze comments by creator
        comments_by_creator = {}
        for comment in comments:
            creator_id = comment["created_by"]
            if creator_id not in comments_by_creator:
                comments_by_creator[creator_id] = []
            comments_by_creator[creator_id].append(comment)
        
        return {
            "collaborator_count": len(collaborators),
            "comment_count": len(comments),
            "measurement_count": len(measurements),
            "file_count": len(files),
            "contributors": {
                "measurements": len(measurements_by_creator),
                "comments": len(comments_by_creator),
                "files": len(files_by_uploader)
            },
            "activity_by_user": self._analyze_activity_by_user(
                collaborators, 
                measurements_by_creator, 
                comments_by_creator, 
                files_by_uploader
            )
        }
    
    def _analyze_activity_by_user(self, collaborators, measurements_by_creator, comments_by_creator, files_by_uploader):
        """
        Analyze activity by user.
        
        Args:
            collaborators (list): List of collaborators
            measurements_by_creator (dict): Measurements grouped by creator
            comments_by_creator (dict): Comments grouped by creator
            files_by_uploader (dict): Files grouped by uploader
            
        Returns:
            dict: Activity analysis by user
        """
        result = {}
        
        # Add collaborators
        for collaborator in collaborators:
            user_id = collaborator["user_id"]
            result[user_id] = {
                "user_id": user_id,
                "name": collaborator["full_name"],
                "username": collaborator["username"],
                "email": collaborator["email"],
                "measurements": [],
                "comments": [],
                "files": [],
                "measurement_count": 0,
                "comment_count": 0,
                "file_count": 0,
                "total_contributions": 0
            }
        
        # Add measurements
        for user_id, measurements in measurements_by_creator.items():
            if user_id not in result:
                # Get user info if not a collaborator
                result[user_id] = {
                    "user_id": user_id,
                    "name": measurements[0]["creator_name"] if measurements else f"User {user_id}",
                    "username": "",
                    "email": "",
                    "measurements": [],
                    "comments": [],
                    "files": [],
                    "measurement_count": 0,
                    "comment_count": 0,
                    "file_count": 0,
                    "total_contributions": 0
                }
            
            result[user_id]["measurements"] = measurements
            result[user_id]["measurement_count"] = len(measurements)
            result[user_id]["total_contributions"] += len(measurements)
        
        # Add comments
        for user_id, comments in comments_by_creator.items():
            if user_id not in result:
                # Get user info if not a collaborator
                result[user_id] = {
                    "user_id": user_id,
                    "name": comments[0]["creator_name"] if comments else f"User {user_id}",
                    "username": "",
                    "email": "",
                    "measurements": [],
                    "comments": [],
                    "files": [],
                    "measurement_count": 0,
                    "comment_count": 0,
                    "file_count": 0,
                    "total_contributions": 0
                }
            
            result[user_id]["comments"] = comments
            result[user_id]["comment_count"] = len(comments)
            result[user_id]["total_contributions"] += len(comments)
        
        # Add files
        for user_id, files in files_by_uploader.items():
            if user_id not in result:
                # Get user info if not a collaborator
                result[user_id] = {
                    "user_id": user_id,
                    "name": files[0]["uploader_name"] if files else f"User {user_id}",
                    "username": "",
                    "email": "",
                    "measurements": [],
                    "comments": [],
                    "files": [],
                    "measurement_count": 0,
                    "comment_count": 0,
                    "file_count": 0,
                    "total_contributions": 0
                }
            
            result[user_id]["files"] = files
            result[user_id]["file_count"] = len(files)
            result[user_id]["total_contributions"] += len(files)
        
        return result
    
    def _generate_summary(self, experiment, measurements, measurement_groups):
        """
        Generate a summary of the experiment analysis.
        
        Args:
            experiment (dict): The experiment data
            measurements (list): List of measurements
            measurement_groups (dict): Measurements grouped by name
            
        Returns:
            dict: Summary of the analysis
        """
        # Parse experiment data
        data = {}
        if experiment["data"]:
            try:
                data = json.loads(experiment["data"])
            except:
                data = {}
        
        # Generate insights based on measurements
        insights = []
        
        # Check if there are any measurements
        if not measurements:
            insights.append("No measurements have been recorded for this experiment.")
        else:
            # Check for measurement consistency
            if len(measurement_groups) == 1:
                insights.append(f"All measurements are of the same type: {list(measurement_groups.keys())[0]}.")
            else:
                insights.append(f"The experiment tracks {len(measurement_groups)} different types of measurements.")
            
            # Check for measurement frequency
            if len(measurements) >= 5:
                # Calculate time span
                timestamps = [self._parse_timestamp(m["timestamp"]) for m in measurements if self._parse_timestamp(m["timestamp"])]
                if timestamps:
                    timestamps.sort()
                    time_span = (timestamps[-1] - timestamps[0]).total_seconds()
                    avg_interval = time_span / (len(timestamps) - 1) if len(timestamps) > 1 else 0
                    
                    if avg_interval > 0:
                        insights.append(f"Measurements were taken approximately every {self._format_duration(avg_interval)}.")
        
        # Check experiment duration
        created_at = self._parse_timestamp(experiment["created_at"])
        last_updated = self._parse_timestamp(experiment["last_updated"])
        
        if created_at and last_updated:
            duration = (last_updated - created_at).total_seconds()
            if duration > 0:
                insights.append(f"The experiment has been running for {self._format_duration(duration)}.")
        
        # Check experiment status
        if experiment["status"] == "Completed":
            insights.append("The experiment is marked as completed.")
        elif experiment["status"] == "Active":
            insights.append("The experiment is currently active.")
        
        # Check for outliers in measurements
        outlier_count = 0
        for group_name, group_analysis in self._analyze_all_measurement_groups(measurement_groups).items():
            if "has_outliers" in group_analysis and group_analysis["has_outliers"]:
                outlier_count += group_analysis["outlier_count"]
        
        if outlier_count > 0:
            insights.append(f"Found {outlier_count} potential outliers across all measurements.")
        
        return {
            "title": experiment["title"],
            "description": experiment["description"],
            "status": experiment["status"],
            "measurement_count": len(measurements),
            "measurement_types": list(measurement_groups.keys()),
            "insights": insights,
            "has_objective": bool(data.get("objective", "")),
            "has_conclusion": bool(data.get("conclusion", "")),
            "completeness_score": self._calculate_completeness_score(experiment, data, measurements)
        }
    
    def _analyze_all_measurement_groups(self, measurement_groups):
        """
        Analyze all measurement groups.
        
        Args:
            measurement_groups (dict): Measurements grouped by name
            
        Returns:
            dict: Analysis results for all measurement groups
        """
        results = {}
        for name, group in measurement_groups.items():
            results[name] = self._analyze_measurement_group(group)
        return results
    
    def _calculate_completeness_score(self, experiment, data, measurements):
        """
        Calculate a completeness score for the experiment.
        
        Args:
            experiment (dict): The experiment data
            data (dict): Parsed experiment data
            measurements (list): List of measurements
            
        Returns:
            float: Completeness score (0-100)
        """
        score = 0
        total_points = 0
        
        # Basic experiment info
        if experiment["title"]:
            score += 10
        total_points += 10
        
        if experiment["description"]:
            score += 5
        total_points += 5
        
        # Experiment details
        if data.get("objective", ""):
            score += 10
        total_points += 10
        
        if data.get("materials", ""):
            score += 5
        total_points += 5
        
        if data.get("procedure", ""):
            score += 10
        total_points += 10
        
        if data.get("observations", ""):
            score += 5
        total_points += 5
        
        if data.get("results", ""):
            score += 10
        total_points += 10
        
        if data.get("conclusion", ""):
            score += 10
        total_points += 10
        
        # Measurements
        if measurements:
            # Up to 20 points for measurements
            measurement_score = min(20, len(measurements))
            score += measurement_score
        total_points += 20
        
        # Status
        if experiment["status"] == "Completed":
            score += 15
        elif experiment["status"] == "Active":
            score += 10
        elif experiment["status"] == "Draft":
            score += 5
        total_points += 15
        
        # Calculate percentage
        return round((score / total_points) * 100) if total_points > 0 else 0
    
    def _parse_timestamp(self, timestamp):
        """Parse a timestamp string into a datetime object."""
        if not timestamp:
            return None
        
        try:
            return datetime.fromisoformat(timestamp)
        except:
            try:
                return datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S")
            except:
                return None
    
    def _format_duration(self, seconds):
        """Format a duration in seconds to a human-readable string."""
        if seconds is None:
            return "unknown duration"
        
        if seconds < 60:
            return f"{int(seconds)} seconds"
        elif seconds < 3600:
            return f"{int(seconds / 60)} minutes"
        elif seconds < 86400:
            return f"{int(seconds / 3600)} hours"
        else:
            return f"{int(seconds / 86400)} days"
    
    def generate_experiment_report(self, experiment_id, format="html"):
        """
        Generate a comprehensive report for an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            format (str): The report format (html, pdf, etc.)
            
        Returns:
            str: The generated report
        """
        # Get the experiment with all data
        experiment = self.experiment_model.get_experiment_by_id(experiment_id, include_data=True)
        if not experiment:
            return None
        
        # Analyze the experiment
        analysis = self.analyze_experiment(experiment_id)
        
        # Parse experiment data
        data = {}
        if experiment["data"]:
            try:
                data = json.loads(experiment["data"])
            except:
                data = {}
        
        # Generate HTML report
        if format == "html":
            return self._generate_html_report(experiment, data, analysis)
        
        # Default to plain text report
        return self._generate_text_report(experiment, data, analysis)
    
    def _generate_html_report(self, experiment, data, analysis):
        """
        Generate an HTML report for an experiment.
        
        Args:
            experiment (dict): The experiment data
            data (dict): Parsed experiment data
            analysis (dict): Analysis results
            
        Returns:
            str: HTML report
        """
        # Format dates
        created_at = self._format_timestamp_for_display(experiment["created_at"])
        last_updated = self._format_timestamp_for_display(experiment["last_updated"])
        
        # Generate measurement tables
        measurement_tables = ""
        for name, group_analysis in analysis["measurement_analysis"].items():
            measurement_tables += f"""
            <div class="measurement-group">
                <h3>{name} ({group_analysis.get('unit', '')})</h3>
                <table class="measurement-table">
                    <tr>
                        <th>Statistic</th>
                        <th>Value</th>
                    </tr>
                    <tr>
                        <td>Count</td>
                        <td>{group_analysis.get('count', 0)}</td>
                    </tr>
                    <tr>
                        <td>Minimum</td>
                        <td>{group_analysis.get('min', 'N/A')}</td>
                    </tr>
                    <tr>
                        <td>Maximum</td>
                        <td>{group_analysis.get('max', 'N/A')}</td>
                    </tr>
                    <tr>
                        <td>Mean</td>
                        <td>{group_analysis.get('mean', 'N/A')}</td>
                    </tr>
                    <tr>
                        <td>Median</td>
                        <td>{group_analysis.get('median', 'N/A')}</td>
                    </tr>
                    <tr>
                        <td>Standard Deviation</td>
                        <td>{group_analysis.get('standard_deviation', 'N/A')}</td>
                    </tr>
                </table>
            </div>
            """
        
        # Generate insights list
        insights_list = ""
        for insight in analysis["summary"]["insights"]:
            insights_list += f"<li>{insight}</li>"
        
        # Generate HTML
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Experiment Report: {experiment['title']}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1, h2, h3 {{ color: #333; }}
                .section {{ margin-bottom: 20px; }}
                .info-table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
                .info-table th, .info-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                .info-table th {{ background-color: #f2f2f2; }}
                .measurement-table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
                .measurement-table th, .measurement-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                .measurement-table th {{ background-color: #f2f2f2; }}
                .measurement-group {{ margin-bottom: 30px; }}
                .completeness {{ width: 100%; background-color: #f3f3f3; border-radius: 5px; }}
                .completeness-bar {{ height: 20px; background-color: #4CAF50; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <h1>Experiment Report: {experiment['title']}</h1>
            
            <div class="section">
                <h2>Experiment Information</h2>
                <table class="info-table">
                    <tr>
                        <th>Title</th>
                        <td>{experiment['title']}</td>
                    </tr>
                    <tr>
                        <th>Description</th>
                        <td>{experiment['description'] or 'No description provided'}</td>
                    </tr>
                    <tr>
                        <th>Status</th>
                        <td>{experiment['status']}</td>
                    </tr>
                    <tr>
                        <th>Created By</th>
                        <td>{experiment['creator_name']}</td>
                    </tr>
                    <tr>
                        <th>Created At</th>
                        <td>{created_at}</td>
                    </tr>
                    <tr>
                        <th>Last Updated</th>
                        <td>{last_updated}</td>
                    </tr>
                    <tr>
                        <th>Duration</th>
                        <td>{analysis['time_analysis']['duration_formatted'] or 'N/A'}</td>
                    </tr>
                </table>
            </div>
            
            <div class="section">
                <h2>Experiment Details</h2>
                <table class="info-table">
                    <tr>
                        <th>Objective</th>
                        <td>{data.get('objective', 'No objective provided')}</td>
                    </tr>
                    <tr>
                        <th>Materials</th>
                        <td>{data.get('materials', 'No materials listed')}</td>
                    </tr>
                    <tr>
                        <th>Procedure</th>
                        <td>{data.get('procedure', 'No procedure provided')}</td>
                    </tr>
                    <tr>
                        <th>Observations</th>
                        <td>{data.get('observations', 'No observations recorded')}</td>
                    </tr>
                    <tr>
                        <th>Results</th>
                        <td>{data.get('results', 'No results recorded')}</td>
                    </tr>
                    <tr>
                        <th>Conclusion</th>
                        <td>{data.get('conclusion', 'No conclusion provided')}</td>
                    </tr>
                </table>
            </div>
            
            <div class="section">
                <h2>Measurement Analysis</h2>
                {measurement_tables}
            </div>
            
            <div class="section">
                <h2>Collaboration</h2>
                <p>Number of collaborators: {analysis['collaboration_analysis']['collaborator_count']}</p>
                <p>Number of comments: {analysis['collaboration_analysis']['comment_count']}</p>
                <p>Number of measurements: {analysis['collaboration_analysis']['measurement_count']}</p>
                <p>Number of files: {analysis['collaboration_analysis']['file_count']}</p>
            </div>
            
            <div class="section">
                <h2>Insights</h2>
                <ul>
                    {insights_list}
                </ul>
            </div>
            
            <div class="section">
                <h2>Completeness Score: {analysis['summary']['completeness_score']}%</h2>
                <div class="completeness">
                    <div class="completeness-bar" style="width: {analysis['summary']['completeness_score']}%;"></div>
                </div>
            </div>
            
            <div class="section">
                <p>Report generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def _generate_text_report(self, experiment, data, analysis):
        """
        Generate a plain text report for an experiment.
        
        Args:
            experiment (dict): The experiment data
            data (dict): Parsed experiment data
            analysis (dict): Analysis results
            
        Returns:
            str: Text report
        """
        # Format dates
        created_at = self._format_timestamp_for_display(experiment["created_at"])
        last_updated = self._format_timestamp_for_display(experiment["last_updated"])
        
        # Generate report
        report = f"""
EXPERIMENT REPORT: {experiment['title']}
===============================================================================

EXPERIMENT INFORMATION
---------------------
Title: {experiment['title']}
Description: {experiment['description'] or 'No description provided'}
Status: {experiment['status']}
Created By: {experiment['creator_name']}
Created At: {created_at}
Last Updated: {last_updated}
Duration: {analysis['time_analysis']['duration_formatted'] or 'N/A'}

EXPERIMENT DETAILS
-----------------
Objective: {data.get('objective', 'No objective provided')}

Materials: {data.get('materials', 'No materials listed')}

Procedure: {data.get('procedure', 'No procedure provided')}

Observations: {data.get('observations', 'No observations recorded')}

Results: {data.get('results', 'No results recorded')}

Conclusion: {data.get('conclusion', 'No conclusion provided')}

MEASUREMENT ANALYSIS
------------------
"""
        
        # Add measurement analysis
        for name, group_analysis in analysis["measurement_analysis"].items():
            report += f"""
{name} ({group_analysis.get('unit', '')})
  Count: {group_analysis.get('count', 0)}
  Minimum: {group_analysis.get('min', 'N/A')}
  Maximum: {group_analysis.get('max', 'N/A')}
  Mean: {group_analysis.get('mean', 'N/A')}
  Median: {group_analysis.get('median', 'N/A')}
  Standard Deviation: {group_analysis.get('standard_deviation', 'N/A')}
"""
        
        # Add collaboration info
        report += f"""
COLLABORATION
------------
Number of collaborators: {analysis['collaboration_analysis']['collaborator_count']}
Number of comments: {analysis['collaboration_analysis']['comment_count']}
Number of measurements: {analysis['collaboration_analysis']['measurement_count']}
Number of files: {analysis['collaboration_analysis']['file_count']}

INSIGHTS
--------
"""
        
        # Add insights
        for insight in analysis["summary"]["insights"]:
            report += f"- {insight}\n"
        
        # Add completeness score
        report += f"""
COMPLETENESS SCORE: {analysis['summary']['completeness_score']}%

Report generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        return report
    
    def _format_timestamp_for_display(self, timestamp):
        """Format a timestamp for display in reports."""
        dt = self._parse_timestamp(timestamp)
        if dt:
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        return "N/A"
    
    def schedule_automatic_analysis(self, experiment_id, interval_hours=24):
        """
        Schedule automatic analysis for an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            interval_hours (int): Interval in hours between analyses
            
        Returns:
            bool: True if scheduled successfully, False otherwise
        """
        # In a real application, this would set up a scheduled task
        # For this example, we'll just return True
        return True