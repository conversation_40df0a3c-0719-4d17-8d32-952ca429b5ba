"""
Pydantic schemas for API request/response models.
"""
from pydantic import BaseModel, EmailStr, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class UserRole(str, Enum):
    """User role enumeration."""
    admin = "admin"
    lab_manager = "lab_manager"
    researcher = "researcher"
    student = "student"


class ExperimentStatus(str, Enum):
    """Experiment status enumeration."""
    planned = "planned"
    in_progress = "in_progress"
    completed = "completed"
    cancelled = "cancelled"


# Authentication schemas
class LoginRequest(BaseModel):
    """Login request schema."""
    username: str
    password: str


class TokenResponse(BaseModel):
    """Token response schema."""
    access_token: str
    token_type: str
    user: 'UserResponse'


# User schemas
class UserBase(BaseModel):
    """Base user schema."""
    username: str
    email: EmailStr
    full_name: str
    role: UserRole


class CreateUserRequest(UserBase):
    """Create user request schema."""
    password: str
    
    @validator('password')
    def validate_password(cls, v):
        """Validate password complexity."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        if not any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in v):
            raise ValueError('Password must contain at least one special character')
        return v


class UpdateUserRequest(BaseModel):
    """Update user request schema."""
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None


class UserResponse(UserBase):
    """User response schema."""
    id: int
    created_at: datetime
    last_login: Optional[datetime] = None
    is_active: bool
    
    class Config:
        from_attributes = True


# Inventory schemas
class InventoryItemBase(BaseModel):
    """Base inventory item schema."""
    name: str
    category: str
    quantity: float
    unit: str
    location: Optional[str] = None
    min_quantity: Optional[float] = None
    expiry_date: Optional[str] = None
    barcode: Optional[str] = None
    notes: Optional[str] = None


class CreateInventoryItemRequest(InventoryItemBase):
    """Create inventory item request schema."""
    pass


class UpdateInventoryItemRequest(BaseModel):
    """Update inventory item request schema."""
    name: Optional[str] = None
    category: Optional[str] = None
    quantity: Optional[float] = None
    unit: Optional[str] = None
    location: Optional[str] = None
    min_quantity: Optional[float] = None
    expiry_date: Optional[str] = None
    barcode: Optional[str] = None
    notes: Optional[str] = None


class InventoryItemResponse(InventoryItemBase):
    """Inventory item response schema."""
    id: int
    added_by: int
    added_at: datetime
    last_updated: Optional[datetime] = None
    
    class Config:
        from_attributes = True


# Experiment schemas
class ExperimentBase(BaseModel):
    """Base experiment schema."""
    title: str
    description: str
    status: ExperimentStatus = ExperimentStatus.planned
    expected_start_date: Optional[str] = None
    expected_end_date: Optional[str] = None
    objectives: Optional[List[str]] = []
    methodology: Optional[str] = None
    materials: Optional[List[str]] = []
    safety_notes: Optional[str] = None


class CreateExperimentRequest(ExperimentBase):
    """Create experiment request schema."""
    pass


class UpdateExperimentRequest(BaseModel):
    """Update experiment request schema."""
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[ExperimentStatus] = None
    expected_start_date: Optional[str] = None
    expected_end_date: Optional[str] = None
    objectives: Optional[List[str]] = None
    methodology: Optional[str] = None
    materials: Optional[List[str]] = None
    safety_notes: Optional[str] = None


class ExperimentResponse(ExperimentBase):
    """Experiment response schema."""
    id: int
    created_by: int
    created_at: datetime
    last_updated: datetime
    
    class Config:
        from_attributes = True


# Booking schemas
class BookingBase(BaseModel):
    """Base booking schema."""
    resource_type: str
    resource_id: int
    start_time: datetime
    end_time: datetime
    purpose: str
    notes: Optional[str] = None


class CreateBookingRequest(BookingBase):
    """Create booking request schema."""
    pass


class BookingResponse(BookingBase):
    """Booking response schema."""
    id: int
    user_id: int
    created_at: datetime
    status: str
    
    class Config:
        from_attributes = True


# Safety schemas
class SafetyIncidentBase(BaseModel):
    """Base safety incident schema."""
    title: str
    description: str
    severity: str
    status: str = "open"
    resolution: Optional[str] = None


class CreateSafetyIncidentRequest(SafetyIncidentBase):
    """Create safety incident request schema."""
    pass


class SafetyIncidentResponse(SafetyIncidentBase):
    """Safety incident response schema."""
    id: int
    reported_by: int
    reported_at: datetime
    resolved_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


# Report schemas
class ReportRequest(BaseModel):
    """Report request schema."""
    report_type: str
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    filters: Optional[Dict[str, Any]] = {}


class ReportResponse(BaseModel):
    """Report response schema."""
    report_type: str
    generated_at: datetime
    data: Dict[str, Any]
    
    class Config:
        from_attributes = True


# Error schemas
class ErrorResponse(BaseModel):
    """Error response schema."""
    detail: str
    error_code: Optional[str] = None
    timestamp: datetime = datetime.now()


# Update forward references
TokenResponse.model_rebuild()
