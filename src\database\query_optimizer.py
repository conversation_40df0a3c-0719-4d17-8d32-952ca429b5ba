"""
Database query optimization utilities for improved performance.
"""
import sqlite3
import logging
from typing import List, Dict, Any, Optional
from contextlib import contextmanager
import time
from functools import wraps

logger = logging.getLogger(__name__)


class QueryOptimizer:
    """
    Database query optimizer for the Science Laboratory Management System.
    Provides optimized queries, caching, and performance monitoring.
    """
    
    def __init__(self, db_manager):
        """
        Initialize the query optimizer.
        
        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager
        self.query_cache = {}
        self.query_stats = {}
        self.cache_enabled = True
        self.max_cache_size = 1000
    
    def enable_cache(self, enabled: bool = True):
        """Enable or disable query caching."""
        self.cache_enabled = enabled
        if not enabled:
            self.clear_cache()
    
    def clear_cache(self):
        """Clear the query cache."""
        self.query_cache.clear()
        logger.info("Query cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "cache_size": len(self.query_cache),
            "max_cache_size": self.max_cache_size,
            "cache_enabled": self.cache_enabled,
            "query_stats": self.query_stats
        }
    
    @contextmanager
    def measure_query_time(self, query_name: str):
        """Context manager to measure query execution time."""
        start_time = time.time()
        try:
            yield
        finally:
            execution_time = time.time() - start_time
            if query_name not in self.query_stats:
                self.query_stats[query_name] = {
                    "count": 0,
                    "total_time": 0,
                    "avg_time": 0,
                    "min_time": float('inf'),
                    "max_time": 0
                }
            
            stats = self.query_stats[query_name]
            stats["count"] += 1
            stats["total_time"] += execution_time
            stats["avg_time"] = stats["total_time"] / stats["count"]
            stats["min_time"] = min(stats["min_time"], execution_time)
            stats["max_time"] = max(stats["max_time"], execution_time)
    
    def cached_query(self, cache_key: str = None, ttl: int = 300):
        """
        Decorator for caching query results.
        
        Args:
            cache_key: Custom cache key (optional)
            ttl: Time to live in seconds
        """
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                if not self.cache_enabled:
                    return func(*args, **kwargs)
                
                # Generate cache key
                key = cache_key or f"{func.__name__}_{hash(str(args) + str(kwargs))}"
                
                # Check cache
                if key in self.query_cache:
                    cached_result, timestamp = self.query_cache[key]
                    if time.time() - timestamp < ttl:
                        logger.debug(f"Cache hit for {key}")
                        return cached_result
                    else:
                        # Remove expired entry
                        del self.query_cache[key]
                
                # Execute query and cache result
                result = func(*args, **kwargs)
                
                # Manage cache size
                if len(self.query_cache) >= self.max_cache_size:
                    # Remove oldest entry
                    oldest_key = min(self.query_cache.keys(), 
                                   key=lambda k: self.query_cache[k][1])
                    del self.query_cache[oldest_key]
                
                self.query_cache[key] = (result, time.time())
                logger.debug(f"Cached result for {key}")
                return result
            
            return wrapper
        return decorator
    
    def create_indexes(self):
        """Create database indexes for improved query performance."""
        indexes = [
            # User table indexes
            "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
            "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
            "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",
            "CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active)",
            
            # Inventory table indexes
            "CREATE INDEX IF NOT EXISTS idx_inventory_category ON inventory_items(category)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_location ON inventory_items(location)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_barcode ON inventory_items(barcode)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_expiry ON inventory_items(expiry_date)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_quantity ON inventory_items(quantity)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_added_by ON inventory_items(added_by)",
            
            # Experiment table indexes
            "CREATE INDEX IF NOT EXISTS idx_experiments_status ON experiments(status)",
            "CREATE INDEX IF NOT EXISTS idx_experiments_created_by ON experiments(created_by)",
            "CREATE INDEX IF NOT EXISTS idx_experiments_dates ON experiments(start_date, end_date)",
            
            # Booking table indexes
            "CREATE INDEX IF NOT EXISTS idx_bookings_user ON bookings(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_bookings_resource ON bookings(resource_type, resource_id)",
            "CREATE INDEX IF NOT EXISTS idx_bookings_time ON bookings(start_time, end_time)",
            "CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings(status)",
            
            # Audit log indexes
            "CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_logs(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_logs(action)",
            "CREATE INDEX IF NOT EXISTS idx_audit_table ON audit_logs(table_name)",
            "CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_logs(timestamp)",
        ]
        
        self.db_manager.connect()
        try:
            for index_sql in indexes:
                with self.measure_query_time("create_index"):
                    self.db_manager.cursor.execute(index_sql)
            self.db_manager.conn.commit()
            logger.info("Database indexes created successfully")
        except sqlite3.Error as e:
            logger.error(f"Error creating indexes: {str(e)}")
        finally:
            self.db_manager.disconnect()
    
    @cached_query(ttl=600)  # Cache for 10 minutes
    def get_inventory_summary(self) -> Dict[str, Any]:
        """Get optimized inventory summary."""
        with self.measure_query_time("inventory_summary"):
            self.db_manager.connect()
            try:
                # Optimized query with aggregations
                query = """
                    SELECT 
                        category,
                        COUNT(*) as item_count,
                        SUM(quantity) as total_quantity,
                        COUNT(CASE WHEN quantity <= min_quantity THEN 1 END) as low_stock_count,
                        COUNT(CASE WHEN expiry_date <= date('now', '+30 days') THEN 1 END) as expiring_soon_count
                    FROM inventory_items 
                    GROUP BY category
                    ORDER BY category
                """
                
                self.db_manager.cursor.execute(query)
                results = self.db_manager.cursor.fetchall()
                
                summary = {
                    "categories": [dict(row) for row in results],
                    "total_items": sum(row["item_count"] for row in results),
                    "total_low_stock": sum(row["low_stock_count"] for row in results),
                    "total_expiring_soon": sum(row["expiring_soon_count"] for row in results)
                }
                
                return summary
            finally:
                self.db_manager.disconnect()
    
    @cached_query(ttl=300)  # Cache for 5 minutes
    def get_user_activity_summary(self, days: int = 30) -> List[Dict[str, Any]]:
        """Get optimized user activity summary."""
        with self.measure_query_time("user_activity_summary"):
            self.db_manager.connect()
            try:
                query = """
                    SELECT 
                        u.id,
                        u.username,
                        u.full_name,
                        u.role,
                        COUNT(DISTINCT al.id) as total_actions,
                        COUNT(DISTINCT CASE WHEN al.action = 'login' THEN al.id END) as login_count,
                        MAX(al.timestamp) as last_activity
                    FROM users u
                    LEFT JOIN audit_logs al ON u.id = al.user_id 
                        AND al.timestamp >= datetime('now', '-{} days')
                    WHERE u.is_active = 1
                    GROUP BY u.id, u.username, u.full_name, u.role
                    ORDER BY total_actions DESC
                """.format(days)
                
                self.db_manager.cursor.execute(query)
                return [dict(row) for row in self.db_manager.cursor.fetchall()]
            finally:
                self.db_manager.disconnect()
    
    @cached_query(ttl=1800)  # Cache for 30 minutes
    def get_experiment_statistics(self) -> Dict[str, Any]:
        """Get optimized experiment statistics."""
        with self.measure_query_time("experiment_statistics"):
            self.db_manager.connect()
            try:
                query = """
                    SELECT 
                        status,
                        COUNT(*) as count,
                        AVG(CASE 
                            WHEN end_date IS NOT NULL AND start_date IS NOT NULL 
                            THEN julianday(end_date) - julianday(start_date) 
                        END) as avg_duration_days
                    FROM experiments
                    GROUP BY status
                """
                
                self.db_manager.cursor.execute(query)
                status_stats = [dict(row) for row in self.db_manager.cursor.fetchall()]
                
                # Get monthly experiment creation trend
                trend_query = """
                    SELECT 
                        strftime('%Y-%m', created_at) as month,
                        COUNT(*) as count
                    FROM experiments
                    WHERE created_at >= datetime('now', '-12 months')
                    GROUP BY strftime('%Y-%m', created_at)
                    ORDER BY month
                """
                
                self.db_manager.cursor.execute(trend_query)
                trend_data = [dict(row) for row in self.db_manager.cursor.fetchall()]
                
                return {
                    "status_breakdown": status_stats,
                    "monthly_trend": trend_data,
                    "total_experiments": sum(stat["count"] for stat in status_stats)
                }
            finally:
                self.db_manager.disconnect()
    
    def optimize_database(self):
        """Run database optimization tasks."""
        self.db_manager.connect()
        try:
            # Analyze tables for query optimization
            with self.measure_query_time("analyze_database"):
                self.db_manager.cursor.execute("ANALYZE")
            
            # Vacuum database to reclaim space
            with self.measure_query_time("vacuum_database"):
                self.db_manager.cursor.execute("VACUUM")
            
            # Update table statistics
            with self.measure_query_time("update_statistics"):
                self.db_manager.cursor.execute("PRAGMA optimize")
            
            logger.info("Database optimization completed")
        except sqlite3.Error as e:
            logger.error(f"Error optimizing database: {str(e)}")
        finally:
            self.db_manager.disconnect()
    
    def get_slow_queries(self, min_time: float = 1.0) -> List[Dict[str, Any]]:
        """Get queries that are slower than the specified threshold."""
        slow_queries = []
        for query_name, stats in self.query_stats.items():
            if stats["avg_time"] > min_time:
                slow_queries.append({
                    "query_name": query_name,
                    "avg_time": stats["avg_time"],
                    "max_time": stats["max_time"],
                    "count": stats["count"]
                })
        
        return sorted(slow_queries, key=lambda x: x["avg_time"], reverse=True)
