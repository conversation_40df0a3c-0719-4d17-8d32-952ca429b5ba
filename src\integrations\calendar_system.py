import logging
import os
import json
import uuid
from datetime import datetime, timedelta
import icalendar
import pytz
import requests
from urllib.parse import quote

class CalendarSystem:
    """
    Calendar integration system for the Science Laboratory Management System.
    Handles calendar exports, imports, and synchronization with external calendars.
    """
    
    def __init__(self, config):
        """
        Initialize the calendar system.
        
        Args:
            config: The application configuration
        """
        self.config = config
        self.logger = logging.getLogger("calendar_system")
        
        # Create calendar directory if it doesn't exist
        self.calendar_dir = os.path.join(os.getcwd(), "data", "calendars")
        os.makedirs(self.calendar_dir, exist_ok=True)
        
        # Set timezone
        app_config = config.get("app", {})
        self.timezone = pytz.timezone(app_config.get("timezone", "UTC"))
    
    def create_ical_event(self, event):
        """
        Create an iCalendar event.
        
        Args:
            event (dict): Event data
            
        Returns:
            icalendar.Event: The iCalendar event
        """
        cal_event = icalendar.Event()
        
        # Set UID
        cal_event.add('uid', event.get('id', str(uuid.uuid4())))
        
        # Set summary (title)
        cal_event.add('summary', event.get('title', 'Lab Reservation'))
        
        # Set description
        description = event.get('description', '')
        if event.get('equipment'):
            description += "\n\nEquipment:\n"
            for item in event.get('equipment', []):
                description += f"- {item.get('name')} (Qty: {item.get('quantity')})\n"
        cal_event.add('description', description)
        
        # Set location
        cal_event.add('location', event.get('location', 'Laboratory'))
        
        # Set start and end times
        start_time = event.get('start_time')
        end_time = event.get('end_time')
        
        if isinstance(start_time, str):
            start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        
        if isinstance(end_time, str):
            end_time = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
        
        cal_event.add('dtstart', start_time)
        cal_event.add('dtend', end_time)
        
        # Set created and last modified
        now = datetime.now(pytz.utc)
        cal_event.add('created', now)
        cal_event.add('last-modified', now)
        
        # Set status
        cal_event.add('status', 'CONFIRMED')
        
        # Set organizer
        if event.get('organizer'):
            cal_event.add('organizer', f"mailto:{event.get('organizer')}")
        
        # Set attendees
        for attendee in event.get('attendees', []):
            cal_event.add('attendee', f"mailto:{attendee}")
        
        # Set categories
        cal_event.add('categories', 'Lab Reservation')
        
        return cal_event
    
    def create_ical_calendar(self, events, calendar_name="Lab Reservations"):
        """
        Create an iCalendar calendar with multiple events.
        
        Args:
            events (list): List of event data dictionaries
            calendar_name (str): Name of the calendar
            
        Returns:
            icalendar.Calendar: The iCalendar calendar
        """
        cal = icalendar.Calendar()
        
        # Set required properties
        cal.add('prodid', '-//Science Laboratory Management System//EN')
        cal.add('version', '2.0')
        cal.add('calscale', 'GREGORIAN')
        cal.add('method', 'PUBLISH')
        cal.add('x-wr-calname', calendar_name)
        cal.add('x-wr-timezone', str(self.timezone))
        
        # Add events
        for event_data in events:
            event = self.create_ical_event(event_data)
            cal.add_component(event)
        
        return cal
    
    def export_calendar(self, events, filename=None, calendar_name="Lab Reservations"):
        """
        Export events to an iCalendar file.
        
        Args:
            events (list): List of event data dictionaries
            filename (str, optional): Output filename
            calendar_name (str): Name of the calendar
            
        Returns:
            str: Path to the exported calendar file
        """
        try:
            # Create calendar
            cal = self.create_ical_calendar(events, calendar_name)
            
            # Generate filename if not provided
            if not filename:
                filename = f"lab_calendar_{datetime.now().strftime('%Y%m%d_%H%M%S')}.ics"
            
            # Ensure it has .ics extension
            if not filename.endswith('.ics'):
                filename += '.ics'
            
            # Write to file
            filepath = os.path.join(self.calendar_dir, filename)
            with open(filepath, 'wb') as f:
                f.write(cal.to_ical())
            
            self.logger.info(f"Calendar exported to {filepath}")
            return filepath
        
        except Exception as e:
            self.logger.error(f"Error exporting calendar: {str(e)}")
            return None
    
    def generate_google_calendar_link(self, event):
        """
        Generate a Google Calendar event link.
        
        Args:
            event (dict): Event data
            
        Returns:
            str: Google Calendar event link
        """
        try:
            # Format dates
            start_time = event.get('start_time')
            end_time = event.get('end_time')
            
            if isinstance(start_time, datetime):
                start_str = start_time.strftime('%Y%m%dT%H%M%SZ')
            else:
                start_str = datetime.fromisoformat(start_time.replace('Z', '+00:00')).strftime('%Y%m%dT%H%M%SZ')
            
            if isinstance(end_time, datetime):
                end_str = end_time.strftime('%Y%m%dT%H%M%SZ')
            else:
                end_str = datetime.fromisoformat(end_time.replace('Z', '+00:00')).strftime('%Y%m%dT%H%M%SZ')
            
            # Build description
            description = event.get('description', '')
            if event.get('equipment'):
                description += "\n\nEquipment:\n"
                for item in event.get('equipment', []):
                    description += f"- {item.get('name')} (Qty: {item.get('quantity')})\n"
            
            # Build URL
            base_url = "https://calendar.google.com/calendar/render"
            params = {
                "action": "TEMPLATE",
                "text": quote(event.get('title', 'Lab Reservation')),
                "dates": f"{start_str}/{end_str}",
                "details": quote(description),
                "location": quote(event.get('location', 'Laboratory')),
                "sf": "true",
                "output": "xml"
            }
            
            url = f"{base_url}?{'&'.join([f'{k}={v}' for k, v in params.items()])}"
            return url
        
        except Exception as e:
            self.logger.error(f"Error generating Google Calendar link: {str(e)}")
            return None
    
    def generate_outlook_calendar_link(self, event):
        """
        Generate a Microsoft Outlook calendar event link.
        
        Args:
            event (dict): Event data
            
        Returns:
            str: Outlook calendar event link
        """
        try:
            # Format dates
            start_time = event.get('start_time')
            end_time = event.get('end_time')
            
            if isinstance(start_time, datetime):
                start_str = start_time.strftime('%Y-%m-%dT%H:%M:%S')
            else:
                start_str = datetime.fromisoformat(start_time.replace('Z', '+00:00')).strftime('%Y-%m-%dT%H:%M:%S')
            
            if isinstance(end_time, datetime):
                end_str = end_time.strftime('%Y-%m-%dT%H:%M:%S')
            else:
                end_str = datetime.fromisoformat(end_time.replace('Z', '+00:00')).strftime('%Y-%m-%dT%H:%M:%S')
            
            # Build description
            description = event.get('description', '')
            if event.get('equipment'):
                description += "\n\nEquipment:\n"
                for item in event.get('equipment', []):
                    description += f"- {item.get('name')} (Qty: {item.get('quantity')})\n"
            
            # Build URL
            base_url = "https://outlook.live.com/calendar/0/deeplink/compose"
            params = {
                "subject": quote(event.get('title', 'Lab Reservation')),
                "startdt": quote(start_str),
                "enddt": quote(end_str),
                "body": quote(description),
                "location": quote(event.get('location', 'Laboratory')),
                "path": "/calendar/action/compose"
            }
            
            url = f"{base_url}?{'&'.join([f'{k}={v}' for k, v in params.items()])}"
            return url
        
        except Exception as e:
            self.logger.error(f"Error generating Outlook Calendar link: {str(e)}")
            return None
    
    def get_calendar_links(self, event):
        """
        Get calendar links for various platforms.
        
        Args:
            event (dict): Event data
            
        Returns:
            dict: Calendar links for different platforms
        """
        return {
            "ical": self.export_calendar([event], f"event_{event.get('id', uuid.uuid4())}.ics"),
            "google": self.generate_google_calendar_link(event),
            "outlook": self.generate_outlook_calendar_link(event)
        }
    
    def get_storage_stats(self):
        """
        Get storage statistics for calendars.
        
        Returns:
            dict: Storage statistics
        """
        try:
            # Count files
            file_count = 0
            total_size = 0
            
            for root, dirs, files in os.walk(self.calendar_dir):
                for file in files:
                    if file.endswith('.ics'):
                        file_count += 1
                        file_path = os.path.join(root, file)
                        total_size += os.path.getsize(file_path)
            
            return {
                "file_count": file_count,
                "total_size": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2)
            }
        
        except Exception as e:
            self.logger.error(f"Error getting storage stats: {str(e)}")
            return {
                "file_count": 0,
                "total_size": 0,
                "total_size_mb": 0,
                "error": str(e)
            }