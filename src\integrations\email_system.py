import os
import logging
import threading
import queue
import time
import smtplib
import mimetypes
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication
from email.utils import formatdate, make_msgid
import jinja2
import re
from bs4 import BeautifulSoup

class EmailSystem:
    """
    Email system for the Science Laboratory Management System.
    Handles email sending, templates, and queuing.
    """
    
    def __init__(self, config):
        """
        Initialize the email system.
        
        Args:
            config: The application configuration
        """
        self.config = config
        self.logger = logging.getLogger("email_system")
        
        # Get email configuration
        email_config = config.get("email", {})
        self.enabled = email_config.get("enabled", False)
        self.smtp_server = email_config.get("smtp_server", "")
        self.smtp_port = email_config.get("smtp_port", 587)
        self.smtp_username = email_config.get("smtp_username", "")
        self.smtp_password = email_config.get("smtp_password", "")
        self.from_address = email_config.get("from_address", "")
        self.use_tls = email_config.get("use_tls", True)
        
        # Set up template environment
        template_dir = os.path.join(os.getcwd(), "templates", "email")
        if not os.path.exists(template_dir):
            os.makedirs(template_dir, exist_ok=True)
        
        self.template_env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(template_dir),
            autoescape=jinja2.select_autoescape(['html', 'xml'])
        )
        
        # Create email queue
        self.email_queue = queue.Queue()
        
        # Create email worker thread
        self.worker_thread = None
        self.stop_worker = threading.Event()
        
        # Start worker if enabled
        if self.enabled:
            self.start_worker()
    
    def start_worker(self):
        """Start the email worker thread."""
        if not self.worker_thread or not self.worker_thread.is_alive():
            self.stop_worker.clear()
            self.worker_thread = threading.Thread(target=self.email_worker, daemon=True)
            self.worker_thread.start()
    
    def stop_worker(self):
        """Stop the email worker thread."""
        if self.worker_thread and self.worker_thread.is_alive():
            self.stop_worker.set()
            self.worker_thread.join(timeout=5)
    
    def email_worker(self):
        """Email worker thread that processes the email queue."""
        self.logger.info("Email worker thread started")
        
        while not self.stop_worker.is_set():
            try:
                # Get email from queue with timeout
                try:
                    email_data = self.email_queue.get(timeout=1)
                except queue.Empty:
                    continue
                
                # Process email
                try:
                    self.logger.info(f"Sending email to {email_data['to']}: {email_data['subject']}")
                    self.send_email_direct(
                        email_data["to"],
                        email_data["subject"],
                        email_data["body"],
                        email_data.get("html_body"),
                        email_data.get("cc"),
                        email_data.get("bcc"),
                        email_data.get("attachments")
                    )
                    self.logger.info(f"Email sent to {email_data['to']}")
                except Exception as e:
                    self.logger.error(f"Error sending email: {str(e)}")
                
                # Mark task as done
                self.email_queue.task_done()
            
            except Exception as e:
                self.logger.error(f"Error in email worker: {str(e)}")
        
        self.logger.info("Email worker thread stopped")
    
    def send_email(self, to, subject, body, html_body=None, cc=None, bcc=None, attachments=None, priority=1):
        """
        Queue an email to be sent.
        
        Args:
            to (str or list): Recipient email address(es)
            subject (str): Email subject
            body (str): Email body (plain text)
            html_body (str, optional): Email body (HTML)
            cc (str or list, optional): CC recipient(s)
            bcc (str or list, optional): BCC recipient(s)
            attachments (list, optional): List of attachment file paths
            priority (int, optional): Priority (1-5, 1 is highest)
            
        Returns:
            bool: True if email was queued, False otherwise
        """
        if not self.enabled:
            self.logger.warning("Email system is disabled")
            return False
        
        try:
            # Validate email addresses
            if not to:
                self.logger.error("No recipient specified")
                return False
            
            # Queue email
            email_data = {
                "to": to,
                "subject": subject,
                "body": body,
                "html_body": html_body,
                "cc": cc,
                "bcc": bcc,
                "attachments": attachments,
                "priority": priority,
                "timestamp": time.time()
            }
            
            self.email_queue.put(email_data)
            self.logger.debug(f"Email queued: {subject}")
            
            # Start worker if not running
            if not self.worker_thread or not self.worker_thread.is_alive():
                self.start_worker()
            
            return True
        
        except Exception as e:
            self.logger.error(f"Error queueing email: {str(e)}")
            return False
    
    def send_email_direct(self, to, subject, body, html_body=None, cc=None, bcc=None, attachments=None):
        """
        Send an email directly (not queued).
        
        Args:
            to (str or list): Recipient email address(es)
            subject (str): Email subject
            body (str): Email body (plain text)
            html_body (str, optional): Email body (HTML)
            cc (str or list, optional): CC recipient(s)
            bcc (str or list, optional): BCC recipient(s)
            attachments (list, optional): List of attachment file paths
            
        Returns:
            bool: True if email was sent, False otherwise
        """
        if not self.enabled:
            self.logger.warning("Email system is disabled")
            return False
        
        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.from_address
            msg['Date'] = formatdate(localtime=True)
            msg['Message-ID'] = make_msgid(domain=self.from_address.split('@')[1] if '@' in self.from_address else 'labsystem.local')
            
            # Add recipients
            if isinstance(to, list):
                msg['To'] = ', '.join(to)
            else:
                msg['To'] = to
            
            if cc:
                if isinstance(cc, list):
                    msg['Cc'] = ', '.join(cc)
                else:
                    msg['Cc'] = cc
            
            if bcc:
                if isinstance(bcc, list):
                    msg['Bcc'] = ', '.join(bcc)
                else:
                    msg['Bcc'] = bcc
            
            # Add text part
            text_part = MIMEText(body, 'plain')
            msg.attach(text_part)
            
            # Add HTML part if provided
            if html_body:
                html_part = MIMEText(html_body, 'html')
                msg.attach(html_part)
            
            # Add attachments
            if attachments:
                for attachment in attachments:
                    if os.path.isfile(attachment):
                        filename = os.path.basename(attachment)
                        mimetype, _ = mimetypes.guess_type(attachment)
                        
                        if mimetype is None:
                            mimetype = 'application/octet-stream'
                        
                        with open(attachment, 'rb') as f:
                            attachment_part = MIMEApplication(f.read(), _subtype=mimetype.split('/')[1])
                        
                        attachment_part.add_header('Content-Disposition', 'attachment', filename=filename)
                        msg.attach(attachment_part)
                    else:
                        self.logger.warning(f"Attachment not found: {attachment}")
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls()
                
                if self.smtp_username and self.smtp_password:
                    server.login(self.smtp_username, self.smtp_password)
                
                # Get all recipients
                all_recipients = []
                if isinstance(to, list):
                    all_recipients.extend(to)
                else:
                    all_recipients.append(to)
                
                if cc:
                    if isinstance(cc, list):
                        all_recipients.extend(cc)
                    else:
                        all_recipients.append(cc)
                
                if bcc:
                    if isinstance(bcc, list):
                        all_recipients.extend(bcc)
                    else:
                        all_recipients.append(bcc)
                
                server.sendmail(self.from_address, all_recipients, msg.as_string())
            
            return True
        
        except Exception as e:
            self.logger.error(f"Error sending email: {str(e)}")
            return False
    
    def send_template_email(self, to, template_name, template_data, subject=None, cc=None, bcc=None, attachments=None):
        """
        Send an email using a template.
        
        Args:
            to (str or list): Recipient email address(es)
            template_name (str): Name of the template
            template_data (dict): Data for the template
            subject (str, optional): Email subject (if None, extracted from template)
            cc (str or list, optional): CC recipient(s)
            bcc (str or list, optional): BCC recipient(s)
            attachments (list, optional): List of attachment file paths
            
        Returns:
            bool: True if email was queued, False otherwise
        """
        try:
            # Load template
            template_path = f"{template_name}.html"
            template = self.template_env.get_template(template_path)
            
            # Render template
            html_body = template.render(**template_data)
            
            # Extract subject from template if not provided
            if subject is None:
                soup = BeautifulSoup(html_body, 'html.parser')
                title = soup.find('title')
                if title:
                    subject = title.string
                else:
                    subject = f"Lab System: {template_name.replace('_', ' ').title()}"
            
            # Convert HTML to plain text
            plain_body = self.html_to_plain(html_body)
            
            # Send email
            return self.send_email(to, subject, plain_body, html_body, cc, bcc, attachments)
        
        except Exception as e:
            self.logger.error(f"Error sending template email: {str(e)}")
            return False
    
    def html_to_plain(self, html):
        """
        Convert HTML to plain text.
        
        Args:
            html (str): HTML content
            
        Returns:
            str: Plain text content
        """
        try:
            # Parse HTML
            soup = BeautifulSoup(html, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.extract()
            
            # Get text
            text = soup.get_text()
            
            # Break into lines and remove leading and trailing space on each
            lines = (line.strip() for line in text.splitlines())
            
            # Break multi-headlines into a line each
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            
            # Remove blank lines
            text = '\n'.join(chunk for chunk in chunks if chunk)
            
            return text
        
        except Exception as e:
            self.logger.error(f"Error converting HTML to plain text: {str(e)}")
            return re.sub('<[^<]+?>', '', html)  # Simple fallback
    
    def create_default_templates(self):
        """Create default email templates."""
        try:
            template_dir = os.path.join(os.getcwd(), "templates", "email")
            if not os.path.exists(template_dir):
                os.makedirs(template_dir, exist_ok=True)
            
            # Welcome template
            welcome_template = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Welcome to the Science Laboratory Management System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            background-color: #0066cc;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .footer {
            background-color: #f5f5f5;
            padding: 10px 20px;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        .button {
            display: inline-block;
            background-color: #0066cc;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Welcome to the Science Laboratory Management System</h1>
    </div>
    <div class="content">
        <p>Hello {{ name }},</p>
        
        <p>Welcome to the Science Laboratory Management System! Your account has been created successfully.</p>
        
        <p>Here are your account details:</p>
        <ul>
            <li><strong>Username:</strong> {{ username }}</li>
            <li><strong>Role:</strong> {{ role }}</li>
        </ul>
        
        <p>You can access the system using the link below:</p>
        
        <p><a href="{{ login_url }}" class="button">Login to the System</a></p>
        
        <p>If you have any questions or need assistance, please contact the system administrator.</p>
        
        <p>Best regards,<br>
        Science Laboratory Management Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>&copy; {{ current_year }} Science Laboratory Management System</p>
    </div>
</body>
</html>"""
            
            # Reservation confirmation template
            reservation_template = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Lab Reservation Confirmation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            background-color: #0066cc;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .footer {
            background-color: #f5f5f5;
            padding: 10px 20px;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        .reservation-details {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .button {
            display: inline-block;
            background-color: #0066cc;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Lab Reservation Confirmation</h1>
    </div>
    <div class="content">
        <p>Hello {{ name }},</p>
        
        <p>Your lab reservation has been confirmed. Here are the details:</p>
        
        <div class="reservation-details">
            <p><strong>Lab Room:</strong> {{ room_name }}</p>
            <p><strong>Date:</strong> {{ date }}</p>
            <p><strong>Time:</strong> {{ start_time }} - {{ end_time }}</p>
            <p><strong>Purpose:</strong> {{ purpose }}</p>
            {% if equipment %}
            <p><strong>Equipment:</strong></p>
            <ul>
                {% for item in equipment %}
                <li>{{ item.name }} ({{ item.quantity }})</li>
                {% endfor %}
            </ul>
            {% endif %}
            <p><strong>Reservation ID:</strong> {{ reservation_id }}</p>
        </div>
        
        <p>You can view or manage your reservation using the link below:</p>
        
        <p><a href="{{ reservation_url }}" class="button">View Reservation</a></p>
        
        <p>Please arrive on time and follow all laboratory safety protocols.</p>
        
        <p>If you need to cancel or modify your reservation, please do so at least 24 hours in advance.</p>
        
        <p>Best regards,<br>
        Science Laboratory Management Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>&copy; {{ current_year }} Science Laboratory Management System</p>
    </div>
</body>
</html>"""
            
            # Password reset template
            password_reset_template = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Password Reset Request</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            background-color: #0066cc;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .footer {
            background-color: #f5f5f5;
            padding: 10px 20px;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        .reset-code {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            font-size: 24px;
            text-align: center;
            letter-spacing: 5px;
            font-family: monospace;
        }
        .button {
            display: inline-block;
            background-color: #0066cc;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 20px 0;
        }
        .warning {
            color: #cc0000;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Password Reset Request</h1>
    </div>
    <div class="content">
        <p>Hello {{ name }},</p>
        
        <p>We received a request to reset your password for the Science Laboratory Management System.</p>
        
        <p>To reset your password, please click the button below:</p>
        
        <p><a href="{{ reset_url }}" class="button">Reset Password</a></p>
        
        <p>Alternatively, you can enter the following code on the password reset page:</p>
        
        <div class="reset-code">{{ reset_code }}</div>
        
        <p class="warning">This code will expire in {{ expiry_time }} minutes.</p>
        
        <p>If you did not request a password reset, please ignore this email or contact the system administrator if you have concerns.</p>
        
        <p>Best regards,<br>
        Science Laboratory Management Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>&copy; {{ current_year }} Science Laboratory Management System</p>
    </div>
</body>
</html>"""
            
            # Inventory alert template
            inventory_alert_template = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Inventory Alert: Low Stock Items</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            background-color: #cc6600;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .footer {
            background-color: #f5f5f5;
            padding: 10px 20px;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        .inventory-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .inventory-table th, .inventory-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .inventory-table th {
            background-color: #f2f2f2;
        }
        .critical {
            color: #cc0000;
            font-weight: bold;
        }
        .warning {
            color: #cc6600;
        }
        .button {
            display: inline-block;
            background-color: #0066cc;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Inventory Alert: Low Stock Items</h1>
    </div>
    <div class="content">
        <p>Hello {{ name }},</p>
        
        <p>This is an automated alert to inform you that the following items in the laboratory inventory are running low:</p>
        
        <table class="inventory-table">
            <tr>
                <th>Item Name</th>
                <th>Current Quantity</th>
                <th>Minimum Threshold</th>
                <th>Status</th>
            </tr>
            {% for item in low_stock_items %}
            <tr>
                <td>{{ item.name }}</td>
                <td>{{ item.quantity }} {{ item.unit }}</td>
                <td>{{ item.min_threshold }} {{ item.unit }}</td>
                <td class="{% if item.quantity == 0 %}critical{% elif item.quantity <= item.min_threshold / 2 %}critical{% else %}warning{% endif %}">
                    {% if item.quantity == 0 %}Out of Stock{% elif item.quantity <= item.min_threshold / 2 %}Critical{% else %}Low{% endif %}
                </td>
            </tr>
            {% endfor %}
        </table>
        
        <p>Please take appropriate action to restock these items to ensure laboratory operations are not disrupted.</p>
        
        <p><a href="{{ inventory_url }}" class="button">View Inventory</a></p>
        
        <p>Best regards,<br>
        Science Laboratory Management System</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>&copy; {{ current_year }} Science Laboratory Management System</p>
    </div>
</body>
</html>"""
            
            # Maintenance notification template
            maintenance_template = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Equipment Maintenance Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            background-color: #0066cc;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .footer {
            background-color: #f5f5f5;
            padding: 10px 20px;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        .maintenance-details {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .button {
            display: inline-block;
            background-color: #0066cc;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Equipment Maintenance Notification</h1>
    </div>
    <div class="content">
        <p>Hello {{ name }},</p>
        
        <p>This is to inform you that the following equipment is scheduled for maintenance:</p>
        
        <div class="maintenance-details">
            <p><strong>Equipment:</strong> {{ equipment_name }}</p>
            <p><strong>Asset ID:</strong> {{ asset_id }}</p>
            <p><strong>Location:</strong> {{ location }}</p>
            <p><strong>Maintenance Type:</strong> {{ maintenance_type }}</p>
            <p><strong>Scheduled Date:</strong> {{ scheduled_date }}</p>
            <p><strong>Estimated Duration:</strong> {{ duration }}</p>
            <p><strong>Technician:</strong> {{ technician }}</p>
            {% if notes %}
            <p><strong>Notes:</strong> {{ notes }}</p>
            {% endif %}
        </div>
        
        <p>During this maintenance period, the equipment will not be available for use. Please plan your laboratory activities accordingly.</p>
        
        <p>If you have any questions or need to reschedule, please contact the laboratory manager.</p>
        
        <p><a href="{{ maintenance_url }}" class="button">View Maintenance Schedule</a></p>
        
        <p>Best regards,<br>
        Science Laboratory Management Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>&copy; {{ current_year }} Science Laboratory Management System</p>
    </div>
</body>
</html>"""
            
            # Write templates to files
            templates = {
                "welcome": welcome_template,
                "reservation_confirmation": reservation_template,
                "password_reset": password_reset_template,
                "inventory_alert": inventory_alert_template,
                "maintenance_notification": maintenance_template
            }
            
            for name, content in templates.items():
                template_path = os.path.join(template_dir, f"{name}.html")
                if not os.path.exists(template_path):
                    with open(template_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    self.logger.info(f"Created email template: {name}")
            
            self.logger.info("Default email templates created")
            
        except Exception as e:
            self.logger.error(f"Error creating default templates: {str(e)}")
    
    def get_queue_size(self):
        """
        Get the current size of the email queue.
        
        Returns:
            int: Number of emails in the queue
        """
        return self.email_queue.qsize()
    
    def flush_queue(self):
        """
        Flush the email queue.
        
        Returns:
            int: Number of emails flushed
        """
        count = 0
        try:
            while not self.email_queue.empty():
                self.email_queue.get(block=False)
                self.email_queue.task_done()
                count += 1
            
            self.logger.info(f"Flushed {count} emails from queue")
            return count
        
        except Exception as e:
            self.logger.error(f"Error flushing email queue: {str(e)}")
            return count