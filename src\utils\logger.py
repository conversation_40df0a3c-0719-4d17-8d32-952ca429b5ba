import logging
import os
from datetime import datetime
import sys

def setup_logger(name, level=logging.INFO, log_file=None):
    """
    Set up a logger with the specified name and level.
    
    Args:
        name (str): The logger name
        level (int, optional): The logging level (default: INFO)
        log_file (str, optional): Path to log file (default: None)
        
    Returns:
        logging.Logger: The configured logger
    """
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # Create file handler if log file specified
    if log_file:
        # Create log directory if it doesn't exist
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        # Create file handler
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def setup_application_logger(app_name="lab_system", log_dir=None):
    """
    Set up the main application logger.
    
    Args:
        app_name (str, optional): The application name (default: lab_system)
        log_dir (str, optional): Directory for log files (default: logs)
        
    Returns:
        logging.Logger: The configured logger
    """
    # Set default log directory if not specified
    if not log_dir:
        log_dir = os.path.join(os.getcwd(), "logs")
    
    # Create log directory if it doesn't exist
    os.makedirs(log_dir, exist_ok=True)
    
    # Generate log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d")
    log_file = os.path.join(log_dir, f"{app_name}_{timestamp}.log")
    
    # Set up root logger
    root_logger = setup_logger("root", logging.INFO, log_file)
    
    # Configure logging levels for specific modules
    logging.getLogger("flet").setLevel(logging.WARNING)
    logging.getLogger("PIL").setLevel(logging.WARNING)
    
    return root_logger

class LoggerManager:
    """
    Manager for application loggers.
    Provides centralized access to loggers and log rotation.
    """
    
    def __init__(self, app_name="lab_system", log_dir=None, max_log_files=30):
        """
        Initialize the logger manager.
        
        Args:
            app_name (str, optional): The application name (default: lab_system)
            log_dir (str, optional): Directory for log files (default: logs)
            max_log_files (int, optional): Maximum number of log files to keep (default: 30)
        """
        # Set default log directory if not specified
        if not log_dir:
            log_dir = os.path.join(os.getcwd(), "logs")
        
        self.app_name = app_name
        self.log_dir = log_dir
        self.max_log_files = max_log_files
        
        # Create log directory if it doesn't exist
        os.makedirs(log_dir, exist_ok=True)
        
        # Set up root logger
        self.root_logger = setup_application_logger(app_name, log_dir)
        
        # Dictionary to store module loggers
        self.loggers = {}
        
        # Perform log rotation
        self.rotate_logs()
    
    def get_logger(self, name, level=logging.INFO):
        """
        Get a logger for a specific module.
        
        Args:
            name (str): The logger name
            level (int, optional): The logging level (default: INFO)
            
        Returns:
            logging.Logger: The configured logger
        """
        # Check if logger already exists
        if name in self.loggers:
            return self.loggers[name]
        
        # Generate log filename with module name
        timestamp = datetime.now().strftime("%Y%m%d")
        log_file = os.path.join(self.log_dir, f"{self.app_name}_{name}_{timestamp}.log")
        
        # Set up logger
        logger = setup_logger(name, level, log_file)
        
        # Store logger
        self.loggers[name] = logger
        
        return logger
    
    def rotate_logs(self):
        """
        Rotate log files, keeping only the most recent ones.
        """
        try:
            # Get all log files
            log_files = [f for f in os.listdir(self.log_dir) if f.startswith(self.app_name) and f.endswith(".log")]
            
            # Sort by modification time (oldest first)
            log_files.sort(key=lambda f: os.path.getmtime(os.path.join(self.log_dir, f)))
            
            # Delete oldest files if there are too many
            if len(log_files) > self.max_log_files:
                files_to_delete = log_files[:(len(log_files) - self.max_log_files)]
                
                for file in files_to_delete:
                    file_path = os.path.join(self.log_dir, file)
                    os.remove(file_path)
                    print(f"Deleted old log file: {file_path}")
        
        except Exception as e:
            print(f"Error rotating logs: {str(e)}")
    
    def archive_logs(self, archive_dir=None):
        """
        Archive log files to a separate directory.
        
        Args:
            archive_dir (str, optional): Directory for archived logs (default: logs/archive)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Set default archive directory if not specified
            if not archive_dir:
                archive_dir = os.path.join(self.log_dir, "archive")
            
            # Create archive directory if it doesn't exist
            os.makedirs(archive_dir, exist_ok=True)
            
            # Get all log files
            log_files = [f for f in os.listdir(self.log_dir) if f.endswith(".log") and os.path.isfile(os.path.join(self.log_dir, f))]
            
            # Get current timestamp for archive name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Import modules for archiving
            import shutil
            import zipfile
            
            # Create archive file
            archive_file = os.path.join(archive_dir, f"logs_{timestamp}.zip")
            
            with zipfile.ZipFile(archive_file, 'w') as zipf:
                for file in log_files:
                    file_path = os.path.join(self.log_dir, file)
                    zipf.write(file_path, arcname=file)
            
            return True
        
        except Exception as e:
            print(f"Error archiving logs: {str(e)}")
            return False