#!/usr/bin/env python
"""
Run script for the Integration Manager.
This script ensures the proper Python path is set up.
"""
import sys
import os

# Add the project root directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Now we can import using the project structure
from src.integrations.integration_manager import IntegrationManager

# Example usage
if __name__ == "__main__":
    # Create a simple configuration for testing
    config = {
        "email": {
            "enabled": False
        }
    }
    
    # Initialize the integration manager
    manager = IntegrationManager(config)
    
    # Get and print the component status
    status = manager.get_component_status()
    print("Integration Components Status:")
    print(status)
    
    # Shutdown the integration manager
    manager.shutdown()