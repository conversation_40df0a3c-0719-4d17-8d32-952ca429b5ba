"""
Authentication utilities for the REST API.
"""
from datetime import datetime, timedelta
from typing import Optional
import jwt
from fastapi import HTT<PERSON>Exception, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import os
import sys

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from src.database.db_manager import DatabaseManager
from src.auth.auth_manager import AuthManager

# JWT Configuration
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Security
security = HTTPBearer()

# Global instances (should be dependency injected in production)
db_manager = DatabaseManager()
auth_manager = AuthManager(db_manager)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """
    Create a JWT access token.
    
    Args:
        data (dict): Data to encode in the token
        expires_delta (timedelta, optional): Token expiration time
        
    Returns:
        str: Encoded JWT token
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[dict]:
    """
    Verify and decode a JWT token.
    
    Args:
        token (str): JWT token to verify
        
    Returns:
        dict: Decoded token payload if valid, None otherwise
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
        return payload
    except jwt.PyJWTError:
        return None


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> dict:
    """
    Get the current authenticated user from JWT token.
    
    Args:
        credentials: HTTP authorization credentials
        
    Returns:
        dict: Current user information
        
    Raises:
        HTTPException: If authentication fails
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Extract token from credentials
        token = credentials.credentials
        
        # Verify token
        payload = verify_token(token)
        if payload is None:
            raise credentials_exception
        
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        
        # Get user from database
        db_manager.connect()
        try:
            db_manager.cursor.execute(
                "SELECT id, username, email, full_name, role, is_active FROM users WHERE username = ?",
                (username,)
            )
            user = db_manager.cursor.fetchone()
            
            if user is None:
                raise credentials_exception
            
            # Convert sqlite3.Row to dict
            user_dict = dict(user)
            
            # Check if user is active
            if not user_dict.get("is_active", False):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User account is disabled"
                )
            
            return user_dict
            
        finally:
            db_manager.disconnect()
            
    except jwt.PyJWTError:
        raise credentials_exception


async def get_current_active_user(current_user: dict = Depends(get_current_user)) -> dict:
    """
    Get the current active user.
    
    Args:
        current_user: Current user from get_current_user dependency
        
    Returns:
        dict: Current active user information
        
    Raises:
        HTTPException: If user is inactive
    """
    if not current_user.get("is_active", False):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


async def require_admin(current_user: dict = Depends(get_current_user)) -> dict:
    """
    Require admin role for the current user.
    
    Args:
        current_user: Current user from get_current_user dependency
        
    Returns:
        dict: Current user information if admin
        
    Raises:
        HTTPException: If user is not admin
    """
    if current_user.get("role") != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user


async def require_lab_manager_or_admin(current_user: dict = Depends(get_current_user)) -> dict:
    """
    Require lab manager or admin role for the current user.
    
    Args:
        current_user: Current user from get_current_user dependency
        
    Returns:
        dict: Current user information if lab manager or admin
        
    Raises:
        HTTPException: If user is not lab manager or admin
    """
    allowed_roles = ["admin", "lab_manager"]
    if current_user.get("role") not in allowed_roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Lab manager or admin access required"
        )
    return current_user


def check_permission(user: dict, required_permission: str) -> bool:
    """
    Check if user has the required permission.
    
    Args:
        user (dict): User information
        required_permission (str): Required permission
        
    Returns:
        bool: True if user has permission, False otherwise
    """
    user_role = user.get("role", "")
    
    # Define role permissions
    role_permissions = {
        "admin": ["admin", "lab_manager", "researcher", "student"],
        "lab_manager": ["lab_manager", "researcher", "student"],
        "researcher": ["researcher", "student"],
        "student": ["student"]
    }
    
    allowed_permissions = role_permissions.get(user_role, [])
    return required_permission in allowed_permissions


async def require_permission(permission: str):
    """
    Create a dependency that requires a specific permission.
    
    Args:
        permission (str): Required permission
        
    Returns:
        function: Dependency function
    """
    async def permission_checker(current_user: dict = Depends(get_current_user)) -> dict:
        if not check_permission(current_user, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        return current_user
    
    return permission_checker
