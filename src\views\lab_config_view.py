import flet as ft
from datetime import datetime

class LabConfigView:
    """
    Laboratory configuration view for the Science Laboratory Management System.
    Allows administrators to configure lab rooms, equipment, and resources.
    """
    
    def __init__(self, controller):
        """
        Initialize the lab configuration view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        self.current_tab = "rooms"  # "rooms", "equipment", or "resources"
        
        # Create tabs
        self.tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            tabs=[
                ft.Tab(
                    text="Lab Rooms",
                    icon=ft.icons.ROOM,
                    content=ft.Container(padding=10),
                ),
                ft.Tab(
                    text="Equipment",
                    icon=ft.icons.SCIENCE,
                    content=ft.Container(padding=10),
                ),
                ft.Tab(
                    text="Resources",
                    icon=ft.icons.INVENTORY_2,
                    content=ft.Container(padding=10),
                ),
            ],
            on_change=self.tab_changed,
        )
        
        # Lab rooms data
        self.rooms = []
        self.rooms_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("Room Number")),
                ft.DataColumn(ft.Text("Name")),
                ft.DataColumn(ft.Text("Capacity")),
                ft.DataColumn(ft.Text("Type")),
                ft.DataColumn(ft.Text("Status")),
                ft.DataColumn(ft.Text("Actions")),
            ],
            rows=[],
        )
        
        # Equipment data
        self.equipment = []
        self.equipment_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("Name")),
                ft.DataColumn(ft.Text("Type")),
                ft.DataColumn(ft.Text("Location")),
                ft.DataColumn(ft.Text("Status")),
                ft.DataColumn(ft.Text("Last Maintenance")),
                ft.DataColumn(ft.Text("Actions")),
            ],
            rows=[],
        )
        
        # Resources data
        self.resources = []
        self.resources_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("Name")),
                ft.DataColumn(ft.Text("Type")),
                ft.DataColumn(ft.Text("Location")),
                ft.DataColumn(ft.Text("Quantity")),
                ft.DataColumn(ft.Text("Status")),
                ft.DataColumn(ft.Text("Actions")),
            ],
            rows=[],
        )
        
        # Room form fields
        self.room_id_field = ft.TextField(visible=False)
        self.room_number_field = ft.TextField(label="Room Number", required=True)
        self.room_name_field = ft.TextField(label="Name", required=True)
        self.room_capacity_field = ft.TextField(
            label="Capacity", 
            required=True,
            keyboard_type=ft.KeyboardType.NUMBER,
        )
        self.room_type_dropdown = ft.Dropdown(
            label="Type",
            required=True,
            options=[
                ft.dropdown.Option("teaching"),
                ft.dropdown.Option("research"),
                ft.dropdown.Option("storage"),
                ft.dropdown.Option("preparation"),
                ft.dropdown.Option("office"),
            ],
        )
        self.room_status_dropdown = ft.Dropdown(
            label="Status",
            required=True,
            options=[
                ft.dropdown.Option("active"),
                ft.dropdown.Option("maintenance"),
                ft.dropdown.Option("inactive"),
            ],
        )
        self.room_description_field = ft.TextField(
            label="Description",
            multiline=True,
            min_lines=2,
            max_lines=4,
        )
        
        # Equipment form fields
        self.equipment_id_field = ft.TextField(visible=False)
        self.equipment_name_field = ft.TextField(label="Name", required=True)
        self.equipment_type_field = ft.TextField(label="Type", required=True)
        self.equipment_location_dropdown = ft.Dropdown(
            label="Location",
            required=True,
        )
        self.equipment_status_dropdown = ft.Dropdown(
            label="Status",
            required=True,
            options=[
                ft.dropdown.Option("operational"),
                ft.dropdown.Option("maintenance"),
                ft.dropdown.Option("out_of_service"),
                ft.dropdown.Option("calibration"),
            ],
        )
        self.equipment_serial_field = ft.TextField(label="Serial Number")
        self.equipment_purchase_date_field = ft.TextField(label="Purchase Date (YYYY-MM-DD)")
        self.equipment_last_maintenance_field = ft.TextField(label="Last Maintenance (YYYY-MM-DD)")
        self.equipment_next_maintenance_field = ft.TextField(label="Next Maintenance (YYYY-MM-DD)")
        self.equipment_notes_field = ft.TextField(
            label="Notes",
            multiline=True,
            min_lines=2,
            max_lines=4,
        )
        
        # Resource form fields
        self.resource_id_field = ft.TextField(visible=False)
        self.resource_name_field = ft.TextField(label="Name", required=True)
        self.resource_type_dropdown = ft.Dropdown(
            label="Type",
            required=True,
            options=[
                ft.dropdown.Option("consumable"),
                ft.dropdown.Option("non_consumable"),
                ft.dropdown.Option("chemical"),
                ft.dropdown.Option("biological"),
                ft.dropdown.Option("safety"),
            ],
        )
        self.resource_location_dropdown = ft.Dropdown(
            label="Location",
            required=True,
        )
        self.resource_quantity_field = ft.TextField(
            label="Quantity", 
            required=True,
            keyboard_type=ft.KeyboardType.NUMBER,
        )
        self.resource_unit_field = ft.TextField(label="Unit", required=True)
        self.resource_status_dropdown = ft.Dropdown(
            label="Status",
            required=True,
            options=[
                ft.dropdown.Option("available"),
                ft.dropdown.Option("low_stock"),
                ft.dropdown.Option("out_of_stock"),
                ft.dropdown.Option("expired"),
            ],
        )
        self.resource_notes_field = ft.TextField(
            label="Notes",
            multiline=True,
            min_lines=2,
            max_lines=4,
        )
        
        # Create dialogs
        self.room_dialog = ft.AlertDialog(
            title=ft.Text("Add Lab Room"),
            content=ft.Column(
                [
                    self.room_id_field,
                    self.room_number_field,
                    self.room_name_field,
                    self.room_capacity_field,
                    self.room_type_dropdown,
                    self.room_status_dropdown,
                    self.room_description_field,
                ],
                tight=True,
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                width=400,
                height=400,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=self.close_dialog),
                ft.TextButton("Save", on_click=self.save_room),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        self.equipment_dialog = ft.AlertDialog(
            title=ft.Text("Add Equipment"),
            content=ft.Column(
                [
                    self.equipment_id_field,
                    self.equipment_name_field,
                    self.equipment_type_field,
                    self.equipment_location_dropdown,
                    self.equipment_status_dropdown,
                    self.equipment_serial_field,
                    self.equipment_purchase_date_field,
                    self.equipment_last_maintenance_field,
                    self.equipment_next_maintenance_field,
                    self.equipment_notes_field,
                ],
                tight=True,
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                width=400,
                height=400,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=self.close_dialog),
                ft.TextButton("Save", on_click=self.save_equipment),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        self.resource_dialog = ft.AlertDialog(
            title=ft.Text("Add Resource"),
            content=ft.Column(
                [
                    self.resource_id_field,
                    self.resource_name_field,
                    self.resource_type_dropdown,
                    self.resource_location_dropdown,
                    self.resource_quantity_field,
                    self.resource_unit_field,
                    self.resource_status_dropdown,
                    self.resource_notes_field,
                ],
                tight=True,
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                width=400,
                height=400,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=self.close_dialog),
                ft.TextButton("Save", on_click=self.save_resource),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
    
    def build(self):
        """
        Build and return the lab configuration view.
        
        Returns:
            ft.Container: The lab configuration view container
        """
        # Load data
        self.load_data()
        
        # Build rooms tab
        rooms_content = ft.Column(
            [
                ft.Row(
                    [
                        ft.Text("Lab Rooms", size=20, weight=ft.FontWeight.BOLD),
                        ft.ElevatedButton(
                            text="Add Room",
                            icon=ft.icons.ADD,
                            on_click=self.show_add_room_dialog,
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Divider(),
                ft.Container(
                    content=self.rooms_table,
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                    padding=10,
                    expand=True,
                ),
            ],
            spacing=10,
            expand=True,
        )
        
        # Build equipment tab
        equipment_content = ft.Column(
            [
                ft.Row(
                    [
                        ft.Text("Equipment", size=20, weight=ft.FontWeight.BOLD),
                        ft.ElevatedButton(
                            text="Add Equipment",
                            icon=ft.icons.ADD,
                            on_click=self.show_add_equipment_dialog,
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Divider(),
                ft.Container(
                    content=self.equipment_table,
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                    padding=10,
                    expand=True,
                ),
            ],
            spacing=10,
            expand=True,
        )
        
        # Build resources tab
        resources_content = ft.Column(
            [
                ft.Row(
                    [
                        ft.Text("Resources", size=20, weight=ft.FontWeight.BOLD),
                        ft.ElevatedButton(
                            text="Add Resource",
                            icon=ft.icons.ADD,
                            on_click=self.show_add_resource_dialog,
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                ft.Divider(),
                ft.Container(
                    content=self.resources_table,
                    border=ft.border.all(1, ft.colors.GREY_300),
                    border_radius=5,
                    padding=10,
                    expand=True,
                ),
            ],
            spacing=10,
            expand=True,
        )
        
        # Set tab contents
        self.tabs.tabs[0].content = rooms_content
        self.tabs.tabs[1].content = equipment_content
        self.tabs.tabs[2].content = resources_content
        
        # Return the complete view
        return ft.Container(
            content=ft.Column(
                [
                    ft.Text("Laboratory Configuration", size=24, weight=ft.FontWeight.BOLD),
                    ft.Container(height=10),
                    self.tabs,
                ],
                spacing=10,
                expand=True,
            ),
            padding=20,
            expand=True,
        )
    
    def load_data(self):
        """Load data from the database."""
        self.load_rooms()
        self.load_equipment()
        self.load_resources()
        
        # Load room locations for dropdowns
        rooms = self.controller.db.execute_query("SELECT id, room_number, name FROM lab_rooms ORDER BY room_number")
        
        self.equipment_location_dropdown.options = [
            ft.dropdown.Option(f"{room['room_number']} - {room['name']}", data=room["id"]) 
            for room in rooms
        ]
        
        self.resource_location_dropdown.options = [
            ft.dropdown.Option(f"{room['room_number']} - {room['name']}", data=room["id"]) 
            for room in rooms
        ]
    
    def load_rooms(self):
        """Load lab rooms from the database."""
        # Query the database
        self.rooms = self.controller.db.execute_query("SELECT * FROM lab_rooms ORDER BY room_number")
        
        # Clear existing rows
        self.rooms_table.rows.clear()
        
        # Add rows for rooms
        for room in self.rooms:
            # Get status color
            status_color = self.get_room_status_color(room["status"])
            
            # Create row
            self.rooms_table.rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(room["room_number"])),
                        ft.DataCell(ft.Text(room["name"])),
                        ft.DataCell(ft.Text(str(room["capacity"]))),
                        ft.DataCell(ft.Text(room["type"].replace("_", " ").capitalize())),
                        ft.DataCell(ft.Text(room["status"].replace("_", " ").capitalize(), color=status_color)),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.icons.EDIT,
                                        tooltip="Edit",
                                        on_click=lambda e, room_id=room["id"]: self.show_edit_room_dialog(e, room_id),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.DELETE,
                                        tooltip="Delete",
                                        on_click=lambda e, room_id=room["id"]: self.delete_room(e, room_id),
                                    ),
                                ]
                            )
                        ),
                    ]
                )
            )
        
        # Update the page
        self.controller.page.update()
    
    def load_equipment(self):
        """Load equipment from the database."""
        # Query the database
        self.equipment = self.controller.db.execute_query("""
            SELECT e.*, r.room_number, r.name as room_name
            FROM lab_equipment e
            LEFT JOIN lab_rooms r ON e.location_id = r.id
            ORDER BY e.name
        """)
        
        # Clear existing rows
        self.equipment_table.rows.clear()
        
        # Add rows for equipment
        for equipment in self.equipment:
            # Format dates
            last_maintenance = self.format_date(equipment["last_maintenance"])
            
            # Get status color
            status_color = self.get_equipment_status_color(equipment["status"])
            
            # Create location text
            location = f"{equipment['room_number']} - {equipment['room_name']}" if equipment["room_number"] else "N/A"
            
            # Create row
            self.equipment_table.rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(equipment["name"])),
                        ft.DataCell(ft.Text(equipment["type"])),
                        ft.DataCell(ft.Text(location)),
                        ft.DataCell(ft.Text(equipment["status"].replace("_", " ").capitalize(), color=status_color)),
                        ft.DataCell(ft.Text(last_maintenance)),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.icons.EDIT,
                                        tooltip="Edit",
                                        on_click=lambda e, eq_id=equipment["id"]: self.show_edit_equipment_dialog(e, eq_id),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.DELETE,
                                        tooltip="Delete",
                                        on_click=lambda e, eq_id=equipment["id"]: self.delete_equipment(e, eq_id),
                                    ),
                                ]
                            )
                        ),
                    ]
                )
            )
        
        # Update the page
        self.controller.page.update()
    
    def load_resources(self):
        """Load resources from the database."""
        # Query the database
        self.resources = self.controller.db.execute_query("""
            SELECT r.*, lr.room_number, lr.name as room_name
            FROM lab_resources r
            LEFT JOIN lab_rooms lr ON r.location_id = lr.id
            ORDER BY r.name
        """)
        
        # Clear existing rows
        self.resources_table.rows.clear()
        
        # Add rows for resources
        for resource in self.resources:
            # Get status color
            status_color = self.get_resource_status_color(resource["status"])
            
            # Create location text
            location = f"{resource['room_number']} - {resource['room_name']}" if resource["room_number"] else "N/A"
            
            # Create row
            self.resources_table.rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(resource["name"])),
                        ft.DataCell(ft.Text(resource["type"].replace("_", " ").capitalize())),
                        ft.DataCell(ft.Text(location)),
                        ft.DataCell(ft.Text(f"{resource['quantity']} {resource['unit']}")),
                        ft.DataCell(ft.Text(resource["status"].replace("_", " ").capitalize(), color=status_color)),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.icons.EDIT,
                                        tooltip="Edit",
                                        on_click=lambda e, res_id=resource["id"]: self.show_edit_resource_dialog(e, res_id),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.DELETE,
                                        tooltip="Delete",
                                        on_click=lambda e, res_id=resource["id"]: self.delete_resource(e, res_id),
                                    ),
                                ]
                            )
                        ),
                    ]
                )
            )
        
        # Update the page
        self.controller.page.update()
    
    def tab_changed(self, e):
        """
        Handle tab change.
        
        Args:
            e: The change event
        """
        self.current_tab = ["rooms", "equipment", "resources"][e.control.selected_index]
    
    def show_add_room_dialog(self, e):
        """
        Show dialog for adding a new lab room.
        
        Args:
            e: The click event
        """
        # Clear form fields
        self.room_id_field.value = ""
        self.room_number_field.value = ""
        self.room_name_field.value = ""
        self.room_capacity_field.value = ""
        self.room_type_dropdown.value = "teaching"
        self.room_status_dropdown.value = "active"
        self.room_description_field.value = ""
        
        # Set dialog title
        self.room_dialog.title = ft.Text("Add Lab Room")
        
        # Show the dialog
        self.controller.page.dialog = self.room_dialog
        self.room_dialog.open = True
        self.controller.page.update()
    
    def show_edit_room_dialog(self, e, room_id):
        """
        Show dialog for editing a lab room.
        
        Args:
            e: The click event
            room_id: ID of the room to edit
        """
        # Find the room
        room = next((r for r in self.rooms if r["id"] == room_id), None)
        
        if not room:
            return
        
        # Set form fields
        self.room_id_field.value = str(room["id"])
        self.room_number_field.value = room["room_number"]
        self.room_name_field.value = room["name"]
        self.room_capacity_field.value = str(room["capacity"])
        self.room_type_dropdown.value = room["type"]
        self.room_status_dropdown.value = room["status"]
        self.room_description_field.value = room["description"] or ""
        
        # Set dialog title
        self.room_dialog.title = ft.Text("Edit Lab Room")
        
        # Show the dialog
        self.controller.page.dialog = self.room_dialog
        self.room_dialog.open = True
        self.controller.page.update()
    
    def show_add_equipment_dialog(self, e):
        """
        Show dialog for adding new equipment.
        
        Args:
            e: The click event
        """
        # Clear form fields
        self.equipment_id_field.value = ""
        self.equipment_name_field.value = ""
        self.equipment_type_field.value = ""
        self.equipment_location_dropdown.value = None
        self.equipment_status_dropdown.value = "operational"
        self.equipment_serial_field.value = ""
        self.equipment_purchase_date_field.value = ""
        self.equipment_last_maintenance_field.value = ""
        self.equipment_next_maintenance_field.value = ""
        self.equipment_notes_field.value = ""
        
        # Set dialog title
        self.equipment_dialog.title = ft.Text("Add Equipment")
        
        # Show the dialog
        self.controller.page.dialog = self.equipment_dialog
        self.equipment_dialog.open = True
        self.controller.page.update()
    
    def show_edit_equipment_dialog(self, e, equipment_id):
        """
        Show dialog for editing equipment.
        
        Args:
            e: The click event
            equipment_id: ID of the equipment to edit
        """
        # Find the equipment
        equipment = next((eq for eq in self.equipment if eq["id"] == equipment_id), None)
        
        if not equipment:
            return
        
        # Set form fields
        self.equipment_id_field.value = str(equipment["id"])
        self.equipment_name_field.value = equipment["name"]
        self.equipment_type_field.value = equipment["type"]
        
        # Find the matching location option
        for option in self.equipment_location_dropdown.options:
            if option.data == equipment["location_id"]:
                self.equipment_location_dropdown.value = option.key
                break
        
        self.equipment_status_dropdown.value = equipment["status"]
        self.equipment_serial_field.value = equipment["serial_number"] or ""
        self.equipment_purchase_date_field.value = equipment["purchase_date"] or ""
        self.equipment_last_maintenance_field.value = equipment["last_maintenance"] or ""
        self.equipment_next_maintenance_field.value = equipment["next_maintenance"] or ""
        self.equipment_notes_field.value = equipment["notes"] or ""
        
        # Set dialog title
        self.equipment_dialog.title = ft.Text("Edit Equipment")
        
        # Show the dialog
        self.controller.page.dialog = self.equipment_dialog
        self.equipment_dialog.open = True
        self.controller.page.update()
    
    def show_add_resource_dialog(self, e):
        """
        Show dialog for adding a new resource.
        
        Args:
            e: The click event
        """
        # Clear form fields
        self.resource_id_field.value = ""
        self.resource_name_field.value = ""
        self.resource_type_dropdown.value = "consumable"
        self.resource_location_dropdown.value = None
        self.resource_quantity_field.value = ""
        self.resource_unit_field.value = ""
        self.resource_status_dropdown.value = "available"
        self.resource_notes_field.value = ""
        
        # Set dialog title
        self.resource_dialog.title = ft.Text("Add Resource")
        
        # Show the dialog
        self.controller.page.dialog = self.resource_dialog
        self.resource_dialog.open = True
        self.controller.page.update()
    
    def show_edit_resource_dialog(self, e, resource_id):
        """
        Show dialog for editing a resource.
        
        Args:
            e: The click event
            resource_id: ID of the resource to edit
        """
        # Find the resource
        resource = next((r for r in self.resources if r["id"] == resource_id), None)
        
        if not resource:
            return
        
        # Set form fields
        self.resource_id_field.value = str(resource["id"])
        self.resource_name_field.value = resource["name"]
        self.resource_type_dropdown.value = resource["type"]
        
        # Find the matching location option
        for option in self.resource_location_dropdown.options:
            if option.data == resource["location_id"]:
                self.resource_location_dropdown.value = option.key
                break
        
        self.resource_quantity_field.value = str(resource["quantity"])
        self.resource_unit_field.value = resource["unit"]
        self.resource_status_dropdown.value = resource["status"]
        self.resource_notes_field.value = resource["notes"] or ""
        
        # Set dialog title
        self.resource_dialog.title = ft.Text("Edit Resource")
        
        # Show the dialog
        self.controller.page.dialog = self.resource_dialog
        self.resource_dialog.open = True
        self.controller.page.update()
    
    def close_dialog(self, e):
        """
        Close the current dialog.
        
        Args:
            e: The click event
        """
        self.room_dialog.open = False
        self.equipment_dialog.open = False
        self.resource_dialog.open = False
        self.controller.page.update()
    
    def save_room(self, e):
        """
        Save the lab room.
        
        Args:
            e: The click event
        """
        # Validate required fields
        if not self.room_number_field.value or not self.room_name_field.value or not self.room_capacity_field.value:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Please fill in all required fields"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
            return
        
        # Prepare room data
        room_data = {
            "room_number": self.room_number_field.value,
            "name": self.room_name_field.value,
            "capacity": int(self.room_capacity_field.value),
            "type": self.room_type_dropdown.value,
            "status": self.room_status_dropdown.value,
            "description": self.room_description_field.value if self.room_description_field.value else None,
        }
        
        # Add or update room
        if self.room_id_field.value:
            # Update existing room
            room_id = int(self.room_id_field.value)
            
            # Build the update query
            query_parts = []
            params = []
            
            for key, value in room_data.items():
                query_parts.append(f"{key} = ?")
                params.append(value)
            
            query = f"UPDATE lab_rooms SET {', '.join(query_parts)} WHERE id = ?"
            params.append(room_id)
            
            # Execute the update
            self.controller.db.execute_query(query, tuple(params))
            
            # Log the update
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "update",
                "lab_room",
                room_id,
                f"Updated lab room: {room_data['room_number']} - {room_data['name']}"
            )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Room updated successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
        else:
            # Add new room
            
            # Build the insert query
            keys = list(room_data.keys())
            placeholders = ["?"] * len(keys)
            
            query = f"INSERT INTO lab_rooms ({', '.join(keys)}) VALUES ({', '.join(placeholders)})"
            params = tuple(room_data.values())
            
            # Execute the insert
            room_id = self.controller.db.execute_insert(query, params)
            
            # Log the insert
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "create",
                "lab_room",
                room_id,
                f"Added new lab room: {room_data['room_number']} - {room_data['name']}"
            )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Room added successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
        
        # Close the dialog
        self.room_dialog.open = False
        
        # Reload data
        self.load_data()
        
        # Update the page
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def save_equipment(self, e):
        """
        Save the equipment.
        
        Args:
            e: The click event
        """
        # Validate required fields
        if not self.equipment_name_field.value or not self.equipment_type_field.value or not self.equipment_location_dropdown.value:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Please fill in all required fields"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
            return
        
        # Get location ID from selected option
        location_id = None
        for option in self.equipment_location_dropdown.options:
            if option.key == self.equipment_location_dropdown.value:
                location_id = option.data
                break
        
        # Prepare equipment data
        equipment_data = {
            "name": self.equipment_name_field.value,
            "type": self.equipment_type_field.value,
            "location_id": location_id,
            "status": self.equipment_status_dropdown.value,
            "serial_number": self.equipment_serial_field.value if self.equipment_serial_field.value else None,
            "purchase_date": self.equipment_purchase_date_field.value if self.equipment_purchase_date_field.value else None,
            "last_maintenance": self.equipment_last_maintenance_field.value if self.equipment_last_maintenance_field.value else None,
            "next_maintenance": self.equipment_next_maintenance_field.value if self.equipment_next_maintenance_field.value else None,
            "notes": self.equipment_notes_field.value if self.equipment_notes_field.value else None,
        }
        
        # Add or update equipment
        if self.equipment_id_field.value:
            # Update existing equipment
            equipment_id = int(self.equipment_id_field.value)
            
            # Build the update query
            query_parts = []
            params = []
            
            for key, value in equipment_data.items():
                query_parts.append(f"{key} = ?")
                params.append(value)
            
            query = f"UPDATE lab_equipment SET {', '.join(query_parts)} WHERE id = ?"
            params.append(equipment_id)
            
            # Execute the update
            self.controller.db.execute_query(query, tuple(params))
            
            # Log the update
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "update",
                "lab_equipment",
                equipment_id,
                f"Updated equipment: {equipment_data['name']}"
            )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Equipment updated successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
        else:
            # Add new equipment
            
            # Build the insert query
            keys = list(equipment_data.keys())
            placeholders = ["?"] * len(keys)
            
            query = f"INSERT INTO lab_equipment ({', '.join(keys)}) VALUES ({', '.join(placeholders)})"
            params = tuple(equipment_data.values())
            
            # Execute the insert
            equipment_id = self.controller.db.execute_insert(query, params)
            
            # Log the insert
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "create",
                "lab_equipment",
                equipment_id,
                f"Added new equipment: {equipment_data['name']}"
            )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Equipment added successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
        
        # Close the dialog
        self.equipment_dialog.open = False
        
        # Reload data
        self.load_data()
        
        # Update the page
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def save_resource(self, e):
        """
        Save the resource.
        
        Args:
            e: The click event
        """
        # Validate required fields
        if not self.resource_name_field.value or not self.resource_type_dropdown.value or not self.resource_location_dropdown.value or not self.resource_quantity_field.value or not self.resource_unit_field.value:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Please fill in all required fields"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
            return
        
        # Get location ID from selected option
        location_id = None
        for option in self.resource_location_dropdown.options:
            if option.key == self.resource_location_dropdown.value:
                location_id = option.data
                break
        
        # Prepare resource data
        resource_data = {
            "name": self.resource_name_field.value,
            "type": self.resource_type_dropdown.value,
            "location_id": location_id,
            "quantity": float(self.resource_quantity_field.value),
            "unit": self.resource_unit_field.value,
            "status": self.resource_status_dropdown.value,
            "notes": self.resource_notes_field.value if self.resource_notes_field.value else None,
        }
        
        # Add or update resource
        if self.resource_id_field.value:
            # Update existing resource
            resource_id = int(self.resource_id_field.value)
            
            # Build the update query
            query_parts = []
            params = []
            
            for key, value in resource_data.items():
                query_parts.append(f"{key} = ?")
                params.append(value)
            
            query = f"UPDATE lab_resources SET {', '.join(query_parts)} WHERE id = ?"
            params.append(resource_id)
            
            # Execute the update
            self.controller.db.execute_query(query, tuple(params))
            
            # Log the update
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "update",
                "lab_resource",
                resource_id,
                f"Updated resource: {resource_data['name']}"
            )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Resource updated successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
        else:
            # Add new resource
            
            # Build the insert query
            keys = list(resource_data.keys())
            placeholders = ["?"] * len(keys)
            
            query = f"INSERT INTO lab_resources ({', '.join(keys)}) VALUES ({', '.join(placeholders)})"
            params = tuple(resource_data.values())
            
            # Execute the insert
            resource_id = self.controller.db.execute_insert(query, params)
            
            # Log the insert
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "create",
                "lab_resource",
                resource_id,
                f"Added new resource: {resource_data['name']}"
            )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Resource added successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
        
        # Close the dialog
        self.resource_dialog.open = False
        
        # Reload data
        self.load_data()
        
        # Update the page
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def delete_room(self, e, room_id):
        """
        Delete a lab room.
        
        Args:
            e: The click event
            room_id: ID of the room to delete
        """
        # Find the room
        room = next((r for r in self.rooms if r["id"] == room_id), None)
        
        if not room:
            return
        
        # Show confirmation dialog
        def confirm_delete(e):
            # Delete the room
            self.controller.db.execute_query(
                "DELETE FROM lab_rooms WHERE id = ?",
                (room_id,)
            )
            
            # Log the delete
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "delete",
                "lab_room",
                room_id,
                f"Deleted lab room: {room['room_number']} - {room['name']}"
            )
            
            # Close the dialog
            confirm_dialog.open = False
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Room deleted successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
            self.controller.page.snack_bar.open = True
            
            # Reload data
            self.load_data()
            
            # Update the page
            self.controller.page.update()
        
        # Create confirmation dialog
        confirm_dialog = ft.AlertDialog(
            title=ft.Text("Confirm Delete"),
            content=ft.Text(f"Are you sure you want to delete room '{room['room_number']} - {room['name']}'?"),
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(confirm_dialog, "open", False)),
                ft.TextButton("Delete", on_click=confirm_delete),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = confirm_dialog
        confirm_dialog.open = True
        self.controller.page.update()
    
    def delete_equipment(self, e, equipment_id):
        """
        Delete equipment.
        
        Args:
            e: The click event
            equipment_id: ID of the equipment to delete
        """
        # Find the equipment
        equipment = next((eq for eq in self.equipment if eq["id"] == equipment_id), None)
        
        if not equipment:
            return
        
        # Show confirmation dialog
        def confirm_delete(e):
            # Delete the equipment
            self.controller.db.execute_query(
                "DELETE FROM lab_equipment WHERE id = ?",
                (equipment_id,)
            )
            
            # Log the delete
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "delete",
                "lab_equipment",
                equipment_id,
                f"Deleted equipment: {equipment['name']}"
            )
            
            # Close the dialog
            confirm_dialog.open = False
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Equipment deleted successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
            self.controller.page.snack_bar.open = True
            
            # Reload data
            self.load_data()
            
            # Update the page
            self.controller.page.update()
        
        # Create confirmation dialog
        confirm_dialog = ft.AlertDialog(
            title=ft.Text("Confirm Delete"),
            content=ft.Text(f"Are you sure you want to delete equipment '{equipment['name']}'?"),
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(confirm_dialog, "open", False)),
                ft.TextButton("Delete", on_click=confirm_delete),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = confirm_dialog
        confirm_dialog.open = True
        self.controller.page.update()
    
    def delete_resource(self, e, resource_id):
        """
        Delete a resource.
        
        Args:
            e: The click event
            resource_id: ID of the resource to delete
        """
        # Find the resource
        resource = next((r for r in self.resources if r["id"] == resource_id), None)
        
        if not resource:
            return
        
        # Show confirmation dialog
        def confirm_delete(e):
            # Delete the resource
            self.controller.db.execute_query(
                "DELETE FROM lab_resources WHERE id = ?",
                (resource_id,)
            )
            
            # Log the delete
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "delete",
                "lab_resource",
                resource_id,
                f"Deleted resource: {resource['name']}"
            )
            
            # Close the dialog
            confirm_dialog.open = False
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Resource deleted successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
            self.controller.page.snack_bar.open = True
            
            # Reload data
            self.load_data()
            
            # Update the page
            self.controller.page.update()
        
        # Create confirmation dialog
        confirm_dialog = ft.AlertDialog(
            title=ft.Text("Confirm Delete"),
            content=ft.Text(f"Are you sure you want to delete resource '{resource['name']}'?"),
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(confirm_dialog, "open", False)),
                ft.TextButton("Delete", on_click=confirm_delete),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = confirm_dialog
        confirm_dialog.open = True
        self.controller.page.update()
    
    def format_date(self, date_str):
        """
        Format a date string for display.
        
        Args:
            date_str (str): The date string to format
            
        Returns:
            str: Formatted date
        """
        if not date_str:
            return "N/A"
            
        try:
            date = datetime.strptime(date_str, "%Y-%m-%d")
            return date.strftime("%m/%d/%Y")
        except:
            return date_str
    
    def get_room_status_color(self, status):
        """
        Get the color for a room status.
        
        Args:
            status (str): The status
            
        Returns:
            str: The color for the status
        """
        if status == "active":
            return ft.colors.GREEN
        elif status == "maintenance":
            return ft.colors.ORANGE
        elif status == "inactive":
            return ft.colors.RED
        else:
            return ft.colors.BLACK
    
    def get_equipment_status_color(self, status):
        """
        Get the color for equipment status.
        
        Args:
            status (str): The status
            
        Returns:
            str: The color for the status
        """
        if status == "operational":
            return ft.colors.GREEN
        elif status == "maintenance":
            return ft.colors.ORANGE
        elif status == "out_of_service":
            return ft.colors.RED
        elif status == "calibration":
            return ft.colors.BLUE
        else:
            return ft.colors.BLACK
    
    def get_resource_status_color(self, status):
        """
        Get the color for resource status.
        
        Args:
            status (str): The status
            
        Returns:
            str: The color for the status
        """
        if status == "available":
            return ft.colors.GREEN
        elif status == "low_stock":
            return ft.colors.ORANGE
        elif status == "out_of_stock":
            return ft.colors.RED
        elif status == "expired":
            return ft.colors.RED_900
        else:
            return ft.colors.BLACK