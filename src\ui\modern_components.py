"""
Modern UI components and enhancements for the Science Laboratory Management System.
"""
import flet as ft
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
import json


class ModernCard(ft.Container):
    """Modern card component with elevation and hover effects."""
    
    def __init__(
        self,
        content: ft.Control,
        title: str = None,
        subtitle: str = None,
        actions: List[ft.Control] = None,
        elevation: int = 2,
        hover_elevation: int = 4,
        padding: int = 20,
        **kwargs
    ):
        # Create card content
        card_content = []
        
        if title or subtitle:
            header = ft.Column([])
            if title:
                header.controls.append(
                    ft.Text(title, size=18, weight=ft.FontWeight.BOLD)
                )
            if subtitle:
                header.controls.append(
                    ft.Text(subtitle, size=14, color=ft.colors.GREY_600)
                )
            card_content.append(header)
            card_content.append(ft.Divider(height=10))
        
        card_content.append(content)
        
        if actions:
            card_content.append(ft.Divider(height=10))
            card_content.append(
                ft.Row(actions, alignment=ft.MainAxisAlignment.END)
            )
        
        super().__init__(
            content=ft.Column(card_content, tight=True),
            padding=padding,
            border_radius=12,
            bgcolor=ft.colors.WHITE,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=elevation * 2,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, elevation)
            ),
            **kwargs
        )
        
        self.elevation = elevation
        self.hover_elevation = hover_elevation
        self.on_hover = self._handle_hover
    
    def _handle_hover(self, e):
        """Handle hover effects."""
        if e.data == "true":
            # Mouse enter
            self.shadow = ft.BoxShadow(
                spread_radius=1,
                blur_radius=self.hover_elevation * 2,
                color=ft.colors.with_opacity(0.15, ft.colors.BLACK),
                offset=ft.Offset(0, self.hover_elevation)
            )
        else:
            # Mouse leave
            self.shadow = ft.BoxShadow(
                spread_radius=1,
                blur_radius=self.elevation * 2,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, self.elevation)
            )
        self.update()


class StatCard(ModernCard):
    """Statistics card component."""
    
    def __init__(
        self,
        title: str,
        value: str,
        subtitle: str = None,
        icon: str = None,
        color: str = ft.colors.BLUE,
        trend: str = None,
        trend_positive: bool = True,
        **kwargs
    ):
        # Create stat content
        stat_row = ft.Row([])
        
        if icon:
            stat_row.controls.append(
                ft.Container(
                    content=ft.Icon(icon, size=40, color=color),
                    padding=10,
                    border_radius=8,
                    bgcolor=ft.colors.with_opacity(0.1, color)
                )
            )
        
        stat_column = ft.Column([
            ft.Text(title, size=14, color=ft.colors.GREY_600),
            ft.Text(value, size=24, weight=ft.FontWeight.BOLD, color=color),
        ], spacing=2)
        
        if subtitle:
            stat_column.controls.append(
                ft.Text(subtitle, size=12, color=ft.colors.GREY_500)
            )
        
        if trend:
            trend_color = ft.colors.GREEN if trend_positive else ft.colors.RED
            trend_icon = ft.icons.TRENDING_UP if trend_positive else ft.icons.TRENDING_DOWN
            stat_column.controls.append(
                ft.Row([
                    ft.Icon(trend_icon, size=16, color=trend_color),
                    ft.Text(trend, size=12, color=trend_color)
                ], spacing=4)
            )
        
        stat_row.controls.append(stat_column)
        
        super().__init__(
            content=stat_row,
            padding=16,
            **kwargs
        )


class ModernDataTable(ft.Container):
    """Modern data table with search, sorting, and pagination."""
    
    def __init__(
        self,
        columns: List[Dict[str, Any]],
        data: List[Dict[str, Any]],
        searchable: bool = True,
        sortable: bool = True,
        paginated: bool = True,
        page_size: int = 10,
        actions: List[Dict[str, Any]] = None,
        **kwargs
    ):
        self.columns = columns
        self.original_data = data
        self.filtered_data = data.copy()
        self.current_page = 0
        self.page_size = page_size
        self.sort_column = None
        self.sort_ascending = True
        self.search_query = ""
        
        # Create table components
        self.search_field = ft.TextField(
            hint_text="Search...",
            prefix_icon=ft.icons.SEARCH,
            on_change=self._handle_search,
            width=300
        ) if searchable else None
        
        self.data_table = ft.DataTable(
            columns=[
                ft.DataColumn(
                    ft.Text(col["label"], weight=ft.FontWeight.BOLD),
                    on_sort=self._handle_sort if sortable else None
                ) for col in columns
            ],
            rows=[]
        )
        
        self.pagination_controls = self._create_pagination_controls() if paginated else None
        
        # Build table content
        table_content = []
        
        if searchable:
            table_content.append(
                ft.Row([
                    self.search_field,
                    ft.Container(expand=True),
                    *([ft.ElevatedButton(
                        action["label"],
                        icon=action.get("icon"),
                        on_click=action["on_click"]
                    ) for action in actions] if actions else [])
                ])
            )
        
        table_content.append(
            ft.Container(
                content=self.data_table,
                border=ft.border.all(1, ft.colors.GREY_300),
                border_radius=8,
                padding=0
            )
        )
        
        if paginated:
            table_content.append(self.pagination_controls)
        
        super().__init__(
            content=ft.Column(table_content, spacing=16),
            **kwargs
        )
        
        self._update_table()
    
    def _create_pagination_controls(self):
        """Create pagination controls."""
        self.page_info = ft.Text("", size=12, color=ft.colors.GREY_600)
        
        return ft.Row([
            ft.IconButton(
                ft.icons.CHEVRON_LEFT,
                on_click=self._previous_page,
                disabled=True
            ),
            self.page_info,
            ft.IconButton(
                ft.icons.CHEVRON_RIGHT,
                on_click=self._next_page
            )
        ], alignment=ft.MainAxisAlignment.CENTER)
    
    def _handle_search(self, e):
        """Handle search input."""
        self.search_query = e.control.value.lower()
        self.current_page = 0
        self._filter_data()
        self._update_table()
    
    def _handle_sort(self, e):
        """Handle column sorting."""
        column_index = e.column_index
        column_key = self.columns[column_index]["key"]
        
        if self.sort_column == column_key:
            self.sort_ascending = not self.sort_ascending
        else:
            self.sort_column = column_key
            self.sort_ascending = True
        
        self._sort_data()
        self._update_table()
    
    def _filter_data(self):
        """Filter data based on search query."""
        if not self.search_query:
            self.filtered_data = self.original_data.copy()
        else:
            self.filtered_data = [
                row for row in self.original_data
                if any(
                    self.search_query in str(row.get(col["key"], "")).lower()
                    for col in self.columns
                )
            ]
    
    def _sort_data(self):
        """Sort data by selected column."""
        if self.sort_column:
            self.filtered_data.sort(
                key=lambda x: x.get(self.sort_column, ""),
                reverse=not self.sort_ascending
            )
    
    def _update_table(self):
        """Update table display."""
        # Calculate pagination
        start_index = self.current_page * self.page_size
        end_index = start_index + self.page_size
        page_data = self.filtered_data[start_index:end_index]
        
        # Update table rows
        self.data_table.rows = [
            ft.DataRow([
                ft.DataCell(ft.Text(str(row.get(col["key"], ""))))
                for col in self.columns
            ]) for row in page_data
        ]
        
        # Update pagination info
        if self.pagination_controls:
            total_items = len(self.filtered_data)
            total_pages = (total_items + self.page_size - 1) // self.page_size
            
            self.page_info.value = f"Page {self.current_page + 1} of {total_pages} ({total_items} items)"
            
            # Update button states
            prev_button = self.pagination_controls.controls[0]
            next_button = self.pagination_controls.controls[2]
            
            prev_button.disabled = self.current_page == 0
            next_button.disabled = self.current_page >= total_pages - 1
        
        self.update()
    
    def _previous_page(self, e):
        """Go to previous page."""
        if self.current_page > 0:
            self.current_page -= 1
            self._update_table()
    
    def _next_page(self, e):
        """Go to next page."""
        total_pages = (len(self.filtered_data) + self.page_size - 1) // self.page_size
        if self.current_page < total_pages - 1:
            self.current_page += 1
            self._update_table()
    
    def update_data(self, new_data: List[Dict[str, Any]]):
        """Update table data."""
        self.original_data = new_data
        self.current_page = 0
        self._filter_data()
        self._sort_data()
        self._update_table()


class ModernDialog(ft.AlertDialog):
    """Modern dialog component."""
    
    def __init__(
        self,
        title: str,
        content: ft.Control,
        actions: List[ft.Control] = None,
        width: int = 400,
        **kwargs
    ):
        super().__init__(
            title=ft.Text(title, size=20, weight=ft.FontWeight.BOLD),
            content=ft.Container(
                content=content,
                width=width,
                padding=ft.padding.symmetric(vertical=10)
            ),
            actions=actions or [],
            actions_alignment=ft.MainAxisAlignment.END,
            shape=ft.RoundedRectangleBorder(radius=12),
            **kwargs
        )


class LoadingOverlay(ft.Container):
    """Loading overlay component."""
    
    def __init__(self, message: str = "Loading...", **kwargs):
        super().__init__(
            content=ft.Column([
                ft.ProgressRing(),
                ft.Text(message, size=16, text_align=ft.TextAlign.CENTER)
            ], 
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=16),
            alignment=ft.alignment.center,
            bgcolor=ft.colors.with_opacity(0.8, ft.colors.WHITE),
            **kwargs
        )


class NotificationBanner(ft.Container):
    """Notification banner component."""
    
    def __init__(
        self,
        message: str,
        notification_type: str = "info",  # info, success, warning, error
        dismissible: bool = True,
        duration: int = 5000,
        on_dismiss: Callable = None,
        **kwargs
    ):
        # Define colors and icons for different types
        type_config = {
            "info": {"color": ft.colors.BLUE, "icon": ft.icons.INFO},
            "success": {"color": ft.colors.GREEN, "icon": ft.icons.CHECK_CIRCLE},
            "warning": {"color": ft.colors.ORANGE, "icon": ft.icons.WARNING},
            "error": {"color": ft.colors.RED, "icon": ft.icons.ERROR}
        }
        
        config = type_config.get(notification_type, type_config["info"])
        
        content_row = [
            ft.Icon(config["icon"], color=config["color"], size=20),
            ft.Text(message, size=14, expand=True)
        ]
        
        if dismissible:
            content_row.append(
                ft.IconButton(
                    ft.icons.CLOSE,
                    icon_size=16,
                    on_click=lambda e: self._dismiss(on_dismiss)
                )
            )
        
        super().__init__(
            content=ft.Row(content_row, spacing=12),
            padding=12,
            border_radius=8,
            bgcolor=ft.colors.with_opacity(0.1, config["color"]),
            border=ft.border.all(1, ft.colors.with_opacity(0.3, config["color"])),
            **kwargs
        )
        
        # Auto-dismiss after duration
        if duration > 0:
            # Note: In a real implementation, you'd use a timer here
            pass
    
    def _dismiss(self, on_dismiss: Callable = None):
        """Dismiss the notification."""
        self.visible = False
        self.update()
        if on_dismiss:
            on_dismiss()
