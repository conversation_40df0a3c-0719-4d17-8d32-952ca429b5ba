"""
Quick Actions Floating Button System
Provides fast access to common laboratory management tasks
"""
import flet as ft
from typing import List, Dict, Any, Callable, Optional
from datetime import datetime


class QuickAction:
    """Represents a quick action item."""
    
    def __init__(self, id: str, title: str, icon: str, route: str, data: Dict[str, Any] = None, 
                 color: str = None, requires_auth: bool = True):
        self.id = id
        self.title = title
        self.icon = icon
        self.route = route
        self.data = data or {}
        self.color = color or ft.colors.BLUE
        self.requires_auth = requires_auth


class QuickActionsManager:
    """
    Manages quick action floating buttons and menus.
    """
    
    def __init__(self, on_navigate: Callable[[str, Dict], None] = None, 
                 current_user: Dict[str, Any] = None):
        """
        Initialize quick actions manager.
        
        Args:
            on_navigate: Callback function for navigation
            current_user: Current user information for role-based actions
        """
        self.on_navigate = on_navigate
        self.current_user = current_user
        self.is_expanded = False
        self.fab_main = None
        self.fab_overlay = None
        self.quick_actions = self._get_quick_actions()
    
    def _get_quick_actions(self) -> List[QuickAction]:
        """Get available quick actions based on user role."""
        actions = [
            # Common actions for all users
            QuickAction("add_inventory", "Add Item", "add_box", "inventory", 
                       {"action": "add"}, ft.colors.GREEN),
            QuickAction("new_experiment", "New Experiment", "science", "experiments", 
                       {"action": "new"}, ft.colors.BLUE),
            QuickAction("book_equipment", "Book Equipment", "event", "scheduling", 
                       {"action": "book"}, ft.colors.ORANGE),
            QuickAction("search", "Global Search", "search", "search", 
                       {"action": "search"}, ft.colors.PURPLE),
            
            # Research-focused actions
            QuickAction("quick_measurement", "Add Measurement", "straighten", "experiments", 
                       {"action": "measurement"}, ft.colors.TEAL),
            QuickAction("safety_report", "Safety Report", "health_and_safety", "safety", 
                       {"action": "report"}, ft.colors.RED),
            
            # Admin actions
            QuickAction("add_user", "Add User", "person_add", "users", 
                       {"action": "add"}, ft.colors.INDIGO),
            QuickAction("system_backup", "Backup System", "backup", "settings", 
                       {"action": "backup"}, ft.colors.GREY),
        ]
        
        # Filter actions based on user role
        if self.current_user:
            user_role = self.current_user.get("role", "student")
            
            if user_role == "student":
                # Students have limited actions
                actions = [a for a in actions if a.id in [
                    "search", "book_equipment", "safety_report"
                ]]
            elif user_role == "researcher":
                # Researchers can't add users or backup system
                actions = [a for a in actions if a.id not in [
                    "add_user", "system_backup"
                ]]
            # Admins and lab managers get all actions
        
        return actions
    
    def create_floating_action_button(self) -> ft.FloatingActionButton:
        """Create the main floating action button."""
        self.fab_main = ft.FloatingActionButton(
            icon=ft.icons.ADD,
            tooltip="Quick Actions",
            on_click=self._toggle_quick_actions,
            bgcolor=ft.colors.BLUE,
            foreground_color=ft.colors.WHITE,
        )
        return self.fab_main
    
    def create_quick_actions_overlay(self, page: ft.Page) -> ft.Container:
        """Create the quick actions overlay."""
        def close_overlay():
            self.fab_overlay.visible = False
            self.is_expanded = False
            self._update_fab_icon()
            page.update()
        
        # Create action buttons
        action_buttons = []
        for i, action in enumerate(self.quick_actions):
            # Calculate position for circular arrangement
            import math
            angle = (i * 2 * math.pi) / len(self.quick_actions) - math.pi / 2
            radius = 120
            x = radius * math.cos(angle)
            y = radius * math.sin(angle)
            
            action_button = ft.Container(
                content=ft.FloatingActionButton(
                    icon=getattr(ft.icons, action.icon.upper(), ft.icons.STAR),
                    tooltip=action.title,
                    mini=True,
                    bgcolor=action.color,
                    foreground_color=ft.colors.WHITE,
                    on_click=lambda e, a=action: self._execute_action(a, page),
                ),
                left=300 + x,  # Center position + offset
                top=300 + y,   # Center position + offset
                animate_position=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT),
            )
            action_buttons.append(action_button)
        
        # Create overlay background
        self.fab_overlay = ft.Container(
            content=ft.Stack([
                # Semi-transparent background
                ft.Container(
                    bgcolor=ft.colors.with_opacity(0.3, ft.colors.BLACK),
                    expand=True,
                    on_click=lambda e: close_overlay(),
                ),
                # Action buttons
                *action_buttons,
                # Center close button
                ft.Container(
                    content=ft.FloatingActionButton(
                        icon=ft.icons.CLOSE,
                        tooltip="Close",
                        bgcolor=ft.colors.RED,
                        foreground_color=ft.colors.WHITE,
                        on_click=lambda e: close_overlay(),
                    ),
                    left=300,
                    top=300,
                ),
                # Title
                ft.Container(
                    content=ft.Text(
                        "Quick Actions",
                        size=24,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.WHITE,
                        text_align=ft.TextAlign.CENTER,
                    ),
                    left=200,
                    top=150,
                    width=200,
                ),
            ]),
            visible=False,
            expand=True,
        )
        
        return self.fab_overlay
    
    def create_quick_actions_menu(self) -> ft.Container:
        """Create a menu-style quick actions panel."""
        action_tiles = []
        
        for action in self.quick_actions:
            tile = ft.ListTile(
                leading=ft.Icon(
                    getattr(ft.icons, action.icon.upper(), ft.icons.STAR),
                    color=action.color
                ),
                title=ft.Text(action.title, weight=ft.FontWeight.BOLD),
                subtitle=ft.Text(f"Navigate to {action.route}"),
                trailing=ft.Icon(ft.icons.ARROW_FORWARD_IOS, size=16),
                on_click=lambda e, a=action: self._execute_action(a, e.page),
            )
            action_tiles.append(tile)
        
        return ft.Container(
            content=ft.Column([
                ft.Container(
                    content=ft.Row([
                        ft.Icon(ft.icons.FLASH_ON, color=ft.colors.AMBER),
                        ft.Text("Quick Actions", weight=ft.FontWeight.BOLD, size=18),
                    ]),
                    padding=ft.padding.only(bottom=10),
                ),
                ft.Divider(height=1),
                *action_tiles,
            ], spacing=0),
            padding=15,
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.colors.GREY_300),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.colors.with_opacity(0.15, ft.colors.BLACK),
                offset=ft.Offset(0, 4)
            ),
        )
    
    def create_quick_actions_grid(self) -> ft.Container:
        """Create a grid-style quick actions panel."""
        action_cards = []
        
        for action in self.quick_actions:
            card = ft.Container(
                content=ft.Column([
                    ft.Icon(
                        getattr(ft.icons, action.icon.upper(), ft.icons.STAR),
                        size=32,
                        color=action.color
                    ),
                    ft.Text(
                        action.title,
                        size=12,
                        weight=ft.FontWeight.BOLD,
                        text_align=ft.TextAlign.CENTER,
                    ),
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                padding=15,
                border_radius=12,
                bgcolor=ft.colors.with_opacity(0.1, action.color),
                border=ft.border.all(1, ft.colors.with_opacity(0.3, action.color)),
                on_click=lambda e, a=action: self._execute_action(a, e.page),
                ink=True,
                tooltip=f"{action.title} - Navigate to {action.route}",
                width=100,
                height=80,
            )
            action_cards.append(card)
        
        # Arrange in rows of 4
        rows = []
        for i in range(0, len(action_cards), 4):
            row = ft.Row(
                action_cards[i:i+4],
                spacing=10,
                alignment=ft.MainAxisAlignment.START,
            )
            rows.append(row)
        
        return ft.Container(
            content=ft.Column([
                ft.Container(
                    content=ft.Row([
                        ft.Icon(ft.icons.DASHBOARD, color=ft.colors.BLUE),
                        ft.Text("Quick Actions", weight=ft.FontWeight.BOLD, size=18),
                    ]),
                    padding=ft.padding.only(bottom=15),
                ),
                *rows,
            ], spacing=10),
            padding=20,
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.colors.GREY_300),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.colors.with_opacity(0.15, ft.colors.BLACK),
                offset=ft.Offset(0, 4)
            ),
        )
    
    def create_context_quick_actions(self, context: str) -> ft.Container:
        """Create context-specific quick actions."""
        context_actions = {
            "dashboard": [
                QuickAction("add_inventory", "Add Item", "add_box", "inventory", {"action": "add"}, ft.colors.GREEN),
                QuickAction("new_experiment", "New Experiment", "science", "experiments", {"action": "new"}, ft.colors.BLUE),
                QuickAction("view_reports", "View Reports", "analytics", "reports", {}, ft.colors.PURPLE),
            ],
            "inventory": [
                QuickAction("add_inventory", "Add Item", "add_box", "inventory", {"action": "add"}, ft.colors.GREEN),
                QuickAction("import_csv", "Import CSV", "upload_file", "inventory", {"action": "import"}, ft.colors.BLUE),
                QuickAction("low_stock", "Low Stock", "warning", "inventory", {"filter": "low_stock"}, ft.colors.ORANGE),
            ],
            "experiments": [
                QuickAction("new_experiment", "New Experiment", "science", "experiments", {"action": "new"}, ft.colors.BLUE),
                QuickAction("add_measurement", "Add Data", "straighten", "experiments", {"action": "measurement"}, ft.colors.TEAL),
                QuickAction("experiment_template", "Use Template", "content_copy", "experiments", {"action": "template"}, ft.colors.PURPLE),
            ],
            "scheduling": [
                QuickAction("book_equipment", "Book Equipment", "event", "scheduling", {"action": "book"}, ft.colors.ORANGE),
                QuickAction("view_calendar", "Calendar View", "calendar_month", "scheduling", {"view": "calendar"}, ft.colors.BLUE),
                QuickAction("check_availability", "Availability", "schedule", "scheduling", {"action": "availability"}, ft.colors.GREEN),
            ],
        }
        
        actions = context_actions.get(context, [])
        if not actions:
            return ft.Container()
        
        action_buttons = []
        for action in actions:
            button = ft.ElevatedButton(
                content=ft.Row([
                    ft.Icon(getattr(ft.icons, action.icon.upper(), ft.icons.STAR), size=20),
                    ft.Text(action.title),
                ], spacing=8),
                style=ft.ButtonStyle(
                    bgcolor=action.color,
                    color=ft.colors.WHITE,
                    shape=ft.RoundedRectangleBorder(radius=8),
                ),
                on_click=lambda e, a=action: self._execute_action(a, e.page),
            )
            action_buttons.append(button)
        
        return ft.Container(
            content=ft.Column([
                ft.Text(f"Quick Actions for {context.title()}", 
                       weight=ft.FontWeight.BOLD, size=16),
                ft.Row(action_buttons, spacing=10, wrap=True),
            ], spacing=10),
            padding=15,
            bgcolor=ft.colors.BLUE_50,
            border_radius=8,
            border=ft.border.all(1, ft.colors.BLUE_200),
        )
    
    def _toggle_quick_actions(self, e):
        """Toggle quick actions overlay."""
        self.is_expanded = not self.is_expanded
        
        if self.fab_overlay:
            self.fab_overlay.visible = self.is_expanded
            self._update_fab_icon()
            e.page.update()
    
    def _update_fab_icon(self):
        """Update the main FAB icon."""
        if self.fab_main:
            self.fab_main.icon = ft.icons.CLOSE if self.is_expanded else ft.icons.ADD
            self.fab_main.bgcolor = ft.colors.RED if self.is_expanded else ft.colors.BLUE
    
    def _execute_action(self, action: QuickAction, page: ft.Page):
        """Execute a quick action."""
        # Close overlay if open
        if self.fab_overlay and self.fab_overlay.visible:
            self.fab_overlay.visible = False
            self.is_expanded = False
            self._update_fab_icon()
        
        # Show feedback
        page.show_snack_bar(
            ft.SnackBar(
                content=ft.Text(f"Executing: {action.title}"),
                bgcolor=action.color,
            )
        )
        
        # Execute navigation
        if self.on_navigate:
            self.on_navigate(action.route, action.data)
        
        page.update()
    
    def update_user_context(self, user: Dict[str, Any]):
        """Update user context for role-based actions."""
        self.current_user = user
        self.quick_actions = self._get_quick_actions()
    
    def get_keyboard_shortcuts(self) -> Dict[str, str]:
        """Get keyboard shortcuts for quick actions."""
        shortcuts = {}
        for i, action in enumerate(self.quick_actions[:9]):  # Limit to 9 for number keys
            shortcuts[f"ctrl+{i+1}"] = action.title
        return shortcuts
