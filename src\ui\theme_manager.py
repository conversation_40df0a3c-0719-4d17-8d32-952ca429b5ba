"""
Theme Manager for Science Laboratory Management System
Provides dark mode, light mode, and custom theme support
"""
import flet as ft
import json
import os
from typing import Dict, Any, Optional
from enum import Enum


class ThemeMode(Enum):
    """Theme mode enumeration."""
    LIGHT = "light"
    DARK = "dark"
    SYSTEM = "system"


class ColorScheme:
    """Color scheme definitions for different themes."""
    
    # Light theme colors
    LIGHT = {
        "primary": ft.colors.BLUE,
        "primary_variant": ft.colors.BLUE_700,
        "secondary": ft.colors.BLUE_ACCENT,
        "background": ft.colors.WHITE,
        "surface": ft.colors.WHITE,
        "error": ft.colors.RED,
        "success": ft.colors.GREEN,
        "warning": ft.colors.ORANGE,
        "info": ft.colors.BLUE,
        "on_primary": ft.colors.WHITE,
        "on_secondary": ft.colors.WHITE,
        "on_background": ft.colors.BLACK,
        "on_surface": ft.colors.BLACK,
        "on_error": ft.colors.WHITE,
        "text_primary": ft.colors.BLACK87,
        "text_secondary": ft.colors.BLACK54,
        "divider": ft.colors.BLACK12,
        "disabled": ft.colors.BLACK26,
        "card_background": ft.colors.WHITE,
        "app_bar_background": ft.colors.BLUE,
        "nav_rail_background": ft.colors.BLUE_50,
        "button_background": ft.colors.BLUE,
        "input_background": ft.colors.GREY_50,
        "shadow": ft.colors.BLACK12,
    }
    
    # Dark theme colors
    DARK = {
        "primary": ft.colors.BLUE_300,
        "primary_variant": ft.colors.BLUE_700,
        "secondary": ft.colors.BLUE_ACCENT_200,
        "background": ft.colors.GREY_900,
        "surface": ft.colors.GREY_800,
        "error": ft.colors.RED_300,
        "success": ft.colors.GREEN_300,
        "warning": ft.colors.ORANGE_300,
        "info": ft.colors.BLUE_300,
        "on_primary": ft.colors.BLACK,
        "on_secondary": ft.colors.BLACK,
        "on_background": ft.colors.WHITE,
        "on_surface": ft.colors.WHITE,
        "on_error": ft.colors.BLACK,
        "text_primary": ft.colors.WHITE,
        "text_secondary": ft.colors.WHITE70,
        "divider": ft.colors.WHITE12,
        "disabled": ft.colors.WHITE38,
        "card_background": ft.colors.GREY_800,
        "app_bar_background": ft.colors.GREY_900,
        "nav_rail_background": ft.colors.GREY_850,
        "button_background": ft.colors.BLUE_300,
        "input_background": ft.colors.GREY_700,
        "shadow": ft.colors.BLACK54,
    }


class ThemeManager:
    """
    Manages application themes and provides theme switching functionality.
    """
    
    def __init__(self, page: ft.Page):
        """
        Initialize the theme manager.
        
        Args:
            page: Flet page instance
        """
        self.page = page
        self.current_theme = ThemeMode.LIGHT
        self.custom_colors = {}
        self.theme_config_file = "config/theme_config.json"
        self.load_theme_config()
    
    def load_theme_config(self):
        """Load theme configuration from file."""
        try:
            if os.path.exists(self.theme_config_file):
                with open(self.theme_config_file, 'r') as f:
                    config = json.load(f)
                    self.current_theme = ThemeMode(config.get("current_theme", "light"))
                    self.custom_colors = config.get("custom_colors", {})
        except Exception as e:
            print(f"Error loading theme config: {e}")
            self.current_theme = ThemeMode.LIGHT
    
    def save_theme_config(self):
        """Save theme configuration to file."""
        try:
            os.makedirs(os.path.dirname(self.theme_config_file), exist_ok=True)
            config = {
                "current_theme": self.current_theme.value,
                "custom_colors": self.custom_colors
            }
            with open(self.theme_config_file, 'w') as f:
                json.dump(config, f, indent=2)
        except Exception as e:
            print(f"Error saving theme config: {e}")
    
    def get_current_colors(self) -> Dict[str, str]:
        """Get current theme colors."""
        if self.current_theme == ThemeMode.DARK:
            colors = ColorScheme.DARK.copy()
        else:
            colors = ColorScheme.LIGHT.copy()
        
        # Apply custom color overrides
        colors.update(self.custom_colors)
        return colors
    
    def set_theme(self, theme_mode: ThemeMode):
        """
        Set the application theme.
        
        Args:
            theme_mode: Theme mode to set
        """
        self.current_theme = theme_mode
        self.apply_theme()
        self.save_theme_config()
    
    def toggle_theme(self):
        """Toggle between light and dark themes."""
        if self.current_theme == ThemeMode.LIGHT:
            self.set_theme(ThemeMode.DARK)
        else:
            self.set_theme(ThemeMode.LIGHT)
    
    def apply_theme(self):
        """Apply the current theme to the page."""
        if self.current_theme == ThemeMode.DARK:
            self.page.theme_mode = ft.ThemeMode.DARK
            self.page.theme = self.create_dark_theme()
        else:
            self.page.theme_mode = ft.ThemeMode.LIGHT
            self.page.theme = self.create_light_theme()
        
        self.page.update()
    
    def create_light_theme(self) -> ft.Theme:
        """Create light theme configuration."""
        colors = ColorScheme.LIGHT
        
        return ft.Theme(
            color_scheme_seed=colors["primary"],
            use_material3=True,
            app_bar_theme=ft.AppBarTheme(
                bgcolor=colors["app_bar_background"],
                color=colors["on_primary"],
                elevation=4,
                title_text_style=ft.TextStyle(
                    size=20,
                    weight=ft.FontWeight.BOLD,
                    color=colors["on_primary"]
                )
            ),
            navigation_rail_theme=ft.NavigationRailTheme(
                bgcolor=colors["nav_rail_background"],
                selected_icon_theme=ft.IconThemeData(color=colors["primary"]),
                unselected_icon_theme=ft.IconThemeData(color=colors["text_secondary"]),
                selected_label_text_style=ft.TextStyle(color=colors["primary"]),
                unselected_label_text_style=ft.TextStyle(color=colors["text_secondary"])
            ),
            elevated_button_theme=ft.ElevatedButtonThemeData(
                style=ft.ButtonStyle(
                    bgcolor=colors["button_background"],
                    color=colors["on_primary"],
                    elevation=2,
                    shape=ft.RoundedRectangleBorder(radius=8)
                )
            ),
            card_theme=ft.CardTheme(
                color=colors["card_background"],
                elevation=2,
                shape=ft.RoundedRectangleBorder(radius=12)
            ),
            input_decoration_theme=ft.InputDecorationTheme(
                filled=True,
                fill_color=colors["input_background"],
                border=ft.InputBorder.OUTLINE,
                border_radius=8
            )
        )
    
    def create_dark_theme(self) -> ft.Theme:
        """Create dark theme configuration."""
        colors = ColorScheme.DARK
        
        return ft.Theme(
            color_scheme_seed=colors["primary"],
            use_material3=True,
            app_bar_theme=ft.AppBarTheme(
                bgcolor=colors["app_bar_background"],
                color=colors["on_background"],
                elevation=4,
                title_text_style=ft.TextStyle(
                    size=20,
                    weight=ft.FontWeight.BOLD,
                    color=colors["on_background"]
                )
            ),
            navigation_rail_theme=ft.NavigationRailTheme(
                bgcolor=colors["nav_rail_background"],
                selected_icon_theme=ft.IconThemeData(color=colors["primary"]),
                unselected_icon_theme=ft.IconThemeData(color=colors["text_secondary"]),
                selected_label_text_style=ft.TextStyle(color=colors["primary"]),
                unselected_label_text_style=ft.TextStyle(color=colors["text_secondary"])
            ),
            elevated_button_theme=ft.ElevatedButtonThemeData(
                style=ft.ButtonStyle(
                    bgcolor=colors["button_background"],
                    color=colors["on_primary"],
                    elevation=2,
                    shape=ft.RoundedRectangleBorder(radius=8)
                )
            ),
            card_theme=ft.CardTheme(
                color=colors["card_background"],
                elevation=2,
                shape=ft.RoundedRectangleBorder(radius=12)
            ),
            input_decoration_theme=ft.InputDecorationTheme(
                filled=True,
                fill_color=colors["input_background"],
                border=ft.InputBorder.OUTLINE,
                border_radius=8
            )
        )
    
    def create_theme_toggle_button(self) -> ft.IconButton:
        """Create a theme toggle button."""
        def toggle_theme_handler(e):
            self.toggle_theme()
            # Update button icon
            if self.current_theme == ThemeMode.DARK:
                e.control.icon = ft.icons.LIGHT_MODE
                e.control.tooltip = "Switch to Light Mode"
            else:
                e.control.icon = ft.icons.DARK_MODE
                e.control.tooltip = "Switch to Dark Mode"
            e.control.update()
        
        icon = ft.icons.DARK_MODE if self.current_theme == ThemeMode.LIGHT else ft.icons.LIGHT_MODE
        tooltip = "Switch to Dark Mode" if self.current_theme == ThemeMode.LIGHT else "Switch to Light Mode"
        
        return ft.IconButton(
            icon=icon,
            tooltip=tooltip,
            on_click=toggle_theme_handler
        )
    
    def get_themed_color(self, color_name: str) -> str:
        """
        Get a color from the current theme.
        
        Args:
            color_name: Name of the color to get
            
        Returns:
            Color value
        """
        colors = self.get_current_colors()
        return colors.get(color_name, ft.colors.BLUE)
    
    def create_theme_settings_dialog(self) -> ft.AlertDialog:
        """Create a theme settings dialog."""
        def close_dialog(e):
            dialog.open = False
            self.page.update()
        
        def apply_theme_setting(e):
            selected_theme = theme_dropdown.value
            if selected_theme == "light":
                self.set_theme(ThemeMode.LIGHT)
            elif selected_theme == "dark":
                self.set_theme(ThemeMode.DARK)
            else:
                self.set_theme(ThemeMode.SYSTEM)
            close_dialog(e)
        
        theme_dropdown = ft.Dropdown(
            label="Theme",
            value=self.current_theme.value,
            options=[
                ft.dropdown.Option("light", "Light Theme"),
                ft.dropdown.Option("dark", "Dark Theme"),
                ft.dropdown.Option("system", "System Theme"),
            ],
            width=200
        )
        
        dialog = ft.AlertDialog(
            title=ft.Text("Theme Settings"),
            content=ft.Container(
                content=ft.Column([
                    ft.Text("Choose your preferred theme:"),
                    theme_dropdown,
                    ft.Container(height=10),
                    ft.Text("Preview:", weight=ft.FontWeight.BOLD),
                    ft.Container(
                        content=ft.Row([
                            ft.Container(
                                content=ft.Text("Light", color=ft.colors.BLACK),
                                bgcolor=ft.colors.WHITE,
                                padding=10,
                                border_radius=8,
                                border=ft.border.all(1, ft.colors.GREY_300)
                            ),
                            ft.Container(
                                content=ft.Text("Dark", color=ft.colors.WHITE),
                                bgcolor=ft.colors.GREY_900,
                                padding=10,
                                border_radius=8,
                                border=ft.border.all(1, ft.colors.GREY_600)
                            ),
                        ], spacing=10),
                        margin=ft.margin.only(top=10)
                    )
                ], spacing=10),
                width=300,
                padding=10
            ),
            actions=[
                ft.TextButton("Cancel", on_click=close_dialog),
                ft.ElevatedButton("Apply", on_click=apply_theme_setting),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        return dialog
