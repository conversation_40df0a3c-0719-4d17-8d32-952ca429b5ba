#!/usr/bin/env python
"""
Simple run script for the Science Laboratory Management System.
This script provides a basic login interface.
"""
import sys
import os

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Import flet
import flet as ft

def main(page: ft.Page):
    """
    Main application entry point with a simplified interface.
    
    Args:
        page (ft.Page): The Flet page object
    """
    # Configure the page with basic settings
    page.title = "Science Laboratory Management System"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window_width = 1200
    page.window_height = 800
    page.window_min_width = 800
    page.window_min_height = 600
    page.padding = 0
    page.spacing = 0
    page.window_center()
    
    # Create a simple login view
    def login_clicked(e):
        page.clean()
        page.add(ft.Text("Login successful! Main application would load here."))
        page.update()
    
    # Create a simple login form
    login_card = ft.Card(
        content=ft.Container(
            content=ft.Column([
                ft.Text("Science Laboratory Management System", size=24, weight=ft.FontWeight.BOLD),
                ft.Text("Login", size=18),
                ft.TextField(label="Username", autofocus=True),
                ft.TextField(label="Password", password=True),
                ft.ElevatedButton("Login", on_click=login_clicked),
            ], spacing=20),
            padding=20,
        ),
        width=400,
    )
    
    # Center the login card
    page.add(
        ft.Container(
            content=login_card,
            alignment=ft.alignment.center,
            expand=True,
        )
    )
    
    page.update()

if __name__ == "__main__":
    # Run the application
    ft.app(target=main)