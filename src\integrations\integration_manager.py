# 6. Additional Features:
# o Equipment management and maintenance tracking
# o Student/user access request system
# o Mobile-responsive design
# o Offline functionality
import logging
import os
import sys

# Add the project root directory to the Python path if not already there
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Now we can use absolute imports
from src.integrations.email_system import EmailSystem
from src.integrations.barcode_system import BarcodeSystem
from src.integrations.calendar_system import CalendarSystem
from src.integrations.file_storage import FileStorage

class IntegrationManager:
    """
    Integration manager for the Science Laboratory Management System.
    Coordinates all integration components and provides a unified interface.
    """
    
    def __init__(self, config, db=None):
        """
        Initialize the integration manager.
        
        Args:
            config: The application configuration
            db: Database connection (optional)
        """
        self.config = config
        self.db = db
        self.logger = logging.getLogger("integration_manager")
        
        # Initialize integration components
        self.email_system = None
        self.barcode_system = None
        self.calendar_system = None
        self.file_storage = None
        
        # Initialize enabled components
        self.initialize_components()
    
    def initialize_components(self):
        """Initialize integration components based on configuration."""
        try:
            # Initialize email system if enabled
            print("Checking email system configuration...")
            if self.config.get("email", {}).get("enabled", False):
                print("Email system enabled, initializing...")
                self.logger.info("Initializing email system")
                self.email_system = EmailSystem(self.config)
                
                print("Creating default email templates...")
                # Create default email templates
                self.email_system.create_default_templates()
            
            # Initialize barcode system
            print("Initializing barcode system...")
            self.logger.info("Initializing barcode system")
            self.barcode_system = BarcodeSystem(self.config)
            
            # Initialize calendar system
            print("Initializing calendar system...")
            self.logger.info("Initializing calendar system")
            self.calendar_system = CalendarSystem(self.config)
            
            # Initialize file storage
            print("Initializing file storage...")
            self.logger.info("Initializing file storage")
            self.file_storage = FileStorage(self.config)
            
            print("All components initialized successfully!")
            self.logger.info("Integration components initialized")
        
        except Exception as e:
            import traceback
            print(f"Error initializing components: {str(e)}")
            print(traceback.format_exc())
            self.logger.error(f"Error initializing integration components: {str(e)}")
    
    def send_email(self, to, subject, body, html_body=None, cc=None, bcc=None, attachments=None):
        """
        Send an email.
        
        Args:
            to (str or list): Recipient email address(es)
            subject (str): Email subject
            body (str): Email body (plain text)
            html_body (str, optional): Email body (HTML)
            cc (str or list, optional): CC recipient(s)
            bcc (str or list, optional): BCC recipient(s)
            attachments (list, optional): List of attachment file paths
            
        Returns:
            bool: True if email was queued, False otherwise
        """
        if not self.email_system:
            self.logger.warning("Email system not initialized")
            return False
        
        return self.email_system.send_email(to, subject, body, html_body, cc, bcc, attachments)
    
    def send_template_email(self, to, template_name, template_data, subject=None, cc=None, bcc=None, attachments=None):
        """
        Send an email using a template.
        
        Args:
            to (str or list): Recipient email address(es)
            template_name (str): Name of the template
            template_data (dict): Data for the template
            subject (str, optional): Email subject (if None, extracted from template)
            cc (str or list, optional): CC recipient(s)
            bcc (str or list, optional): BCC recipient(s)
            attachments (list, optional): List of attachment file paths
            
        Returns:
            bool: True if email was queued, False otherwise
        """
        if not self.email_system:
            self.logger.warning("Email system not initialized")
            return False
        
        return self.email_system.send_template_email(to, template_name, template_data, subject, cc, bcc, attachments)
    
    def generate_qr_code(self, data, size=200, filename=None, include_label=True, label_text=None):
        """
        Generate a QR code.
        
        Args:
            data (str or dict): Data to encode in the QR code
            size (int, optional): Size of the QR code in pixels
            filename (str, optional): Filename to save the QR code
            include_label (bool, optional): Whether to include a label
            label_text (str, optional): Text for the label
            
        Returns:
            tuple: (image, filename) where image is a PIL Image and filename is the path if saved
        """
        if not self.barcode_system:
            self.logger.warning("Barcode system not initialized")
            return (None, None)
        
        return self.barcode_system.generate_qr_code(data, size, 4, filename, include_label, label_text)
    
    def generate_barcode_for_item(self, item, include_details=True, filename=None):
        """
        Generate a barcode for an inventory item.
        
        Args:
            item (dict): The inventory item
            include_details (bool, optional): Whether to include item details
            filename (str, optional): Filename to save the barcode
            
        Returns:
            tuple: (image, filename) where image is a PIL Image and filename is the path if saved
        """
        if not self.barcode_system:
            self.logger.warning("Barcode system not initialized")
            return (None, None)
        
        return self.barcode_system.generate_barcode_for_item(item, include_details, filename)
    
    def scan_barcode_from_image(self, image_path):
        """
        Scan barcodes from an image file.
        
        Args:
            image_path (str): Path to the image file
            
        Returns:
            list: List of decoded barcode data
        """
        if not self.barcode_system:
            self.logger.warning("Barcode system not initialized")
            return []
        
        return self.barcode_system.scan_from_image(image_path)
    
    def process_barcode_data(self, data):
        """
        Process barcode data and return relevant information.
        
        Args:
            data (dict): The barcode data
            
        Returns:
            dict: Processed data with additional information
        """
        if not self.barcode_system or not self.db:
            self.logger.warning("Barcode system or database not initialized")
            return {"error": "Barcode system or database not initialized"}
        
        return self.barcode_system.process_barcode_data(data, self.db)
    
    def export_calendar(self, events, filename=None, calendar_name="Lab System Calendar"):
        """
        Export events to an iCalendar file.
        
        Args:
            events (list): List of events
            filename (str, optional): Filename for the calendar
            calendar_name (str, optional): Name of the calendar
            
        Returns:
            str: Path to the exported calendar file or None if failed
        """
        if not self.calendar_system:
            self.logger.warning("Calendar system not initialized")
            return None
        
        return self.calendar_system.export_calendar(events, filename, calendar_name)
    
    def export_lab_schedule(self, start_date=None, end_date=None, room_id=None, user_id=None, filename=None):
        """
        Export lab schedule to an iCalendar file.
        
        Args:
            start_date (str, optional): Start date for filtering
            end_date (str, optional): End date for filtering
            room_id (int, optional): Room ID for filtering
            user_id (int, optional): User ID for filtering
            filename (str, optional): Filename for the calendar
            
        Returns:
            str: Path to the exported calendar file or None if failed
        """
        if not self.calendar_system or not self.db:
            self.logger.warning("Calendar system or database not initialized")
            return None
        
        return self.calendar_system.export_lab_schedule(self.db, start_date, end_date, room_id, user_id, filename)
    
    def generate_calendar_link(self, event, calendar_type="google"):
        """
        Generate a calendar event link.
        
        Args:
            event (dict): Event data
            calendar_type (str, optional): Type of calendar (google or outlook)
            
        Returns:
            str: Calendar event link
        """
        if not self.calendar_system:
            self.logger.warning("Calendar system not initialized")
            return None
        
        if calendar_type.lower() == "google":
            return self.calendar_system.generate_google_calendar_link(event)
        elif calendar_type.lower() == "outlook":
            return self.calendar_system.generate_outlook_calendar_link(event)
        else:
            self.logger.warning(f"Unsupported calendar type: {calendar_type}")
            return None
    
    def save_file(self, file_data, filename=None, directory=None, entity_type=None, entity_id=None, user_id=None, metadata=None):
        """
        Save a file to storage.
        
        Args:
            file_data (bytes or str): File data or path to file
            filename (str, optional): Filename
            directory (str, optional): Subdirectory to save in
            entity_type (str, optional): Type of entity the file is associated with
            entity_id (int, optional): ID of the entity the file is associated with
            user_id (int, optional): ID of the user uploading the file
            metadata (dict, optional): Additional metadata for the file
            
        Returns:
            dict: File information or None if failed
        """
        if not self.file_storage:
            self.logger.warning("File storage not initialized")
            return None
        
        return self.file_storage.save_file(file_data, filename, directory, entity_type, entity_id, user_id, metadata)
    
    def save_file_from_upload(self, upload_data, entity_type=None, entity_id=None, user_id=None, metadata=None):
        """
        Save a file from upload data.
        
        Args:
            upload_data: Upload data (depends on web framework)
            entity_type (str, optional): Type of entity the file is associated with
            entity_id (int, optional): ID of the entity the file is associated with
            user_id (int, optional): ID of the user uploading the file
            metadata (dict, optional): Additional metadata for the file
            
        Returns:
            dict: File information or None if failed
        """
        if not self.file_storage:
            self.logger.warning("File storage not initialized")
            return None
        
        return self.file_storage.save_file_from_upload(upload_data, entity_type, entity_id, user_id, metadata)
    
    def get_file(self, file_id=None, file_path=None):
        """
        Get file information.
        
        Args:
            file_id (int, optional): File ID in database
            file_path (str, optional): Path to file
            
        Returns:
            dict: File information or None if not found
        """
        if not self.file_storage:
            self.logger.warning("File storage not initialized")
            return None
        
        return self.file_storage.get_file(file_id, file_path)
    
    def list_files(self, directory=None, entity_type=None, entity_id=None, extensions=None):
        """
        List files in a directory.
        
        Args:
            directory (str, optional): Subdirectory to list
            entity_type (str, optional): Type of entity to filter by
            entity_id (int, optional): ID of entity to filter by
            extensions (list, optional): List of extensions to filter by
            
        Returns:
            list: List of file information
        """
        if not self.file_storage:
            self.logger.warning("File storage not initialized")
            return []
        
        return self.file_storage.list_files(directory, entity_type, entity_id, extensions)
    
    def create_zip_archive(self, files, output_filename=None):
        """
        Create a ZIP archive of files.
        
        Args:
            files (list): List of file paths
            output_filename (str, optional): Output filename
            
        Returns:
            str: Path to ZIP file or None if failed
        """
        if not self.file_storage:
            self.logger.warning("File storage not initialized")
            return None
        
        return self.file_storage.create_zip_archive(files, output_filename)
    
    def set_database(self, db):
        """
        Set the database connection.
        
        Args:
            db: Database connection
        """
        self.db = db
    
    def shutdown(self):
        """Shutdown integration components."""
        try:
            # Shutdown email system
            if self.email_system:
                self.email_system.stop_worker()
            
            # Shutdown file storage
            if self.file_storage:
                self.file_storage.stop_processing_thread()
            
            self.logger.info("Integration components shut down")
        
        except Exception as e:
            self.logger.error(f"Error shutting down integration components: {str(e)}")
    
    def get_component_status(self):
        """
        Get status of integration components.
        
        Returns:
            dict: Status of integration components
        """
        status = {
            "email_system": {
                "initialized": self.email_system is not None,
                "enabled": self.config.get("email", {}).get("enabled", False),
                "queue_size": self.email_system.get_queue_size() if self.email_system else 0
            },
            "barcode_system": {
                "initialized": self.barcode_system is not None
            },
            "calendar_system": {
                "initialized": self.calendar_system is not None
            },
            "file_storage": {
                "initialized": self.file_storage is not None,
                "stats": self.file_storage.get_storage_stats() if self.file_storage else None
            }
        }
        
        return status