#!/usr/bin/env python
"""
Science Laboratory Management System Application
Main desktop application with complete laboratory management functionality.
"""
import flet as ft  # type: ignore
import os
import sys
import logging
from datetime import datetime
import json

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Import the necessary modules
from src.utils.logger import setup_logger
from src.database.db_manager import DatabaseManager
from src.auth.auth_manager import AuthManager

# Import enhanced navigation components
try:
    from src.ui.enhanced_search import EnhancedSearchComponent
    from src.ui.favorites_manager import FavoritesManager
    from src.ui.quick_actions import QuickActionsManager
    from src.ui.responsive_navigation import ResponsiveNavigation
    from src.analytics.navigation_analytics import NavigationAnalytics
except ImportError as e:
    logger.warning(f"Enhanced navigation components not available: {e}")
    EnhancedSearchComponent = None
    FavoritesManager = None
    QuickActionsManager = None
    ResponsiveNavigation = None
    NavigationAnalytics = None

# Set up logging
logger = setup_logger("lab_management")

class LabManagementApp:
    """
    Main application class for the Science Laboratory Management System.
    """

    def __init__(self, page: ft.Page):
        """
        Initialize the application.

        Args:
            page (ft.Page): The Flet page
        """
        self.page = page
        self.logger = logger
        self.logger.info("Initializing application")

        # Initialize database and authentication
        self.db_manager = DatabaseManager()
        self.auth_manager = AuthManager(self.db_manager)

        # Load configuration
        self.config_path = os.path.join(project_root, "config", "app_config.json")
        self.config = self.load_config()

        # Initialize enhanced navigation components
        self.current_user = None
        self.session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Initialize navigation analytics
        if NavigationAnalytics:
            self.nav_analytics = NavigationAnalytics()
        else:
            self.nav_analytics = None

        # Initialize enhanced search
        if EnhancedSearchComponent:
            self.enhanced_search = EnhancedSearchComponent(on_navigate=self.navigate_to)
        else:
            self.enhanced_search = None

        # Initialize favorites manager
        if FavoritesManager:
            self.favorites_manager = FavoritesManager(
                user_id=self.current_user.get("id", "default") if self.current_user else "default",
                on_navigate=self.navigate_to
            )
        else:
            self.favorites_manager = None

        # Initialize quick actions
        if QuickActionsManager:
            self.quick_actions = QuickActionsManager(
                on_navigate=self.navigate_to,
                current_user=self.current_user
            )
        else:
            self.quick_actions = None

        # Initialize responsive navigation
        if ResponsiveNavigation:
            self.responsive_nav = ResponsiveNavigation(
                on_navigate=self.navigate_to,
                current_user=self.current_user
            )
        else:
            self.responsive_nav = None

        # Set up the page
        self.setup_page()

        # Create the layout
        self.create_layout()

        # Navigate to the login page if not authenticated, otherwise to dashboard
        if self.auth_manager.is_authenticated():
            self.navigate_to("dashboard")
        else:
            self.navigate_to("login")

    def load_config(self):
        """
        Load the application configuration.

        Returns:
            dict: The application configuration
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, "r") as f:
                    return json.load(f)
            else:
                self.logger.error(f"Configuration file not found: {self.config_path}")
                return {}
        except Exception as e:
            self.logger.error(f"Error loading configuration: {str(e)}")
            return {}

    def setup_page(self):
        """Configure the application page settings."""
        # Set page properties
        self.page.title = "Science Laboratory Management System"
        self.page.padding = 0
        self.page.theme_mode = ft.ThemeMode.LIGHT
        self.page.fonts = {
            "Roboto": "https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap",
            "Montserrat": "https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700&display=swap",
        }

        # Set window properties using the new API
        if hasattr(self.page, 'window') and self.page.window:
            self.page.window.width = 1200
            self.page.window.height = 800
            self.page.window.min_width = 800
            self.page.window.min_height = 600

            # Handle window close
            self.page.window.on_event = self.handle_window_event

        # Set up theme
        self.page.theme = ft.Theme(
            color_scheme_seed=ft.Colors.BLUE,
            visual_density=ft.VisualDensity.COMFORTABLE,
            font_family="Roboto",
        )

        # Set up error handling
        self.page.on_error = self.handle_error

        # Set up keyboard event handler
        self.page.on_keyboard_event = self.handle_keyboard_event

        # Initialize keyboard shortcuts
        self.keyboard_shortcuts = {
            "global": {
                "ctrl+/": self.show_keyboard_shortcuts,
                "ctrl+h": self.show_help,
                "ctrl+s": self.show_settings_page,
                "ctrl+f": self.show_global_search,
                "ctrl+n": self.show_notifications,
                "ctrl+l": self.logout,
            },
            "dashboard": {
                "ctrl+1": lambda e: self.navigate_to("dashboard"),
                "ctrl+2": lambda e: self.navigate_to("inventory"),
                "ctrl+3": lambda e: self.navigate_to("experiments"),
                "ctrl+4": lambda e: self.navigate_to("scheduling"),
                "ctrl+5": lambda e: self.navigate_to("reports"),
                "ctrl+6": lambda e: self.navigate_to("users"),
            },
            "inventory": {
                "ctrl+a": self.show_add_inventory_dialog,
            },
            "experiments": {
                "ctrl+a": self.show_add_experiment_dialog,
            },
            "users": {
                "ctrl+a": self.show_add_user_dialog,
            },
        }

        # Current active shortcuts (global + page-specific)
        self.active_shortcuts = {**self.keyboard_shortcuts["global"]}

    def create_layout(self):
        """Create the main application layout."""
        # Create app bar with breadcrumb navigation and notification center
        self.breadcrumb_text = ft.Text("Dashboard", color=ft.colors.WHITE, size=14)

        # Create notification icon with badge
        self.notification_count = 3
        self.notification_icon = ft.IconButton(
            icon=ft.icons.NOTIFICATIONS_OUTLINED,
            icon_color=ft.colors.WHITE,
            tooltip="Notifications",
            on_click=self.show_notifications,
            badge="3"
        )

        # Create app bar
        self.app_bar = ft.AppBar(
            leading=ft.Icon(ft.icons.SCIENCE, color=ft.colors.WHITE),
            title=ft.Row(
                [
                    ft.Text("Lab Management", weight=ft.FontWeight.BOLD),
                    ft.Icon(ft.icons.CHEVRON_RIGHT, size=20, color=ft.colors.WHITE70),
                    self.breadcrumb_text,
                ],
                spacing=5,
                alignment=ft.MainAxisAlignment.START,
            ),
            center_title=False,
            bgcolor=ft.colors.BLUE,
            color=ft.colors.WHITE,
            toolbar_height=60,
            actions=[
                # Enhanced search component or fallback
                self.enhanced_search.create_search_component() if self.enhanced_search else ft.IconButton(
                    icon=ft.icons.SEARCH,
                    tooltip="Global Search (Ctrl+F)",
                    on_click=self.show_global_search,
                ),
                self.notification_icon,
                # Favorites toggle button
                ft.IconButton(
                    icon=ft.icons.STAR_OUTLINE,
                    tooltip="Favorites",
                    on_click=self.show_favorites_panel,
                ) if self.favorites_manager else ft.Container(),
                ft.IconButton(
                    icon=ft.icons.ACCOUNT_CIRCLE,
                    tooltip="User Profile",
                    on_click=self.show_user_menu,
                ),
                ft.IconButton(
                    icon=ft.icons.HELP_OUTLINE,
                    tooltip="Help",
                    on_click=self.show_help,
                ),
            ],
            elevation=4,
        )

        # Create enhanced navigation rail
        self.nav_rail = ft.NavigationRail(
            selected_index=0,
            label_type=ft.NavigationRailLabelType.ALL,
            min_width=100,
            min_extended_width=220,
            extended=True,  # Start with extended view
            bgcolor=ft.colors.BLUE_50,
            leading=ft.Container(
                content=ft.Column([
                    ft.Container(
                        content=ft.Icon(
                            name=ft.icons.SCIENCE_OUTLINED,
                            size=40,
                            color=ft.colors.BLUE,
                        ),
                        margin=ft.margin.only(top=20, bottom=5),
                    ),
                    ft.Text(
                        "Lab Management",
                        size=14,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.BLUE,
                    ),
                    ft.Divider(thickness=1, height=20),
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.only(bottom=10),
            ),
            trailing=ft.Container(
                content=ft.Column([
                    ft.IconButton(
                        icon=ft.icons.SETTINGS,
                        icon_color=ft.colors.BLUE_900,
                        tooltip="Settings",
                        on_click=lambda e: self.navigate_to("settings"),
                    ),
                    ft.IconButton(
                        icon=ft.icons.HELP_OUTLINE,
                        icon_color=ft.colors.BLUE_900,
                        tooltip="Help",
                        on_click=self.show_help,
                    ),
                    ft.IconButton(
                        icon=ft.icons.LOGOUT,
                        icon_color=ft.colors.RED,
                        tooltip="Logout",
                        on_click=lambda e: self.logout(),
                    ),
                    ft.Container(height=20),
                    ft.IconButton(
                        icon=ft.icons.MENU,
                        icon_color=ft.colors.BLUE_900,
                        tooltip="Toggle Navigation",
                        on_click=self.toggle_nav_rail,
                    ),
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.only(bottom=20),
            ),
            group_alignment=0,  # Align destinations to the top
            destinations=[
                ft.NavigationRailDestination(
                    icon=ft.icons.DASHBOARD_OUTLINED,
                    selected_icon=ft.icons.DASHBOARD,
                    label="Dashboard",
                    padding=15,
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.INVENTORY_2_OUTLINED,
                    selected_icon=ft.icons.INVENTORY_2,
                    label="Inventory",
                    padding=15,
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.SCIENCE_OUTLINED,
                    selected_icon=ft.icons.SCIENCE,
                    label="Experiments",
                    padding=15,
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.CALENDAR_MONTH_OUTLINED,
                    selected_icon=ft.icons.CALENDAR_MONTH,
                    label="Scheduling",
                    padding=15,
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.INSERT_CHART_OUTLINED,
                    selected_icon=ft.icons.INSERT_CHART,
                    label="Reports",
                    padding=15,
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.PEOPLE_OUTLINE,
                    selected_icon=ft.icons.PEOPLE,
                    label="Users",
                    padding=15,
                ),
            ],
            on_change=self.nav_rail_change,
            visible=False,  # Will be shown after login
        )

        # Create main content area
        self.content_area = ft.Container(
            expand=True,
            content=ft.Column(
                [
                    ft.Text("Welcome to the Science Laboratory Management System"),
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.CENTER,
            ),
        )

        # Create footer
        self.footer = ft.Container(
            content=ft.Row(
                [
                    ft.Text("© 2025 Science Laboratory Management System"),
                    ft.Container(expand=True),
                    ft.Text(f"Version: {self.config.get('app', {}).get('version', '1.0.0')}"),
                ],
                spacing=10,
            ),
            padding=5,
            bgcolor=ft.Colors.BLUE_50,
        )

        # Create main layout
        self.page.add(
            ft.Column(
                [
                    self.app_bar,
                    ft.Container(height=1, bgcolor=ft.Colors.BLUE_200),
                    ft.Row(
                        [
                            self.nav_rail,
                            ft.VerticalDivider(width=1, visible=False),  # Will be shown after login
                            self.content_area,
                        ],
                        expand=True,
                    ),
                    self.footer,
                ],
                expand=True
            )
        )

    def nav_rail_change(self, e):
        """
        Handle navigation rail changes.

        Args:
            e: The change event
        """
        index = e.control.selected_index

        if index == 0:
            self.navigate_to("dashboard")
        elif index == 1:
            self.navigate_to("inventory")
        elif index == 2:
            self.navigate_to("experiments")
        elif index == 3:
            self.navigate_to("scheduling")
        elif index == 4:
            self.navigate_to("reports")
        elif index == 5:
            self.navigate_to("users")

    def toggle_nav_rail(self, e):
        """
        Toggle the navigation rail between extended and compact modes.

        Args:
            e: The click event
        """
        self.nav_rail.extended = not self.nav_rail.extended

        # Update the icon based on the current state
        if self.nav_rail.extended:
            e.control.icon = ft.icons.MENU
            e.control.tooltip = "Collapse Navigation"
        else:
            e.control.icon = ft.icons.MENU_OPEN
            e.control.tooltip = "Expand Navigation"

        self.page.update()

    def show_user_menu(self, e):
        """
        Show the user menu.

        Args:
            e: The click event
        """
        # Get current user
        user = self.auth_manager.get_current_user()

        if user is None:
            # Not logged in, show login option
            # Only set nav_rail index if it has destinations
            if hasattr(self, 'nav_rail') and self.nav_rail.destinations and len(self.nav_rail.destinations) > 0:
                self.nav_rail.selected_index = 0

            menu_items = [
                ft.PopupMenuItem(
                    text="Login",
                    icon=ft.Icons.LOGIN,
                    on_click=lambda e: self.navigate_to("login"),
                ),
            ]
        else:
            # Logged in, show user options
            # Only set nav_rail index if it has destinations
            if hasattr(self, 'nav_rail') and self.nav_rail.destinations and len(self.nav_rail.destinations) > 2:
                self.nav_rail.selected_index = 2

            menu_items = [
                ft.PopupMenuItem(
                    text=f"Logged in as {user['username']}",
                    icon=ft.Icons.PERSON,
                    disabled=True,
                ),
                ft.PopupMenuItem(
                    text="Profile",
                    icon=ft.Icons.ACCOUNT_CIRCLE,
                    on_click=lambda e: self.navigate_to("profile"),
                ),
                ft.PopupMenuItem(
                    text="Settings",
                    icon=ft.Icons.SETTINGS,
                    on_click=lambda e: self.navigate_to("settings"),
                ),
                ft.PopupMenuItem(
                    text="Logout",
                    icon=ft.Icons.LOGOUT,
                    on_click=lambda e: self.logout(),
                ),
            ]

        # Create popup menu
        menu = ft.PopupMenuButton(
            items=menu_items,
        )

        # Show the menu
        menu.open = True
        self.page.update()

    def navigate_to(self, route_name, params=None):
        """
        Navigate to a specific route.

        Args:
            route_name (str): The name of the route to navigate to
            params (dict, optional): Parameters to pass to the route
        """
        self.logger.info(f"Navigated to {route_name}")

        # Check if authentication is required for this route
        auth_required_routes = [
            "dashboard", "inventory", "experiments", "scheduling", "reports",
            "profile", "settings", "users"
        ]

        if route_name in auth_required_routes and not self.auth_manager.is_authenticated():
            # Redirect to login
            self.show_error("You must be logged in to access this page")
            route_name = "login"

        # Update breadcrumb text
        route_display_names = {
            "dashboard": "Dashboard",
            "inventory": "Inventory",
            "experiments": "Experiments",
            "scheduling": "Scheduling",
            "reports": "Reports",
            "users": "User Management",
            "profile": "User Profile",
            "settings": "Settings",
            "login": "Login",
        }

        # Update breadcrumb text if the route has a display name
        if route_name in route_display_names:
            self.breadcrumb_text.value = route_display_names[route_name]
        else:
            self.breadcrumb_text.value = route_name.capitalize()

        # Register keyboard shortcuts for this page
        self.register_keyboard_shortcuts(route_name)

        # Clear the content area
        if route_name == "login":
            # Hide navigation rail for login page
            self.nav_rail.visible = False
            self.page.controls[0].controls[2].controls[1].visible = False  # Hide vertical divider
            self.show_login_page()
        elif route_name == "dashboard":
            # Show navigation rail for dashboard and other pages
            self.nav_rail.visible = True
            self.page.controls[0].controls[2].controls[1].visible = True  # Show vertical divider
            self.nav_rail.selected_index = 0
            self.show_dashboard_page()
        elif route_name == "inventory":
            self.nav_rail.visible = True
            self.page.controls[0].controls[2].controls[1].visible = True
            self.nav_rail.selected_index = 1
            self.show_inventory_page()
        elif route_name == "experiments":
            self.nav_rail.visible = True
            self.page.controls[0].controls[2].controls[1].visible = True
            self.nav_rail.selected_index = 2
            self.show_experiments_page()
        elif route_name == "scheduling":
            self.nav_rail.visible = True
            self.page.controls[0].controls[2].controls[1].visible = True
            self.nav_rail.selected_index = 3
            self.show_scheduling_page()
        elif route_name == "reports":
            self.nav_rail.visible = True
            self.page.controls[0].controls[2].controls[1].visible = True
            self.nav_rail.selected_index = 4
            self.show_reports_page()
        elif route_name == "users":
            self.nav_rail.visible = True
            self.page.controls[0].controls[2].controls[1].visible = True
            self.nav_rail.selected_index = 5
            self.show_users_page()
        elif route_name == "profile":
            self.nav_rail.visible = True
            self.page.controls[0].controls[2].controls[1].visible = True
            self.show_profile_page()
        elif route_name == "settings":
            self.nav_rail.visible = True
            self.page.controls[0].controls[2].controls[1].visible = True
            self.show_settings_page()
        else:
            self.content_area.content = ft.Column(
                [
                    ft.Text(f"This is the {route_name} page"),
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.CENTER,
            )

        # Track navigation analytics
        if self.nav_analytics and self.current_user:
            from_route = getattr(self, 'current_route', None)
            self.nav_analytics.track_navigation(
                user_id=self.current_user.get("id", "unknown"),
                session_id=self.session_id,
                to_route=route_name,
                method='click',
                from_route=from_route
            )
            self.current_route = route_name

        # Update the page
        self.page.update()

    # Enhanced Navigation Methods
    def show_global_search(self, e=None):
        """Show enhanced global search overlay."""
        if self.enhanced_search:
            self.enhanced_search.show_overlay(self.page)
        else:
            # Fallback to basic search
            self.page.show_snack_bar(
                ft.SnackBar(content=ft.Text("Search functionality - use Ctrl+F"))
            )

    def show_favorites_panel(self, e=None):
        """Show favorites management panel."""
        if not self.favorites_manager:
            return

        def close_dialog():
            dialog.open = False
            self.page.update()

        # Create favorites panel content
        favorites_content = ft.Column([
            self.favorites_manager.create_favorites_panel(),
            ft.Container(height=20),
            self.favorites_manager.create_favorites_quick_bar(),
        ], spacing=15)

        dialog = ft.AlertDialog(
            title=ft.Row([
                ft.Icon(ft.icons.STAR, color=ft.colors.AMBER),
                ft.Text("Favorites Manager"),
            ]),
            content=ft.Container(
                content=favorites_content,
                width=500,
                height=400,
            ),
            actions=[
                ft.TextButton("Close", on_click=lambda e: close_dialog()),
            ],
        )

        self.page.dialog = dialog
        dialog.open = True
        self.page.update()

    def show_quick_actions_menu(self, e=None):
        """Show quick actions menu."""
        if not self.quick_actions:
            return

        def close_dialog():
            dialog.open = False
            self.page.update()

        # Create quick actions content
        quick_actions_content = ft.Column([
            self.quick_actions.create_quick_actions_menu(),
            ft.Container(height=10),
            self.quick_actions.create_quick_actions_grid(),
        ], spacing=15)

        dialog = ft.AlertDialog(
            title=ft.Row([
                ft.Icon(ft.icons.FLASH_ON, color=ft.colors.AMBER),
                ft.Text("Quick Actions"),
            ]),
            content=ft.Container(
                content=quick_actions_content,
                width=600,
                height=500,
            ),
            actions=[
                ft.TextButton("Close", on_click=lambda e: close_dialog()),
            ],
        )

        self.page.dialog = dialog
        dialog.open = True
        self.page.update()

    def handle_keyboard_event(self, e: ft.KeyboardEvent):
        """Enhanced keyboard event handler."""
        # Handle global search shortcut
        if e.key == "F" and e.ctrl:
            self.show_global_search()
            return

        # Handle quick actions shortcut
        if e.key == "Q" and e.ctrl:
            self.show_quick_actions_menu()
            return

        # Handle favorites shortcut
        if e.key == "B" and e.ctrl:  # B for bookmarks/favorites
            self.show_favorites_panel()
            return

        # Handle existing shortcuts
        key_combination = ""
        if e.ctrl:
            key_combination += "ctrl+"
        if e.shift:
            key_combination += "shift+"
        if e.alt:
            key_combination += "alt+"
        key_combination += e.key.lower()

        if key_combination in self.active_shortcuts:
            self.active_shortcuts[key_combination](e)

    def update_user_context(self, user):
        """Update user context for enhanced navigation components."""
        self.current_user = user

        # Start analytics session
        if self.nav_analytics and user:
            device_type = "desktop"  # Could be detected from user agent
            self.nav_analytics.start_session(
                user_id=user.get("id", "unknown"),
                session_id=self.session_id,
                device_type=device_type
            )

        # Update favorites manager
        if self.favorites_manager:
            self.favorites_manager.user_id = user.get("id", "default") if user else "default"

        # Update quick actions
        if self.quick_actions:
            self.quick_actions.update_user_context(user)

        # Update responsive navigation
        if self.responsive_nav:
            self.responsive_nav.set_user(user)

    def logout(self):
        """Enhanced logout with analytics tracking."""
        self.logger.info("User logged out")

        # End analytics session
        if self.nav_analytics:
            self.nav_analytics.end_session(self.session_id)

        # Clear user context
        self.current_user = None
        self.update_user_context(None)

        # Perform logout
        self.auth_manager.logout()
        self.navigate_to("login")

    def show_login_page(self):
        """Show the login page."""
        # Create login form
        username_field = ft.TextField(
            label="Username",
            autofocus=True,
            prefix_icon=ft.Icons.PERSON,
            width=300,
        )

        password_field = ft.TextField(
            label="Password",
            password=True,
            prefix_icon=ft.Icons.LOCK,
            width=300,
        )

        error_text = ft.Text(
            "",
            color=ft.Colors.RED,
            visible=False,
        )

        def login_clicked(e):
            if not username_field.value:
                username_field.error_text = "Username cannot be empty"
                self.page.update()
                return

            if not password_field.value:
                password_field.error_text = "Password cannot be empty"
                self.page.update()
                return

            # Clear error texts
            username_field.error_text = None
            password_field.error_text = None

            # Attempt to login
            if self.auth_manager.login(username_field.value, password_field.value):
                # Login successful - update user context
                user = self.auth_manager.get_current_user()
                self.update_user_context(user)
                self.navigate_to("dashboard")
            else:
                # Login failed
                error_text.value = "Invalid username or password"
                error_text.visible = True
                self.page.update()

        login_button = ft.ElevatedButton(
            "Login",
            on_click=login_clicked,
            width=300,
            icon=ft.Icons.LOGIN,
        )

        register_button = ft.TextButton(
            "Register",
            on_click=lambda e: self.navigate_to("register"),
        )

        forgot_password_button = ft.TextButton(
            "Forgot Password?",
            on_click=lambda e: self.navigate_to("forgot_password"),
        )

        # Create login form
        login_form = ft.Column(
            [
                ft.Text("Login", size=24, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                error_text,
                username_field,
                password_field,
                ft.Container(height=20),
                login_button,
                ft.Row(
                    [
                        register_button,
                        forgot_password_button,
                    ],
                    alignment=ft.MainAxisAlignment.CENTER,
                ),
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=10,
            width=400,
        )

        # Create login card
        login_card = ft.Card(
            content=ft.Container(
                content=login_form,
                padding=20,
            ),
            elevation=5,
        )

        # Set content area
        self.content_area.content = ft.Column(
            [
                ft.Container(height=50),
                ft.Text("Science Laboratory Management System", size=30, weight=ft.FontWeight.BOLD),
                ft.Container(height=20),
                login_card,
                ft.Container(
                    content=ft.Text(
                        "Default login: admin / admin123",
                        size=12,
                        color=ft.Colors.GREY,
                    ),
                    margin=ft.margin.only(top=20),
                ),
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            alignment=ft.MainAxisAlignment.START,
        )

    def show_dashboard_page(self):
        """Show the dashboard page with enhanced functionality."""
        # Get current user
        user = self.auth_manager.get_current_user()

        # Get recent activities
        activities = self.db_manager.get_recent_activities(5)

        # Create activity rows
        activity_rows = []
        for activity in activities:
            activity_rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(activity['timestamp'])),
                        ft.DataCell(ft.Text(activity['username'] or "System")),
                        ft.DataCell(ft.Text(activity['action'])),
                    ],
                )
            )

        # Create welcome section with time-based greeting and date
        from datetime import datetime
        current_time = datetime.now()
        hour = current_time.hour

        if hour < 12:
            greeting = "Good morning"
        elif hour < 18:
            greeting = "Good afternoon"
        else:
            greeting = "Good evening"

        date_str = current_time.strftime("%A, %B %d, %Y")

        # Create quick action buttons
        quick_actions = ft.Row(
            [
                ft.ElevatedButton(
                    "Add Inventory",
                    icon=ft.icons.ADD_BOX,
                    on_click=lambda e: self.show_add_inventory_dialog(),
                    style=ft.ButtonStyle(
                        shape=ft.RoundedRectangleBorder(radius=8),
                    ),
                ),
                ft.ElevatedButton(
                    "New Experiment",
                    icon=ft.icons.SCIENCE,
                    on_click=lambda e: self.show_add_experiment_dialog(),
                    style=ft.ButtonStyle(
                        shape=ft.RoundedRectangleBorder(radius=8),
                    ),
                ),
                ft.ElevatedButton(
                    "Schedule Event",
                    icon=ft.icons.EVENT,
                    on_click=lambda e: self.navigate_to("scheduling"),
                    style=ft.ButtonStyle(
                        shape=ft.RoundedRectangleBorder(radius=8),
                    ),
                ),
                ft.ElevatedButton(
                    "Generate Report",
                    icon=ft.icons.SUMMARIZE,
                    on_click=lambda e: self.navigate_to("reports"),
                    style=ft.ButtonStyle(
                        shape=ft.RoundedRectangleBorder(radius=8),
                    ),
                ),
            ],
            alignment=ft.MainAxisAlignment.CENTER,
            spacing=10,
        )

        # Create system status indicators
        system_status = ft.Container(
            content=ft.Row(
                [
                    ft.Container(
                        content=ft.Row(
                            [
                                ft.Icon(
                                    name=ft.icons.CIRCLE,
                                    color=ft.colors.GREEN,
                                    size=12,
                                ),
                                ft.Text("Database: Online"),
                            ],
                            spacing=5,
                        ),
                        padding=5,
                    ),
                    ft.Container(
                        content=ft.Row(
                            [
                                ft.Icon(
                                    name=ft.icons.CIRCLE,
                                    color=ft.colors.GREEN,
                                    size=12,
                                ),
                                ft.Text("Storage: 75% Free"),
                            ],
                            spacing=5,
                        ),
                        padding=5,
                    ),
                    ft.Container(
                        content=ft.Row(
                            [
                                ft.Icon(
                                    name=ft.icons.CIRCLE,
                                    color=ft.colors.GREEN,
                                    size=12,
                                ),
                                ft.Text("Last Backup: Today"),
                            ],
                            spacing=5,
                        ),
                        padding=5,
                    ),
                ],
                alignment=ft.MainAxisAlignment.END,
            ),
            padding=10,
            border_radius=8,
            bgcolor=ft.colors.BLUE_50,
        )

        # Create statistics cards with progress indicators
        inventory_card = ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Row(
                            [
                                ft.Icon(
                                    name=ft.icons.INVENTORY,
                                    color=ft.colors.BLUE,
                                    size=24,
                                ),
                                ft.Text("Inventory", size=20, weight=ft.FontWeight.BOLD),
                            ],
                            spacing=10,
                        ),
                        ft.Divider(height=0, thickness=1),
                        ft.Container(height=10),
                        ft.Row(
                            [
                                ft.Text("Total Items:", weight=ft.FontWeight.BOLD),
                                ft.Text("250"),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Row(
                            [
                                ft.Text("Low Stock:", weight=ft.FontWeight.BOLD),
                                ft.Text("15", color=ft.colors.RED),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Row(
                            [
                                ft.Text("Categories:", weight=ft.FontWeight.BOLD),
                                ft.Text("4"),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Container(height=10),
                        ft.Text("Stock Level", size=14),
                        ft.ProgressBar(value=0.85, bgcolor=ft.colors.BLUE_100, color=ft.colors.BLUE),
                        ft.Container(
                            content=ft.Text("85%", size=12),
                            alignment=ft.alignment.center,
                        ),
                    ],
                    spacing=5,
                ),
                padding=15,
                width=300,
            ),
            elevation=3,
        )

        experiments_card = ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Row(
                            [
                                ft.Icon(
                                    name=ft.icons.SCIENCE,
                                    color=ft.colors.PURPLE,
                                    size=24,
                                ),
                                ft.Text("Experiments", size=20, weight=ft.FontWeight.BOLD),
                            ],
                            spacing=10,
                        ),
                        ft.Divider(height=0, thickness=1),
                        ft.Container(height=10),
                        ft.Row(
                            [
                                ft.Text("Active:", weight=ft.FontWeight.BOLD),
                                ft.Text("12", color=ft.colors.GREEN),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Row(
                            [
                                ft.Text("Completed:", weight=ft.FontWeight.BOLD),
                                ft.Text("45"),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Row(
                            [
                                ft.Text("Planned:", weight=ft.FontWeight.BOLD),
                                ft.Text("8"),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Container(height=10),
                        ft.Text("Completion Rate", size=14),
                        ft.ProgressBar(value=0.72, bgcolor=ft.colors.PURPLE_100, color=ft.colors.PURPLE),
                        ft.Container(
                            content=ft.Text("72%", size=12),
                            alignment=ft.alignment.center,
                        ),
                    ],
                    spacing=5,
                ),
                padding=15,
                width=300,
            ),
            elevation=3,
        )

        users_card = ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Row(
                            [
                                ft.Icon(
                                    name=ft.icons.PEOPLE,
                                    color=ft.colors.AMBER,
                                    size=24,
                                ),
                                ft.Text("Users", size=20, weight=ft.FontWeight.BOLD),
                            ],
                            spacing=10,
                        ),
                        ft.Divider(height=0, thickness=1),
                        ft.Container(height=10),
                        ft.Row(
                            [
                                ft.Text("Total:", weight=ft.FontWeight.BOLD),
                                ft.Text("35"),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Row(
                            [
                                ft.Text("Active Today:", weight=ft.FontWeight.BOLD),
                                ft.Text("12", color=ft.colors.GREEN),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Row(
                            [
                                ft.Text("Admins:", weight=ft.FontWeight.BOLD),
                                ft.Text("3"),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Container(height=10),
                        ft.Text("User Activity", size=14),
                        ft.ProgressBar(value=0.34, bgcolor=ft.colors.AMBER_100, color=ft.colors.AMBER),
                        ft.Container(
                            content=ft.Text("34%", size=12),
                            alignment=ft.alignment.center,
                        ),
                    ],
                    spacing=5,
                ),
                padding=15,
                width=300,
            ),
            elevation=3,
        )

        # Create notifications section
        notifications = [
            {"icon": ft.icons.WARNING_AMBER, "color": ft.colors.AMBER, "message": "5 inventory items are low on stock", "time": "10 minutes ago"},
            {"icon": ft.icons.EVENT_AVAILABLE, "color": ft.colors.GREEN, "message": "Lab meeting scheduled for tomorrow", "time": "1 hour ago"},
            {"icon": ft.icons.SCIENCE, "color": ft.colors.BLUE, "message": "Experiment #EXP003 is due in 3 days", "time": "2 hours ago"},
        ]

        notification_items = []
        for notification in notifications:
            notification_items.append(
                ft.Container(
                    content=ft.Row(
                        [
                            ft.Icon(
                                name=notification["icon"],
                                color=notification["color"],
                                size=20,
                            ),
                            ft.Column(
                                [
                                    ft.Text(notification["message"]),
                                    ft.Text(notification["time"], size=12, color=ft.colors.GREY),
                                ],
                                spacing=2,
                                expand=True,
                            ),
                            ft.IconButton(
                                icon=ft.icons.CLOSE,
                                icon_size=16,
                                tooltip="Dismiss",
                            ),
                        ],
                        spacing=10,
                        alignment=ft.MainAxisAlignment.START,
                    ),
                    padding=10,
                    border_radius=8,
                    bgcolor=ft.colors.WHITE,
                    border=ft.border.all(1, ft.colors.GREY_300),
                    margin=ft.margin.only(bottom=5),
                )
            )

        notifications_section = ft.Container(
            content=ft.Column(
                [
                    ft.Row(
                        [
                            ft.Text("Notifications", size=20, weight=ft.FontWeight.BOLD),
                            ft.Container(
                                content=ft.Text(f"{len(notifications)}", color=ft.colors.WHITE, size=12),
                                bgcolor=ft.colors.RED,
                                padding=5,
                                border_radius=10,
                                width=25,
                                height=25,
                                alignment=ft.alignment.center,
                            ),
                            ft.Container(expand=True),
                            ft.TextButton("View All", on_click=lambda e: print("View all notifications")),
                        ],
                        alignment=ft.MainAxisAlignment.START,
                    ),
                    ft.Column(notification_items),
                ],
                spacing=10,
            ),
            padding=15,
            border_radius=8,
            bgcolor=ft.colors.BLUE_50,
        )

        # Create upcoming events section
        upcoming_events = [
            {"date": "May 25", "time": "10:00 AM", "title": "Lab Meeting", "location": "Conference Room"},
            {"date": "May 26", "time": "2:00 PM", "title": "Equipment Training", "location": "Lab 2"},
            {"date": "May 28", "time": "9:00 AM", "title": "Safety Inspection", "location": "All Labs"},
        ]

        event_items = []
        for event in upcoming_events:
            event_items.append(
                ft.Container(
                    content=ft.Row(
                        [
                            ft.Container(
                                content=ft.Column(
                                    [
                                        ft.Text(event["date"], size=14, weight=ft.FontWeight.BOLD),
                                        ft.Text(event["time"], size=12),
                                    ],
                                    spacing=2,
                                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                                ),
                                width=80,
                                height=50,
                                bgcolor=ft.colors.BLUE_100,
                                border_radius=8,
                                alignment=ft.alignment.center,
                            ),
                            ft.Column(
                                [
                                    ft.Text(event["title"], size=16, weight=ft.FontWeight.BOLD),
                                    ft.Text(event["location"], size=14),
                                ],
                                spacing=2,
                                expand=True,
                            ),
                            ft.IconButton(
                                icon=ft.icons.CALENDAR_TODAY,
                                tooltip="Add to Calendar",
                            ),
                        ],
                        spacing=10,
                        alignment=ft.MainAxisAlignment.START,
                    ),
                    padding=10,
                    border_radius=8,
                    bgcolor=ft.colors.WHITE,
                    border=ft.border.all(1, ft.colors.GREY_300),
                    margin=ft.margin.only(bottom=5),
                )
            )

        upcoming_events_section = ft.Container(
            content=ft.Column(
                [
                    ft.Row(
                        [
                            ft.Text("Upcoming Events", size=20, weight=ft.FontWeight.BOLD),
                            ft.Container(expand=True),
                            ft.TextButton("View Calendar", on_click=lambda e: self.navigate_to("scheduling")),
                        ],
                        alignment=ft.MainAxisAlignment.START,
                    ),
                    ft.Column(event_items),
                ],
                spacing=10,
            ),
            padding=15,
            border_radius=8,
            bgcolor=ft.colors.BLUE_50,
        )

        # Create recent activity section with improved styling
        activity_section = ft.Container(
            content=ft.Column(
                [
                    ft.Row(
                        [
                            ft.Text("Recent Activity", size=20, weight=ft.FontWeight.BOLD),
                            ft.Container(expand=True),
                            ft.TextButton("View All", on_click=lambda e: print("View all activities")),
                        ],
                        alignment=ft.MainAxisAlignment.START,
                    ),
                    ft.DataTable(
                        columns=[
                            ft.DataColumn(ft.Text("Date")),
                            ft.DataColumn(ft.Text("User")),
                            ft.DataColumn(ft.Text("Activity")),
                        ],
                        rows=activity_rows if activity_rows else [
                            ft.DataRow(
                                cells=[
                                    ft.DataCell(ft.Text("No recent activity")),
                                    ft.DataCell(ft.Text("")),
                                    ft.DataCell(ft.Text("")),
                                ],
                            )
                        ],
                        border_radius=8,
                        heading_row_height=40,
                        data_row_min_height=40,
                        horizontal_lines=ft.border.BorderSide(1, ft.colors.GREY_300),
                        vertical_lines=ft.border.BorderSide(0, ft.colors.TRANSPARENT),
                    ),
                ],
                spacing=10,
            ),
            padding=15,
            border_radius=8,
            bgcolor=ft.colors.BLUE_50,
        )

        # Create dashboard content with all sections
        dashboard_content = ft.Column(
            [
                # Header section with greeting and system status
                ft.Container(
                    content=ft.Row(
                        [
                            ft.Column(
                                [
                                    ft.Text(f"{greeting}, {user['full_name']}", size=24, weight=ft.FontWeight.BOLD),
                                    ft.Text(date_str, size=16, color=ft.colors.GREY),
                                ],
                                spacing=5,
                            ),
                            ft.Container(expand=True),
                            system_status,
                        ],
                    ),
                    padding=ft.padding.only(bottom=10),
                ),

                # Quick actions section
                ft.Container(
                    content=quick_actions,
                    padding=15,
                    border_radius=8,
                    bgcolor=ft.colors.BLUE_50,
                    margin=ft.margin.only(bottom=20),
                ),

                # Statistics cards
                ft.Row(
                    [
                        inventory_card,
                        experiments_card,
                        users_card,
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                    spacing=20,
                ),

                ft.Container(height=20),

                # Two-column layout for notifications and upcoming events
                ft.Row(
                    [
                        ft.Column(
                            [
                                notifications_section,
                            ],
                            expand=True,
                        ),
                        ft.Column(
                            [
                                upcoming_events_section,
                            ],
                            expand=True,
                        ),
                    ],
                    spacing=20,
                ),

                ft.Container(height=20),

                # Recent activity section
                activity_section,
            ],
            spacing=10,
            scroll=ft.ScrollMode.AUTO,
        )

        # Set content area
        self.content_area.content = dashboard_content

        # Update navigation rail
        self.nav_rail.selected_index = 0

    def show_inventory_page(self):
        """Show the inventory page."""
        # Create inventory content

        # Create search field
        search_field = ft.TextField(
            label="Search Inventory",
            prefix_icon=ft.Icons.SEARCH,
            expand=True,
        )

        # Create filter dropdown
        filter_dropdown = ft.Dropdown(
            label="Filter By",
            options=[
                ft.dropdown.Option("All"),
                ft.dropdown.Option("Chemicals"),
                ft.dropdown.Option("Equipment"),
                ft.dropdown.Option("Glassware"),
                ft.dropdown.Option("Consumables"),
            ],
            value="All",
            width=200,
        )

        # Create add button
        add_button = ft.ElevatedButton(
            "Add Item",
            icon=ft.Icons.ADD,
            on_click=lambda e: self.show_add_inventory_dialog(),
        )

        # Create inventory table
        inventory_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("ID")),
                ft.DataColumn(ft.Text("Name")),
                ft.DataColumn(ft.Text("Category")),
                ft.DataColumn(ft.Text("Quantity")),
                ft.DataColumn(ft.Text("Location")),
                ft.DataColumn(ft.Text("Status")),
                ft.DataColumn(ft.Text("Actions")),
            ],
            rows=[
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("INV001")),
                        ft.DataCell(ft.Text("Microscope")),
                        ft.DataCell(ft.Text("Equipment")),
                        ft.DataCell(ft.Text("5")),
                        ft.DataCell(ft.Text("Lab 1")),
                        ft.DataCell(ft.Text("Available")),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.Icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.DELETE,
                                        tooltip="Delete",
                                        icon_color=ft.Colors.RED,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("INV002")),
                        ft.DataCell(ft.Text("Test Tubes")),
                        ft.DataCell(ft.Text("Glassware")),
                        ft.DataCell(ft.Text("100")),
                        ft.DataCell(ft.Text("Storage Room")),
                        ft.DataCell(ft.Text("Low Stock")),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.Icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.DELETE,
                                        tooltip="Delete",
                                        icon_color=ft.Colors.RED,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("INV003")),
                        ft.DataCell(ft.Text("Bunsen Burner")),
                        ft.DataCell(ft.Text("Equipment")),
                        ft.DataCell(ft.Text("10")),
                        ft.DataCell(ft.Text("Lab 2")),
                        ft.DataCell(ft.Text("Available")),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.Icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.DELETE,
                                        tooltip="Delete",
                                        icon_color=ft.Colors.RED,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("INV004")),
                        ft.DataCell(ft.Text("Sodium Chloride")),
                        ft.DataCell(ft.Text("Chemicals")),
                        ft.DataCell(ft.Text("500g")),
                        ft.DataCell(ft.Text("Chemical Storage")),
                        ft.DataCell(ft.Text("Available")),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.Icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.DELETE,
                                        tooltip="Delete",
                                        icon_color=ft.Colors.RED,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
            ],
        )

        # Create pagination controls
        pagination = ft.Row(
            [
                ft.Text("Page 1 of 5"),
                ft.IconButton(
                    icon=ft.Icons.ARROW_BACK,
                    tooltip="Previous Page",
                ),
                ft.IconButton(
                    icon=ft.Icons.ARROW_FORWARD,
                    tooltip="Next Page",
                ),
            ],
            alignment=ft.MainAxisAlignment.CENTER,
        )

        # Create inventory content
        inventory_content = ft.Column(
            [
                ft.Text("Inventory Management", size=24, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                ft.Row(
                    [
                        search_field,
                        filter_dropdown,
                        add_button,
                    ],
                    spacing=10,
                ),
                ft.Container(height=20),
                inventory_table,
                ft.Container(height=20),
                pagination,
            ],
            spacing=10,
            scroll=ft.ScrollMode.AUTO,
        )

        # Set content area
        self.content_area.content = inventory_content

        # Update navigation rail
        self.nav_rail.selected_index = 1

    def show_add_inventory_dialog(self):
        """Show the add inventory dialog."""
        # Create form fields
        name_field = ft.TextField(
            label="Item Name",
            autofocus=True,
            width=400,
        )

        category_dropdown = ft.Dropdown(
            label="Category",
            options=[
                ft.dropdown.Option("Chemicals"),
                ft.dropdown.Option("Equipment"),
                ft.dropdown.Option("Glassware"),
                ft.dropdown.Option("Consumables"),
            ],
            width=400,
        )

        quantity_field = ft.TextField(
            label="Quantity",
            width=400,
        )

        location_field = ft.TextField(
            label="Location",
            width=400,
        )

        description_field = ft.TextField(
            label="Description",
            multiline=True,
            min_lines=3,
            max_lines=5,
            width=400,
        )

        # Create dialog
        dialog = ft.AlertDialog(
            title=ft.Text("Add Inventory Item"),
            content=ft.Column(
                [
                    name_field,
                    category_dropdown,
                    quantity_field,
                    location_field,
                    description_field,
                ],
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                height=400,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=lambda _: setattr(dialog, "open", False)),
                ft.ElevatedButton(
                    "Add",
                    on_click=lambda _: setattr(dialog, "open", False),
                ),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )

        # Show the dialog using the new API
        if hasattr(self.page, 'overlay') and self.page.overlay is not None:
            self.page.overlay.append(dialog)
            dialog.open = True
            self.page.update()
        else:
            # Fallback to the old API
            self.page.dialog = dialog
            dialog.open = True
            self.page.update()

    def show_experiments_page(self):
        """Show the experiments page."""
        # Create experiments content

        # Create search field
        search_field = ft.TextField(
            label="Search Experiments",
            prefix_icon=ft.Icons.SEARCH,
            expand=True,
        )

        # Create filter dropdown
        filter_dropdown = ft.Dropdown(
            label="Filter By",
            options=[
                ft.dropdown.Option("All"),
                ft.dropdown.Option("Active"),
                ft.dropdown.Option("Completed"),
                ft.dropdown.Option("Planned"),
            ],
            value="All",
            width=200,
        )

        # Create add button
        add_button = ft.ElevatedButton(
            "New Experiment",
            icon=ft.Icons.ADD,
            on_click=lambda e: self.show_add_experiment_dialog(),
        )

        # Create experiments table
        experiments_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("ID")),
                ft.DataColumn(ft.Text("Title")),
                ft.DataColumn(ft.Text("Status")),
                ft.DataColumn(ft.Text("Start Date")),
                ft.DataColumn(ft.Text("End Date")),
                ft.DataColumn(ft.Text("Researcher")),
                ft.DataColumn(ft.Text("Actions")),
            ],
            rows=[
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("EXP001")),
                        ft.DataCell(ft.Text("Water Quality Analysis")),
                        ft.DataCell(ft.Text("Active")),
                        ft.DataCell(ft.Text("2025-05-01")),
                        ft.DataCell(ft.Text("2025-06-01")),
                        ft.DataCell(ft.Text("John Doe")),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.Icons.VISIBILITY,
                                        tooltip="View",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.DELETE,
                                        tooltip="Delete",
                                        icon_color=ft.Colors.RED,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("EXP002")),
                        ft.DataCell(ft.Text("Bacterial Growth Study")),
                        ft.DataCell(ft.Text("Completed")),
                        ft.DataCell(ft.Text("2025-04-15")),
                        ft.DataCell(ft.Text("2025-05-15")),
                        ft.DataCell(ft.Text("Jane Smith")),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.Icons.VISIBILITY,
                                        tooltip="View",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.DELETE,
                                        tooltip="Delete",
                                        icon_color=ft.Colors.RED,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("EXP003")),
                        ft.DataCell(ft.Text("Chemical Reaction Rates")),
                        ft.DataCell(ft.Text("Planned")),
                        ft.DataCell(ft.Text("2025-06-01")),
                        ft.DataCell(ft.Text("2025-07-01")),
                        ft.DataCell(ft.Text("Bob Johnson")),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.Icons.VISIBILITY,
                                        tooltip="View",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.DELETE,
                                        tooltip="Delete",
                                        icon_color=ft.Colors.RED,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
            ],
        )

        # Create pagination controls
        pagination = ft.Row(
            [
                ft.Text("Page 1 of 3"),
                ft.IconButton(
                    icon=ft.Icons.ARROW_BACK,
                    tooltip="Previous Page",
                ),
                ft.IconButton(
                    icon=ft.Icons.ARROW_FORWARD,
                    tooltip="Next Page",
                ),
            ],
            alignment=ft.MainAxisAlignment.CENTER,
        )

        # Create experiments content
        experiments_content = ft.Column(
            [
                ft.Text("Experiment Management", size=24, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                ft.Row(
                    [
                        search_field,
                        filter_dropdown,
                        add_button,
                    ],
                    spacing=10,
                ),
                ft.Container(height=20),
                experiments_table,
                ft.Container(height=20),
                pagination,
            ],
            spacing=10,
            scroll=ft.ScrollMode.AUTO,
        )

        # Set content area
        self.content_area.content = experiments_content

        # Update navigation rail
        self.nav_rail.selected_index = 2

    def show_add_experiment_dialog(self):
        """Show the add experiment dialog."""
        # Create form fields
        title_field = ft.TextField(
            label="Experiment Title",
            autofocus=True,
            width=400,
        )

        status_dropdown = ft.Dropdown(
            label="Status",
            options=[
                ft.dropdown.Option("Planned"),
                ft.dropdown.Option("Active"),
                ft.dropdown.Option("Completed"),
            ],
            width=400,
        )

        start_date_field = ft.TextField(
            label="Start Date (YYYY-MM-DD)",
            width=400,
        )

        end_date_field = ft.TextField(
            label="End Date (YYYY-MM-DD)",
            width=400,
        )

        researcher_field = ft.TextField(
            label="Researcher",
            width=400,
        )

        description_field = ft.TextField(
            label="Description",
            multiline=True,
            min_lines=3,
            max_lines=5,
            width=400,
        )

        # Create dialog
        dialog = ft.AlertDialog(
            title=ft.Text("Add New Experiment"),
            content=ft.Column(
                [
                    title_field,
                    status_dropdown,
                    start_date_field,
                    end_date_field,
                    researcher_field,
                    description_field,
                ],
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                height=400,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=lambda _: setattr(dialog, "open", False)),
                ft.ElevatedButton(
                    "Add",
                    on_click=lambda _: setattr(dialog, "open", False),
                ),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )

        # Show the dialog using the new API
        if hasattr(self.page, 'overlay') and self.page.overlay is not None:
            self.page.overlay.append(dialog)
            dialog.open = True
            self.page.update()
        else:
            # Fallback to the old API
            self.page.dialog = dialog
            dialog.open = True
            self.page.update()

    def show_scheduling_page(self):
        """Show the scheduling page."""
        # Create scheduling content
        scheduling_content = ft.Column(
            [
                ft.Text("Scheduling", size=24, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                ft.Text("Calendar view will be implemented here."),
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Text("Upcoming Events", size=20, weight=ft.FontWeight.BOLD),
                            ft.DataTable(
                                columns=[
                                    ft.DataColumn(ft.Text("Date")),
                                    ft.DataColumn(ft.Text("Time")),
                                    ft.DataColumn(ft.Text("Event")),
                                    ft.DataColumn(ft.Text("Location")),
                                    ft.DataColumn(ft.Text("Participants")),
                                ],
                                rows=[
                                    ft.DataRow(
                                        cells=[
                                            ft.DataCell(ft.Text("2025-05-25")),
                                            ft.DataCell(ft.Text("10:00 AM")),
                                            ft.DataCell(ft.Text("Lab Meeting")),
                                            ft.DataCell(ft.Text("Conference Room")),
                                            ft.DataCell(ft.Text("All Staff")),
                                        ],
                                    ),
                                    ft.DataRow(
                                        cells=[
                                            ft.DataCell(ft.Text("2025-05-26")),
                                            ft.DataCell(ft.Text("2:00 PM")),
                                            ft.DataCell(ft.Text("Equipment Training")),
                                            ft.DataCell(ft.Text("Lab 2")),
                                            ft.DataCell(ft.Text("New Staff")),
                                        ],
                                    ),
                                    ft.DataRow(
                                        cells=[
                                            ft.DataCell(ft.Text("2025-05-28")),
                                            ft.DataCell(ft.Text("9:00 AM")),
                                            ft.DataCell(ft.Text("Safety Inspection")),
                                            ft.DataCell(ft.Text("All Labs")),
                                            ft.DataCell(ft.Text("Safety Officer")),
                                        ],
                                    ),
                                ],
                            ),
                        ],
                        spacing=10,
                    ),
                    padding=20,
                ),
            ],
            spacing=20,
            scroll=ft.ScrollMode.AUTO,
        )

        # Set content area
        self.content_area.content = scheduling_content

        # Update navigation rail
        self.nav_rail.selected_index = 3

    def show_reports_page(self):
        """Show the reports page."""
        # Create reports content
        reports_content = ft.Column(
            [
                ft.Text("Reports", size=24, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                ft.Row(
                    [
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column(
                                    [
                                        ft.Text("Inventory Report", size=18, weight=ft.FontWeight.BOLD),
                                        ft.Text("Summary of inventory items and status"),
                                        ft.ElevatedButton(
                                            "Generate",
                                            icon=ft.Icons.DOWNLOAD,
                                        ),
                                    ],
                                    spacing=10,
                                ),
                                padding=20,
                                width=250,
                            ),
                            elevation=5,
                        ),
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column(
                                    [
                                        ft.Text("Experiment Report", size=18, weight=ft.FontWeight.BOLD),
                                        ft.Text("Summary of experiments and results"),
                                        ft.ElevatedButton(
                                            "Generate",
                                            icon=ft.Icons.DOWNLOAD,
                                        ),
                                    ],
                                    spacing=10,
                                ),
                                padding=20,
                                width=250,
                            ),
                            elevation=5,
                        ),
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column(
                                    [
                                        ft.Text("Usage Report", size=18, weight=ft.FontWeight.BOLD),
                                        ft.Text("Summary of equipment and material usage"),
                                        ft.ElevatedButton(
                                            "Generate",
                                            icon=ft.Icons.DOWNLOAD,
                                        ),
                                    ],
                                    spacing=10,
                                ),
                                padding=20,
                                width=250,
                            ),
                            elevation=5,
                        ),
                    ],
                    spacing=20,
                    alignment=ft.MainAxisAlignment.CENTER,
                ),
                ft.Container(height=20),
                ft.Text("Recent Reports", size=20, weight=ft.FontWeight.BOLD),
                ft.DataTable(
                    columns=[
                        ft.DataColumn(ft.Text("Date")),
                        ft.DataColumn(ft.Text("Report Type")),
                        ft.DataColumn(ft.Text("Generated By")),
                        ft.DataColumn(ft.Text("Actions")),
                    ],
                    rows=[
                        ft.DataRow(
                            cells=[
                                ft.DataCell(ft.Text("2025-05-20")),
                                ft.DataCell(ft.Text("Inventory Report")),
                                ft.DataCell(ft.Text("John Doe")),
                                ft.DataCell(
                                    ft.Row(
                                        [
                                            ft.IconButton(
                                                icon=ft.Icons.VISIBILITY,
                                                tooltip="View",
                                                icon_color=ft.Colors.BLUE,
                                            ),
                                            ft.IconButton(
                                                icon=ft.Icons.DOWNLOAD,
                                                tooltip="Download",
                                                icon_color=ft.Colors.GREEN,
                                            ),
                                        ]
                                    )
                                ),
                            ],
                        ),
                        ft.DataRow(
                            cells=[
                                ft.DataCell(ft.Text("2025-05-15")),
                                ft.DataCell(ft.Text("Experiment Report")),
                                ft.DataCell(ft.Text("Jane Smith")),
                                ft.DataCell(
                                    ft.Row(
                                        [
                                            ft.IconButton(
                                                icon=ft.Icons.VISIBILITY,
                                                tooltip="View",
                                                icon_color=ft.Colors.BLUE,
                                            ),
                                            ft.IconButton(
                                                icon=ft.Icons.DOWNLOAD,
                                                tooltip="Download",
                                                icon_color=ft.Colors.GREEN,
                                            ),
                                        ]
                                    )
                                ),
                            ],
                        ),
                        ft.DataRow(
                            cells=[
                                ft.DataCell(ft.Text("2025-05-10")),
                                ft.DataCell(ft.Text("Usage Report")),
                                ft.DataCell(ft.Text("Bob Johnson")),
                                ft.DataCell(
                                    ft.Row(
                                        [
                                            ft.IconButton(
                                                icon=ft.Icons.VISIBILITY,
                                                tooltip="View",
                                                icon_color=ft.Colors.BLUE,
                                            ),
                                            ft.IconButton(
                                                icon=ft.Icons.DOWNLOAD,
                                                tooltip="Download",
                                                icon_color=ft.Colors.GREEN,
                                            ),
                                        ]
                                    )
                                ),
                            ],
                        ),
                    ],
                ),
            ],
            spacing=20,
            scroll=ft.ScrollMode.AUTO,
        )

        # Set content area
        self.content_area.content = reports_content

        # Update navigation rail
        self.nav_rail.selected_index = 4

    def show_profile_page(self):
        """Show the profile page."""
        # Get current user
        user = self.auth_manager.get_current_user()

        # Create profile content
        profile_content = ft.Column(
            [
                ft.Text("User Profile", size=24, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Row(
                                [
                                    ft.Container(
                                        content=ft.Icon(
                                            ft.Icons.ACCOUNT_CIRCLE,
                                            size=100,
                                            color=ft.Colors.BLUE,
                                        ),
                                        width=150,
                                        alignment=ft.alignment.center,
                                    ),
                                    ft.Column(
                                        [
                                            ft.Text(user['full_name'], size=24, weight=ft.FontWeight.BOLD),
                                            ft.Text(f"Username: {user['username']}", size=16),
                                            ft.Text(f"Email: {user['email']}", size=16),
                                            ft.Text(f"Role: {user['role'].capitalize()}", size=16),
                                        ],
                                        spacing=10,
                                    ),
                                ],
                                alignment=ft.MainAxisAlignment.START,
                            ),
                            ft.Divider(),
                            ft.Text("Account Settings", size=20, weight=ft.FontWeight.BOLD),
                            ft.Container(height=10),
                            ft.ElevatedButton(
                                "Change Password",
                                icon=ft.Icons.LOCK,
                                on_click=lambda e: self.show_change_password_dialog(),
                            ),
                            ft.Container(height=10),
                            ft.ElevatedButton(
                                "Update Profile",
                                icon=ft.Icons.EDIT,
                                on_click=lambda _: self.show_update_profile_dialog(),
                            ),
                        ],
                        spacing=10,
                    ),
                    padding=20,
                    border=ft.border.all(1, ft.Colors.GREY_300),
                    border_radius=10,
                ),
            ],
            spacing=20,
            scroll=ft.ScrollMode.AUTO,
        )

        # Set content area
        self.content_area.content = profile_content

    def show_change_password_dialog(self):
        """Show the change password dialog."""
        # Create form fields
        current_password_field = ft.TextField(
            label="Current Password",
            password=True,
            autofocus=True,
            width=400,
        )

        new_password_field = ft.TextField(
            label="New Password",
            password=True,
            width=400,
        )

        confirm_password_field = ft.TextField(
            label="Confirm New Password",
            password=True,
            width=400,
        )

        # Create dialog
        dialog = ft.AlertDialog(
            title=ft.Text("Change Password"),
            content=ft.Column(
                [
                    current_password_field,
                    new_password_field,
                    confirm_password_field,
                ],
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                height=200,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=lambda _: setattr(dialog, "open", False)),
                ft.ElevatedButton(
                    "Change",
                    on_click=lambda _: setattr(dialog, "open", False),
                ),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )

        # Show the dialog using the new API
        if hasattr(self.page, 'overlay') and self.page.overlay is not None:
            self.page.overlay.append(dialog)
            dialog.open = True
            self.page.update()
        else:
            # Fallback to the old API
            self.page.dialog = dialog
            dialog.open = True
            self.page.update()

    def show_update_profile_dialog(self):
        """Show the update profile dialog."""
        # Get current user
        user = self.auth_manager.get_current_user()

        # Create form fields
        full_name_field = ft.TextField(
            label="Full Name",
            value=user['full_name'],
            autofocus=True,
            width=400,
        )

        email_field = ft.TextField(
            label="Email",
            value=user['email'],
            width=400,
        )

        # Create dialog
        dialog = ft.AlertDialog(
            title=ft.Text("Update Profile"),
            content=ft.Column(
                [
                    full_name_field,
                    email_field,
                ],
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                height=150,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=lambda _: setattr(dialog, "open", False)),
                ft.ElevatedButton(
                    "Update",
                    on_click=lambda _: setattr(dialog, "open", False),
                ),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )

        # Show the dialog using the new API
        if hasattr(self.page, 'overlay') and self.page.overlay is not None:
            self.page.overlay.append(dialog)
            dialog.open = True
            self.page.update()
        else:
            # Fallback to the old API
            self.page.dialog = dialog
            dialog.open = True
            self.page.update()

    def show_settings_page(self):
        """Show the settings page."""
        # Create settings content
        settings_content = ft.Column(
            [
                ft.Text("Settings", size=24, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Text("Application Settings", size=20, weight=ft.FontWeight.BOLD),
                            ft.Container(height=10),
                            ft.Row(
                                [
                                    ft.Text("Theme Mode", size=16),
                                    ft.Container(expand=True),
                                    ft.Switch(
                                        value=self.page.theme_mode == ft.ThemeMode.DARK,
                                        label="Dark Mode",
                                        on_change=self.toggle_theme_mode,
                                    ),
                                ],
                            ),
                            ft.Row(
                                [
                                    ft.Text("Notifications", size=16),
                                    ft.Container(expand=True),
                                    ft.Switch(
                                        value=True,
                                        label="Enable Notifications",
                                    ),
                                ],
                            ),
                            ft.Row(
                                [
                                    ft.Text("Auto Logout", size=16),
                                    ft.Container(expand=True),
                                    ft.Switch(
                                        value=False,
                                        label="Enable Auto Logout",
                                    ),
                                ],
                            ),
                        ],
                        spacing=10,
                    ),
                    padding=20,
                    border=ft.border.all(1, ft.Colors.GREY_300),
                    border_radius=10,
                ),
            ],
            spacing=20,
            scroll=ft.ScrollMode.AUTO,
        )

        # Set content area
        self.content_area.content = settings_content

    def show_users_page(self):
        """Show the users management page."""
        # Create search field
        search_field = ft.TextField(
            label="Search Users",
            prefix_icon=ft.icons.SEARCH,
            expand=True,
        )

        # Create filter dropdown
        filter_dropdown = ft.Dropdown(
            label="Filter By",
            options=[
                ft.dropdown.Option("All"),
                ft.dropdown.Option("Admin"),
                ft.dropdown.Option("Staff"),
                ft.dropdown.Option("User"),
            ],
            value="All",
            width=200,
        )

        # Create add button
        add_button = ft.ElevatedButton(
            "Add User",
            icon=ft.icons.PERSON_ADD,
            on_click=lambda e: self.show_add_user_dialog(),
        )

        # Create users table
        users_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("ID")),
                ft.DataColumn(ft.Text("Username")),
                ft.DataColumn(ft.Text("Full Name")),
                ft.DataColumn(ft.Text("Email")),
                ft.DataColumn(ft.Text("Role")),
                ft.DataColumn(ft.Text("Last Login")),
                ft.DataColumn(ft.Text("Status")),
                ft.DataColumn(ft.Text("Actions")),
            ],
            rows=[
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("1")),
                        ft.DataCell(ft.Text("admin")),
                        ft.DataCell(ft.Text("Administrator")),
                        ft.DataCell(ft.Text("<EMAIL>")),
                        ft.DataCell(ft.Text("Admin")),
                        ft.DataCell(ft.Text("Today")),
                        ft.DataCell(
                            ft.Container(
                                content=ft.Text("Active", color=ft.colors.WHITE),
                                bgcolor=ft.colors.GREEN,
                                border_radius=5,
                                padding=5,
                                width=80,
                                alignment=ft.alignment.center,
                            )
                        ),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.BLOCK,
                                        tooltip="Disable",
                                        icon_color=ft.colors.RED,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("2")),
                        ft.DataCell(ft.Text("john")),
                        ft.DataCell(ft.Text("John Doe")),
                        ft.DataCell(ft.Text("<EMAIL>")),
                        ft.DataCell(ft.Text("Staff")),
                        ft.DataCell(ft.Text("Yesterday")),
                        ft.DataCell(
                            ft.Container(
                                content=ft.Text("Active", color=ft.colors.WHITE),
                                bgcolor=ft.colors.GREEN,
                                border_radius=5,
                                padding=5,
                                width=80,
                                alignment=ft.alignment.center,
                            )
                        ),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.BLOCK,
                                        tooltip="Disable",
                                        icon_color=ft.colors.RED,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("3")),
                        ft.DataCell(ft.Text("jane")),
                        ft.DataCell(ft.Text("Jane Smith")),
                        ft.DataCell(ft.Text("<EMAIL>")),
                        ft.DataCell(ft.Text("Staff")),
                        ft.DataCell(ft.Text("2 days ago")),
                        ft.DataCell(
                            ft.Container(
                                content=ft.Text("Active", color=ft.colors.WHITE),
                                bgcolor=ft.colors.GREEN,
                                border_radius=5,
                                padding=5,
                                width=80,
                                alignment=ft.alignment.center,
                            )
                        ),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.BLOCK,
                                        tooltip="Disable",
                                        icon_color=ft.colors.RED,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("4")),
                        ft.DataCell(ft.Text("robert")),
                        ft.DataCell(ft.Text("Robert Johnson")),
                        ft.DataCell(ft.Text("<EMAIL>")),
                        ft.DataCell(ft.Text("User")),
                        ft.DataCell(ft.Text("1 week ago")),
                        ft.DataCell(
                            ft.Container(
                                content=ft.Text("Inactive", color=ft.colors.WHITE),
                                bgcolor=ft.colors.GREY,
                                border_radius=5,
                                padding=5,
                                width=80,
                                alignment=ft.alignment.center,
                            )
                        ),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.CHECK_CIRCLE,
                                        tooltip="Enable",
                                        icon_color=ft.colors.GREEN,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
            ],
            border_radius=8,
            heading_row_height=50,
            data_row_min_height=60,
            horizontal_lines=ft.border.BorderSide(1, ft.colors.GREY_300),
            vertical_lines=ft.border.BorderSide(0, ft.colors.TRANSPARENT),
        )

        # Create pagination controls
        pagination = ft.Row(
            [
                ft.Text("Page 1 of 1"),
                ft.IconButton(
                    icon=ft.icons.ARROW_BACK,
                    tooltip="Previous Page",
                    disabled=True,
                ),
                ft.IconButton(
                    icon=ft.icons.ARROW_FORWARD,
                    tooltip="Next Page",
                    disabled=True,
                ),
            ],
            alignment=ft.MainAxisAlignment.CENTER,
        )

        # Create user statistics
        user_stats = ft.Row(
            [
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Text("Total Users", size=14, color=ft.colors.GREY),
                            ft.Text("35", size=24, weight=ft.FontWeight.BOLD),
                        ],
                        spacing=5,
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    ),
                    padding=15,
                    border_radius=8,
                    bgcolor=ft.colors.BLUE_50,
                    width=150,
                ),
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Text("Active Users", size=14, color=ft.colors.GREY),
                            ft.Text("28", size=24, weight=ft.FontWeight.BOLD, color=ft.colors.GREEN),
                        ],
                        spacing=5,
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    ),
                    padding=15,
                    border_radius=8,
                    bgcolor=ft.colors.BLUE_50,
                    width=150,
                ),
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Text("Admins", size=14, color=ft.colors.GREY),
                            ft.Text("3", size=24, weight=ft.FontWeight.BOLD, color=ft.colors.AMBER),
                        ],
                        spacing=5,
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    ),
                    padding=15,
                    border_radius=8,
                    bgcolor=ft.colors.BLUE_50,
                    width=150,
                ),
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Text("New This Month", size=14, color=ft.colors.GREY),
                            ft.Text("7", size=24, weight=ft.FontWeight.BOLD, color=ft.colors.BLUE),
                        ],
                        spacing=5,
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    ),
                    padding=15,
                    border_radius=8,
                    bgcolor=ft.colors.BLUE_50,
                    width=150,
                ),
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            spacing=10,
        )

        # Create users content
        users_content = ft.Column(
            [
                ft.Text("User Management", size=24, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                user_stats,
                ft.Container(height=20),
                ft.Row(
                    [
                        search_field,
                        filter_dropdown,
                        add_button,
                    ],
                    spacing=10,
                ),
                ft.Container(height=20),
                users_table,
                ft.Container(height=20),
                pagination,
            ],
            spacing=10,
            scroll=ft.ScrollMode.AUTO,
        )

        # Set content area
        self.content_area.content = users_content

    def show_add_user_dialog(self):
        """Show the add user dialog."""
        # Create form fields
        username_field = ft.TextField(
            label="Username",
            autofocus=True,
            width=400,
        )

        password_field = ft.TextField(
            label="Password",
            password=True,
            width=400,
        )

        email_field = ft.TextField(
            label="Email",
            width=400,
        )

        full_name_field = ft.TextField(
            label="Full Name",
            width=400,
        )

        role_dropdown = ft.Dropdown(
            label="Role",
            options=[
                ft.dropdown.Option("Admin"),
                ft.dropdown.Option("Staff"),
                ft.dropdown.Option("User"),
            ],
            width=400,
        )

        # Create dialog
        dialog = ft.AlertDialog(
            title=ft.Text("Add New User"),
            content=ft.Column(
                [
                    username_field,
                    password_field,
                    email_field,
                    full_name_field,
                    role_dropdown,
                ],
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                height=400,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=lambda _: setattr(dialog, "open", False)),
                ft.ElevatedButton(
                    "Add",
                    on_click=lambda _: setattr(dialog, "open", False),
                ),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )

        # Show the dialog using the new API
        if hasattr(self.page, 'overlay') and self.page.overlay is not None:
            self.page.overlay.append(dialog)
            dialog.open = True
            self.page.update()
        else:
            # Fallback to the old API
            self.page.dialog = dialog
            dialog.open = True
            self.page.update()

    def toggle_theme_mode(self, e):
        """
        Toggle the theme mode between light and dark.

        Args:
            e: The change event
        """
        if self.page.theme_mode == ft.ThemeMode.LIGHT:
            self.page.theme_mode = ft.ThemeMode.DARK
        else:
            self.page.theme_mode = ft.ThemeMode.LIGHT

        self.page.update()

    def show_global_search(self, e):
        """
        Show the global search dialog.

        Args:
            e: The event
        """
        # Create search field
        search_field = ft.TextField(
            label="Search",
            prefix_icon=ft.icons.SEARCH,
            autofocus=True,
            width=500,
            on_submit=lambda e: self.perform_global_search(search_field.value),
        )

        # Create search categories
        categories = ft.Tabs(
            selected_index=0,
            tabs=[
                ft.Tab(text="All"),
                ft.Tab(text="Inventory"),
                ft.Tab(text="Experiments"),
                ft.Tab(text="Users"),
                ft.Tab(text="Reports"),
            ],
            width=500,
        )

        # Create recent searches
        recent_searches = ft.Column(
            [
                ft.Text("Recent Searches", weight=ft.FontWeight.BOLD),
                ft.Container(height=5),
                ft.Row([ft.Icon(ft.icons.HISTORY, size=16), ft.Text("microscope")]),
                ft.Row([ft.Icon(ft.icons.HISTORY, size=16), ft.Text("chemical analysis")]),
                ft.Row([ft.Icon(ft.icons.HISTORY, size=16), ft.Text("john doe")]),
            ],
            spacing=10,
        )

        # Create search dialog
        search_dialog = ft.AlertDialog(
            title=ft.Text("Global Search"),
            content=ft.Column(
                [
                    search_field,
                    ft.Container(height=10),
                    categories,
                    ft.Divider(),
                    recent_searches,
                ],
                spacing=10,
                width=500,
                height=300,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=lambda _: setattr(search_dialog, "open", False)),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )

        # Show the dialog
        if hasattr(self.page, 'overlay') and self.page.overlay is not None:
            self.page.overlay.append(search_dialog)
            search_dialog.open = True
            self.page.update()
        else:
            self.page.dialog = search_dialog
            search_dialog.open = True
            self.page.update()

    def perform_global_search(self, query):
        """
        Perform a global search.

        Args:
            query (str): The search query
        """
        # This would be implemented to search across all data
        self.logger.info(f"Performing global search: {query}")
        # For now, just close the dialog
        self.page.dialog.open = False
        self.page.update()

    def show_notifications(self, e):
        """
        Show the notifications panel.

        Args:
            e: The event
        """
        # Create notifications
        notifications = [
            {
                "id": 1,
                "type": "warning",
                "icon": ft.icons.WARNING_AMBER_ROUNDED,
                "color": ft.colors.AMBER,
                "title": "Low Stock Alert",
                "message": "5 inventory items are low on stock",
                "time": "10 minutes ago",
                "read": False
            },
            {
                "id": 2,
                "type": "info",
                "icon": ft.icons.EVENT_AVAILABLE,
                "color": ft.colors.GREEN,
                "title": "Upcoming Event",
                "message": "Lab meeting scheduled for tomorrow at 10:00 AM",
                "time": "1 hour ago",
                "read": False
            },
            {
                "id": 3,
                "type": "alert",
                "icon": ft.icons.SCIENCE,
                "color": ft.colors.BLUE,
                "title": "Experiment Due",
                "message": "Experiment #EXP003 is due in 3 days",
                "time": "2 hours ago",
                "read": False
            },
            {
                "id": 4,
                "type": "info",
                "icon": ft.icons.PERSON_ADD,
                "color": ft.colors.PURPLE,
                "title": "New User",
                "message": "New user 'Sarah Wilson' has been added to the system",
                "time": "Yesterday",
                "read": True
            },
            {
                "id": 5,
                "type": "success",
                "icon": ft.icons.CHECK_CIRCLE,
                "color": ft.colors.GREEN,
                "title": "Backup Complete",
                "message": "System backup completed successfully",
                "time": "2 days ago",
                "read": True
            }
        ]

        # Create notification items
        notification_items = []
        for notification in notifications:
            bg_color = ft.colors.WHITE if notification["read"] else ft.colors.BLUE_50

            notification_items.append(
                ft.Container(
                    content=ft.Row(
                        [
                            ft.Container(
                                content=ft.Icon(
                                    name=notification["icon"],
                                    color=ft.colors.WHITE,
                                    size=20,
                                ),
                                bgcolor=notification["color"],
                                width=40,
                                height=40,
                                border_radius=20,
                                alignment=ft.alignment.center,
                            ),
                            ft.Column(
                                [
                                    ft.Text(notification["title"], weight=ft.FontWeight.BOLD),
                                    ft.Text(notification["message"], size=14),
                                    ft.Text(notification["time"], size=12, color=ft.colors.GREY),
                                ],
                                spacing=2,
                                expand=True,
                            ),
                            ft.IconButton(
                                icon=ft.icons.DELETE_OUTLINE,
                                icon_size=20,
                                tooltip="Delete",
                                on_click=lambda e, id=notification["id"]: self.delete_notification(id),
                            ),
                        ],
                        spacing=10,
                        alignment=ft.MainAxisAlignment.START,
                    ),
                    padding=10,
                    border_radius=8,
                    bgcolor=bg_color,
                    border=ft.border.all(1, ft.colors.GREY_300),
                    margin=ft.margin.only(bottom=5),
                )
            )

        # Create notification panel
        notification_panel = ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Row(
                            [
                                ft.Text("Notifications", size=18, weight=ft.FontWeight.BOLD),
                                ft.Container(expand=True),
                                ft.TextButton("Mark All as Read", on_click=self.mark_all_notifications_read),
                                ft.TextButton("Clear All", on_click=self.clear_all_notifications),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Divider(),
                        ft.Column(
                            notification_items,
                            scroll=ft.ScrollMode.AUTO,
                            spacing=5,
                            height=400,
                        ) if notification_items else ft.Container(
                            content=ft.Column(
                                [
                                    ft.Icon(ft.icons.NOTIFICATIONS_OFF, size=50, color=ft.colors.GREY),
                                    ft.Text("No notifications", size=16, color=ft.colors.GREY),
                                ],
                                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                                spacing=10,
                            ),
                            alignment=ft.alignment.center,
                            height=400,
                        ),
                    ],
                    spacing=10,
                ),
                padding=15,
                width=400,
            ),
            elevation=5,
        )

        # Calculate position for the notification panel
        # Position it below the notification icon
        notification_icon_pos = e.control.page_position
        if notification_icon_pos:
            x = notification_icon_pos.x - 350  # Adjust to position the panel correctly
            y = notification_icon_pos.y + 50   # Position below the app bar

            # Create a positioned notification panel
            positioned_panel = ft.Stack([
                ft.Container(),  # Empty container as base layer
                ft.Container(
                    content=notification_panel,
                    left=x,
                    top=y,
                )
            ])

            # Show the panel
            if hasattr(self.page, 'overlay') and self.page.overlay is not None:
                # Clear any existing notification panels
                self.page.overlay = [o for o in self.page.overlay if not hasattr(o, 'data') or o.data != 'notification_panel']

                # Add the new panel
                positioned_panel.data = 'notification_panel'
                self.page.overlay.append(positioned_panel)
                self.page.update()
            else:
                # Fallback to dialog
                notification_dialog = ft.AlertDialog(
                    content=notification_panel,
                    actions=[
                        ft.TextButton("Close", on_click=lambda _: setattr(notification_dialog, "open", False)),
                    ],
                )
                self.page.dialog = notification_dialog
                notification_dialog.open = True
                self.page.update()
        else:
            # Fallback if position can't be determined
            notification_dialog = ft.AlertDialog(
                content=notification_panel,
                actions=[
                    ft.TextButton("Close", on_click=lambda _: setattr(notification_dialog, "open", False)),
                ],
            )
            self.page.dialog = notification_dialog
            notification_dialog.open = True
            self.page.update()

        # Update notification badge
        self.update_notification_badge()

    def delete_notification(self, notification_id):
        """
        Delete a notification.

        Args:
            notification_id: The ID of the notification to delete
        """
        self.logger.info(f"Deleting notification: {notification_id}")
        # This would delete the notification from the database
        # For now, just update the badge count
        if self.notification_count > 0:
            self.notification_count -= 1
            self.notification_icon.badge = str(self.notification_count)
            self.page.update()

    def mark_all_notifications_read(self, e):
        """
        Mark all notifications as read.

        Args:
            e: The event
        """
        self.logger.info("Marking all notifications as read")
        # This would mark all notifications as read in the database
        # For now, just update the badge count
        self.notification_count = 0
        self.notification_icon.badge = "0"
        self.page.update()

    def clear_all_notifications(self, e):
        """
        Clear all notifications.

        Args:
            e: The event
        """
        self.logger.info("Clearing all notifications")
        # This would clear all notifications from the database
        # For now, just update the badge count
        self.notification_count = 0
        self.notification_icon.badge = "0"
        self.page.update()

    def update_notification_badge(self):
        """
        Update the notification badge count.
        """
        # This would get the count of unread notifications from the database
        # For now, just use the current count
        self.notification_icon.badge = str(self.notification_count)
        self.page.update()

    def show_help(self, e):
        """
        Show the help dialog.

        Args:
            e: The event
        """
        help_dialog = ft.AlertDialog(
            title=ft.Text("Help"),
            content=ft.Column(
                [
                    ft.Text("Science Laboratory Management System", weight=ft.FontWeight.BOLD),
                    ft.Text("Version: 1.0.0"),
                    ft.Divider(),
                    ft.Text("This application helps manage laboratory resources, experiments, and scheduling."),
                    ft.Text("For more information, please contact the administrator."),
                    ft.Divider(),
                    ft.Text("Navigation:", weight=ft.FontWeight.BOLD),
                    ft.Text("• Dashboard: Overview of laboratory activities"),
                    ft.Text("• Inventory: Manage laboratory equipment and supplies"),
                    ft.Text("• Experiments: Track and manage experiments"),
                    ft.Text("• Scheduling: Schedule laboratory activities"),
                    ft.Text("• Reports: Generate and view reports"),
                ],
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                height=300,
            ),
            actions=[
                ft.TextButton("Close", on_click=lambda _: setattr(help_dialog, "open", False)),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )

        # Show the dialog using the new API
        if hasattr(self.page, 'overlay') and self.page.overlay is not None:
            self.page.overlay.append(help_dialog)
            help_dialog.open = True
            self.page.update()
        else:
            # Fallback to the old API
            self.page.dialog = help_dialog
            help_dialog.open = True
            self.page.update()

    def show_error(self, message):
        """
        Show an error dialog.

        Args:
            message (str): The error message
        """
        error_dialog = ft.AlertDialog(
            title=ft.Text("Error"),
            content=ft.Text(message),
            actions=[
                ft.TextButton("OK", on_click=lambda _: setattr(error_dialog, "open", False)),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )

        # Show the dialog using the new API
        if hasattr(self.page, 'overlay') and self.page.overlay is not None:
            self.page.overlay.append(error_dialog)
            error_dialog.open = True
            self.page.update()
        else:
            # Fallback to the old API
            self.page.dialog = error_dialog
            error_dialog.open = True
            self.page.update()

        # Log the error
        self.logger.error(f"Error: {message}")

    def handle_error(self, e):
        """
        Handle application errors.

        Args:
            e: The error event
        """
        # Log the error
        self.logger.error(f"Application error: {str(e)}")

        # Show error message
        self.show_error(f"An error occurred: {str(e)}")

    def handle_window_event(self, e):
        """
        Handle window events.

        Args:
            e: The window event
        """
        # Check if the event is a close event
        if hasattr(e, 'data') and e.data == "close":
            # Perform cleanup before closing
            self.cleanup()
        # For the new API, the event might be different
        elif hasattr(e, 'type') and e.type == "close":
            # Perform cleanup before closing
            self.cleanup()

    def register_keyboard_shortcuts(self, route_name):
        """
        Register keyboard shortcuts for the current page.

        Args:
            route_name (str): The current route name
        """
        # Start with global shortcuts
        self.active_shortcuts = {**self.keyboard_shortcuts["global"]}

        # Add page-specific shortcuts if they exist
        if route_name in self.keyboard_shortcuts:
            self.active_shortcuts.update(self.keyboard_shortcuts[route_name])

    def handle_keyboard_event(self, e):
        """
        Handle keyboard events.

        Args:
            e: The keyboard event
        """
        # Check if this is a key combination we're interested in
        if e.key:
            # Build the key combination string (e.g., "ctrl+s")
            key_combo = ""
            if e.ctrl:
                key_combo += "ctrl+"
            if e.alt:
                key_combo += "alt+"
            if e.shift:
                key_combo += "shift+"

            # Add the main key
            key_combo += e.key.lower()

            # Check if this combination is in our active shortcuts
            if key_combo in self.active_shortcuts:
                # Execute the shortcut function
                self.active_shortcuts[key_combo](e)
                return True  # Indicate that we've handled this event

        return False  # Let the event propagate

    def show_keyboard_shortcuts(self, e=None):
        """
        Show the keyboard shortcuts dialog.

        Args:
            e: The event (optional)
        """
        # Create shortcut categories
        global_shortcuts = ft.Column(
            [
                ft.Row([ft.Text("Ctrl + /", weight=ft.FontWeight.BOLD), ft.Text("Show Keyboard Shortcuts")]),
                ft.Row([ft.Text("Ctrl + H", weight=ft.FontWeight.BOLD), ft.Text("Show Help")]),
                ft.Row([ft.Text("Ctrl + S", weight=ft.FontWeight.BOLD), ft.Text("Open Settings")]),
                ft.Row([ft.Text("Ctrl + F", weight=ft.FontWeight.BOLD), ft.Text("Global Search")]),
                ft.Row([ft.Text("Ctrl + N", weight=ft.FontWeight.BOLD), ft.Text("Show Notifications")]),
                ft.Row([ft.Text("Ctrl + L", weight=ft.FontWeight.BOLD), ft.Text("Logout")]),
            ],
            spacing=10,
        )

        navigation_shortcuts = ft.Column(
            [
                ft.Row([ft.Text("Ctrl + 1", weight=ft.FontWeight.BOLD), ft.Text("Go to Dashboard")]),
                ft.Row([ft.Text("Ctrl + 2", weight=ft.FontWeight.BOLD), ft.Text("Go to Inventory")]),
                ft.Row([ft.Text("Ctrl + 3", weight=ft.FontWeight.BOLD), ft.Text("Go to Experiments")]),
                ft.Row([ft.Text("Ctrl + 4", weight=ft.FontWeight.BOLD), ft.Text("Go to Scheduling")]),
                ft.Row([ft.Text("Ctrl + 5", weight=ft.FontWeight.BOLD), ft.Text("Go to Reports")]),
                ft.Row([ft.Text("Ctrl + 6", weight=ft.FontWeight.BOLD), ft.Text("Go to Users")]),
            ],
            spacing=10,
        )

        page_shortcuts = ft.Column(
            [
                ft.Row([ft.Text("Ctrl + A", weight=ft.FontWeight.BOLD), ft.Text("Add New Item (context-dependent)")]),
            ],
            spacing=10,
        )

        # Create tabs for different shortcut categories
        shortcut_tabs = ft.Tabs(
            selected_index=0,
            tabs=[
                ft.Tab(
                    text="Global",
                    content=ft.Container(
                        content=global_shortcuts,
                        padding=20,
                    ),
                ),
                ft.Tab(
                    text="Navigation",
                    content=ft.Container(
                        content=navigation_shortcuts,
                        padding=20,
                    ),
                ),
                ft.Tab(
                    text="Page-Specific",
                    content=ft.Container(
                        content=page_shortcuts,
                        padding=20,
                    ),
                ),
            ],
            width=500,
        )

        # Create dialog
        shortcuts_dialog = ft.AlertDialog(
            title=ft.Text("Keyboard Shortcuts"),
            content=ft.Column(
                [
                    ft.Text("Use these keyboard shortcuts to quickly navigate and perform actions."),
                    ft.Container(height=10),
                    shortcut_tabs,
                ],
                width=500,
                height=400,
            ),
            actions=[
                ft.TextButton("Close", on_click=lambda _: setattr(shortcuts_dialog, "open", False)),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )

        # Show the dialog
        if hasattr(self.page, 'overlay') and self.page.overlay is not None:
            self.page.overlay.append(shortcuts_dialog)
            shortcuts_dialog.open = True
            self.page.update()
        else:
            self.page.dialog = shortcuts_dialog
            shortcuts_dialog.open = True
            self.page.update()

    def cleanup(self):
        """Perform cleanup before application exit."""
        self.logger.info("Cleaning up before exit")


def main():
    """Main function to run the application."""
    ft.app(target=lambda page: LabManagementApp(page))


if __name__ == "__main__":
    main()