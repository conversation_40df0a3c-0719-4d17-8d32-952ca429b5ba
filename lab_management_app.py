#!/usr/bin/env python
"""
Science Laboratory Management System Application
This is a simplified version of the application with multiple pages.
"""
import flet as ft
import os
import sys
import logging
from datetime import datetime
import json

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(project_root, 'app.log'))
    ]
)

logger = logging.getLogger('lab_management')

class LabManagementApp:
    """
    Main application class for the Science Laboratory Management System.
    """
    
    def __init__(self, page: ft.Page):
        """
        Initialize the application.
        
        Args:
            page (ft.Page): The Flet page
        """
        self.page = page
        self.logger = logger
        self.logger.info("Initializing application")
        
        # Load configuration
        self.config_path = os.path.join(project_root, "config", "app_config.json")
        self.config = self.load_config()
        
        # Set up the page
        self.setup_page()
        
        # Create the layout
        self.create_layout()
        
        # Navigate to the login page
        self.navigate_to("login")
    
    def load_config(self):
        """
        Load the application configuration.
        
        Returns:
            dict: The application configuration
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, "r") as f:
                    return json.load(f)
            else:
                self.logger.error(f"Configuration file not found: {self.config_path}")
                return {}
        except Exception as e:
            self.logger.error(f"Error loading configuration: {str(e)}")
            return {}
    
    def setup_page(self):
        """Configure the application page settings."""
        # Set page properties
        self.page.title = "Science Laboratory Management System"
        self.page.padding = 0
        self.page.theme_mode = ft.ThemeMode.LIGHT
        
        # Set window properties using the new API
        if hasattr(self.page, 'window') and self.page.window:
            self.page.window.width = 1200
            self.page.window.height = 800
            self.page.window.min_width = 800
            self.page.window.min_height = 600
            
            # Handle window close
            self.page.window.on_event = self.handle_window_event
        
        # Set up theme
        self.page.theme = ft.Theme(
            color_scheme_seed=ft.Colors.BLUE,
            visual_density=ft.VisualDensity.COMFORTABLE,
        )
        
        # Set up error handling
        self.page.on_error = self.handle_error
    
    def create_layout(self):
        """Create the main application layout."""
        # Create app bar
        self.app_bar = ft.AppBar(
            title=ft.Text("Science Laboratory Management System"),
            center_title=False,
            bgcolor=ft.Colors.BLUE,
            color=ft.Colors.WHITE,
            actions=[
                ft.IconButton(
                    icon=ft.Icons.ACCOUNT_CIRCLE,
                    tooltip="User Profile",
                    on_click=self.show_user_menu,
                ),
                ft.IconButton(
                    icon=ft.Icons.HELP_OUTLINE,
                    tooltip="Help",
                    on_click=self.show_help,
                ),
            ],
        )
        
        # Create navigation rail
        self.nav_rail = ft.NavigationRail(
            selected_index=0,
            label_type=ft.NavigationRailLabelType.ALL,
            min_width=100,
            min_extended_width=200,
            destinations=[
                ft.NavigationRailDestination(
                    icon=ft.Icons.DASHBOARD,
                    selected_icon=ft.Icons.DASHBOARD,
                    label="Dashboard",
                ),
                ft.NavigationRailDestination(
                    icon=ft.Icons.INVENTORY,
                    selected_icon=ft.Icons.INVENTORY,
                    label="Inventory",
                ),
                ft.NavigationRailDestination(
                    icon=ft.Icons.SCIENCE,
                    selected_icon=ft.Icons.SCIENCE,
                    label="Experiments",
                ),
                ft.NavigationRailDestination(
                    icon=ft.Icons.CALENDAR_TODAY,
                    selected_icon=ft.Icons.CALENDAR_TODAY,
                    label="Scheduling",
                ),
                ft.NavigationRailDestination(
                    icon=ft.Icons.ANALYTICS,
                    selected_icon=ft.Icons.ANALYTICS,
                    label="Reports",
                ),
            ],
            on_change=self.nav_rail_change,
            visible=False,  # Will be shown after login
        )
        
        # Create main content area
        self.content_area = ft.Container(
            expand=True,
            content=ft.Column(
                [
                    ft.Text("Welcome to the Science Laboratory Management System"),
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.CENTER,
            ),
        )
        
        # Create footer
        self.footer = ft.Container(
            content=ft.Row(
                [
                    ft.Text("© 2025 Science Laboratory Management System"),
                    ft.Container(expand=True),
                    ft.Text(f"Version: {self.config.get('app', {}).get('version', '1.0.0')}"),
                ],
                spacing=10,
            ),
            padding=5,
            bgcolor=ft.Colors.BLUE_50,
        )
        
        # Create main layout
        self.page.add(
            ft.Column(
                [
                    self.app_bar,
                    ft.Container(height=1, bgcolor=ft.Colors.BLUE_200),
                    ft.Row(
                        [
                            self.nav_rail,
                            ft.VerticalDivider(width=1, visible=False),  # Will be shown after login
                            self.content_area,
                        ],
                        expand=True,
                    ),
                    self.footer,
                ],
                expand=True,
            )
        )
    
    def nav_rail_change(self, e):
        """
        Handle navigation rail changes.
        
        Args:
            e: The change event
        """
        index = e.control.selected_index
        
        if index == 0:
            self.navigate_to("dashboard")
        elif index == 1:
            self.navigate_to("inventory")
        elif index == 2:
            self.navigate_to("experiments")
        elif index == 3:
            self.navigate_to("scheduling")
        elif index == 4:
            self.navigate_to("reports")
    
    def show_user_menu(self, e):
        """
        Show the user menu.
        
        Args:
            e: The click event
        """
        # Create popup menu
        menu = ft.PopupMenuButton(
            items=[
                ft.PopupMenuItem(
                    text="Profile",
                    icon=ft.Icons.PERSON,
                    on_click=lambda e: self.navigate_to("profile"),
                ),
                ft.PopupMenuItem(
                    text="Settings",
                    icon=ft.Icons.SETTINGS,
                    on_click=lambda e: self.navigate_to("settings"),
                ),
                ft.PopupMenuItem(
                    text="Logout",
                    icon=ft.Icons.LOGOUT,
                    on_click=lambda e: self.logout(),
                ),
            ],
        )
        
        # Show the menu
        menu.open = True
        self.page.update()
    
    def navigate_to(self, route_name, params=None):
        """
        Navigate to a specific route.
        
        Args:
            route_name (str): The name of the route to navigate to
            params (dict, optional): Parameters to pass to the route
        """
        self.logger.info(f"Navigated to {route_name}")
        
        # Clear the content area
        if route_name == "login":
            # Hide navigation rail for login page
            self.nav_rail.visible = False
            self.page.controls[0].controls[2].controls[1].visible = False  # Hide vertical divider
            self.show_login_page()
        elif route_name == "dashboard":
            # Show navigation rail for dashboard and other pages
            self.nav_rail.visible = True
            self.page.controls[0].controls[2].controls[1].visible = True  # Show vertical divider
            self.show_dashboard_page()
        elif route_name == "inventory":
            self.nav_rail.visible = True
            self.page.controls[0].controls[2].controls[1].visible = True
            self.show_inventory_page()
        elif route_name == "experiments":
            self.nav_rail.visible = True
            self.page.controls[0].controls[2].controls[1].visible = True
            self.show_experiments_page()
        elif route_name == "scheduling":
            self.nav_rail.visible = True
            self.page.controls[0].controls[2].controls[1].visible = True
            self.show_scheduling_page()
        elif route_name == "reports":
            self.nav_rail.visible = True
            self.page.controls[0].controls[2].controls[1].visible = True
            self.show_reports_page()
        else:
            self.content_area.content = ft.Column(
                [
                    ft.Text(f"This is the {route_name} page"),
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.CENTER,
            )
        
        # Update the page
        self.page.update()
    
    def logout(self):
        """Log out the user and return to the login page."""
        self.logger.info("User logged out")
        self.navigate_to("login")
    
    def show_login_page(self):
        """Show the login page."""
        # Create login form
        username_field = ft.TextField(
            label="Username",
            autofocus=True,
            prefix_icon=ft.Icons.PERSON,
            width=300,
        )
        
        password_field = ft.TextField(
            label="Password",
            password=True,
            prefix_icon=ft.Icons.LOCK,
            width=300,
        )
        
        def login_clicked(e):
            if not username_field.value:
                username_field.error_text = "Username cannot be empty"
                self.page.update()
                return
            
            if not password_field.value:
                password_field.error_text = "Password cannot be empty"
                self.page.update()
                return
            
            # For demo purposes, accept any username/password
            self.navigate_to("dashboard")
        
        login_button = ft.ElevatedButton(
            "Login",
            on_click=login_clicked,
            width=300,
            icon=ft.Icons.LOGIN,
        )
        
        register_button = ft.TextButton(
            "Register",
            on_click=lambda e: self.navigate_to("register"),
        )
        
        forgot_password_button = ft.TextButton(
            "Forgot Password?",
            on_click=lambda e: self.navigate_to("forgot_password"),
        )
        
        # Create login form
        login_form = ft.Column(
            [
                ft.Text("Login", size=24, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                username_field,
                password_field,
                ft.Container(height=20),
                login_button,
                ft.Row(
                    [
                        register_button,
                        forgot_password_button,
                    ],
                    alignment=ft.MainAxisAlignment.CENTER,
                ),
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=10,
            width=400,
        )
        
        # Create login card
        login_card = ft.Card(
            content=ft.Container(
                content=login_form,
                padding=20,
            ),
            elevation=5,
        )
        
        # Set content area
        self.content_area.content = ft.Column(
            [
                ft.Container(height=50),
                ft.Text("Science Laboratory Management System", size=30, weight=ft.FontWeight.BOLD),
                ft.Container(height=20),
                login_card,
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            alignment=ft.MainAxisAlignment.START,
        )
    
    def show_dashboard_page(self):
        """Show the dashboard page."""
        # Create dashboard content
        dashboard_content = ft.Column(
            [
                ft.Text("Dashboard", size=24, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                ft.Row(
                    [
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column(
                                    [
                                        ft.Text("Inventory", size=20, weight=ft.FontWeight.BOLD),
                                        ft.Text("Total Items: 250"),
                                        ft.Text("Low Stock: 15"),
                                    ],
                                    spacing=10,
                                ),
                                padding=20,
                                width=200,
                                height=150,
                            ),
                            elevation=5,
                        ),
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column(
                                    [
                                        ft.Text("Experiments", size=20, weight=ft.FontWeight.BOLD),
                                        ft.Text("Active: 12"),
                                        ft.Text("Completed: 45"),
                                    ],
                                    spacing=10,
                                ),
                                padding=20,
                                width=200,
                                height=150,
                            ),
                            elevation=5,
                        ),
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column(
                                    [
                                        ft.Text("Users", size=20, weight=ft.FontWeight.BOLD),
                                        ft.Text("Total: 35"),
                                        ft.Text("Active: 12"),
                                    ],
                                    spacing=10,
                                ),
                                padding=20,
                                width=200,
                                height=150,
                            ),
                            elevation=5,
                        ),
                    ],
                    spacing=20,
                    alignment=ft.MainAxisAlignment.CENTER,
                ),
                ft.Container(height=20),
                ft.Text("Recent Activity", size=20, weight=ft.FontWeight.BOLD),
                ft.DataTable(
                    columns=[
                        ft.DataColumn(ft.Text("Date")),
                        ft.DataColumn(ft.Text("User")),
                        ft.DataColumn(ft.Text("Activity")),
                    ],
                    rows=[
                        ft.DataRow(
                            cells=[
                                ft.DataCell(ft.Text("2025-05-23")),
                                ft.DataCell(ft.Text("John Doe")),
                                ft.DataCell(ft.Text("Added new experiment")),
                            ],
                        ),
                        ft.DataRow(
                            cells=[
                                ft.DataCell(ft.Text("2025-05-22")),
                                ft.DataCell(ft.Text("Jane Smith")),
                                ft.DataCell(ft.Text("Updated inventory")),
                            ],
                        ),
                        ft.DataRow(
                            cells=[
                                ft.DataCell(ft.Text("2025-05-21")),
                                ft.DataCell(ft.Text("Bob Johnson")),
                                ft.DataCell(ft.Text("Completed experiment")),
                            ],
                        ),
                    ],
                ),
            ],
            spacing=20,
            scroll=ft.ScrollMode.AUTO,
        )
        
        # Set content area
        self.content_area.content = dashboard_content
        
        # Update navigation rail
        self.nav_rail.selected_index = 0
    
    def show_inventory_page(self):
        """Show the inventory page."""
        # Create inventory content
        
        # Create search field
        search_field = ft.TextField(
            label="Search Inventory",
            prefix_icon=ft.Icons.SEARCH,
            expand=True,
        )
        
        # Create filter dropdown
        filter_dropdown = ft.Dropdown(
            label="Filter By",
            options=[
                ft.dropdown.Option("All"),
                ft.dropdown.Option("Chemicals"),
                ft.dropdown.Option("Equipment"),
                ft.dropdown.Option("Glassware"),
                ft.dropdown.Option("Consumables"),
            ],
            value="All",
            width=200,
        )
        
        # Create add button
        add_button = ft.ElevatedButton(
            "Add Item",
            icon=ft.Icons.ADD,
            on_click=lambda e: self.show_add_inventory_dialog(),
        )
        
        # Create inventory table
        inventory_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("ID")),
                ft.DataColumn(ft.Text("Name")),
                ft.DataColumn(ft.Text("Category")),
                ft.DataColumn(ft.Text("Quantity")),
                ft.DataColumn(ft.Text("Location")),
                ft.DataColumn(ft.Text("Status")),
                ft.DataColumn(ft.Text("Actions")),
            ],
            rows=[
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("INV001")),
                        ft.DataCell(ft.Text("Microscope")),
                        ft.DataCell(ft.Text("Equipment")),
                        ft.DataCell(ft.Text("5")),
                        ft.DataCell(ft.Text("Lab 1")),
                        ft.DataCell(ft.Text("Available")),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.Icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.DELETE,
                                        tooltip="Delete",
                                        icon_color=ft.Colors.RED,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("INV002")),
                        ft.DataCell(ft.Text("Test Tubes")),
                        ft.DataCell(ft.Text("Glassware")),
                        ft.DataCell(ft.Text("100")),
                        ft.DataCell(ft.Text("Storage Room")),
                        ft.DataCell(ft.Text("Low Stock")),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.Icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.DELETE,
                                        tooltip="Delete",
                                        icon_color=ft.Colors.RED,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("INV003")),
                        ft.DataCell(ft.Text("Bunsen Burner")),
                        ft.DataCell(ft.Text("Equipment")),
                        ft.DataCell(ft.Text("10")),
                        ft.DataCell(ft.Text("Lab 2")),
                        ft.DataCell(ft.Text("Available")),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.Icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.DELETE,
                                        tooltip="Delete",
                                        icon_color=ft.Colors.RED,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("INV004")),
                        ft.DataCell(ft.Text("Sodium Chloride")),
                        ft.DataCell(ft.Text("Chemicals")),
                        ft.DataCell(ft.Text("500g")),
                        ft.DataCell(ft.Text("Chemical Storage")),
                        ft.DataCell(ft.Text("Available")),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.Icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.DELETE,
                                        tooltip="Delete",
                                        icon_color=ft.Colors.RED,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
            ],
        )
        
        # Create pagination controls
        pagination = ft.Row(
            [
                ft.Text("Page 1 of 5"),
                ft.IconButton(
                    icon=ft.Icons.ARROW_BACK,
                    tooltip="Previous Page",
                ),
                ft.IconButton(
                    icon=ft.Icons.ARROW_FORWARD,
                    tooltip="Next Page",
                ),
            ],
            alignment=ft.MainAxisAlignment.CENTER,
        )
        
        # Create inventory content
        inventory_content = ft.Column(
            [
                ft.Text("Inventory Management", size=24, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                ft.Row(
                    [
                        search_field,
                        filter_dropdown,
                        add_button,
                    ],
                    spacing=10,
                ),
                ft.Container(height=20),
                inventory_table,
                ft.Container(height=20),
                pagination,
            ],
            spacing=10,
            scroll=ft.ScrollMode.AUTO,
        )
        
        # Set content area
        self.content_area.content = inventory_content
        
        # Update navigation rail
        self.nav_rail.selected_index = 1
    
    def show_add_inventory_dialog(self):
        """Show the add inventory dialog."""
        # Create form fields
        name_field = ft.TextField(
            label="Item Name",
            autofocus=True,
            width=400,
        )
        
        category_dropdown = ft.Dropdown(
            label="Category",
            options=[
                ft.dropdown.Option("Chemicals"),
                ft.dropdown.Option("Equipment"),
                ft.dropdown.Option("Glassware"),
                ft.dropdown.Option("Consumables"),
            ],
            width=400,
        )
        
        quantity_field = ft.TextField(
            label="Quantity",
            width=400,
        )
        
        location_field = ft.TextField(
            label="Location",
            width=400,
        )
        
        description_field = ft.TextField(
            label="Description",
            multiline=True,
            min_lines=3,
            max_lines=5,
            width=400,
        )
        
        # Create dialog
        dialog = ft.AlertDialog(
            title=ft.Text("Add Inventory Item"),
            content=ft.Column(
                [
                    name_field,
                    category_dropdown,
                    quantity_field,
                    location_field,
                    description_field,
                ],
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                height=400,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(dialog, "open", False)),
                ft.ElevatedButton(
                    "Add",
                    on_click=lambda e: setattr(dialog, "open", False),
                ),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog using the new API
        if hasattr(self.page, 'overlay') and self.page.overlay is not None:
            self.page.overlay.append(dialog)
            dialog.open = True
            self.page.update()
        else:
            # Fallback to the old API
            self.page.dialog = dialog
            dialog.open = True
            self.page.update()
    
    def show_experiments_page(self):
        """Show the experiments page."""
        # Create experiments content
        
        # Create search field
        search_field = ft.TextField(
            label="Search Experiments",
            prefix_icon=ft.Icons.SEARCH,
            expand=True,
        )
        
        # Create filter dropdown
        filter_dropdown = ft.Dropdown(
            label="Filter By",
            options=[
                ft.dropdown.Option("All"),
                ft.dropdown.Option("Active"),
                ft.dropdown.Option("Completed"),
                ft.dropdown.Option("Planned"),
            ],
            value="All",
            width=200,
        )
        
        # Create add button
        add_button = ft.ElevatedButton(
            "New Experiment",
            icon=ft.Icons.ADD,
            on_click=lambda e: self.show_add_experiment_dialog(),
        )
        
        # Create experiments table
        experiments_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("ID")),
                ft.DataColumn(ft.Text("Title")),
                ft.DataColumn(ft.Text("Status")),
                ft.DataColumn(ft.Text("Start Date")),
                ft.DataColumn(ft.Text("End Date")),
                ft.DataColumn(ft.Text("Researcher")),
                ft.DataColumn(ft.Text("Actions")),
            ],
            rows=[
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("EXP001")),
                        ft.DataCell(ft.Text("Water Quality Analysis")),
                        ft.DataCell(ft.Text("Active")),
                        ft.DataCell(ft.Text("2025-05-01")),
                        ft.DataCell(ft.Text("2025-06-01")),
                        ft.DataCell(ft.Text("John Doe")),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.Icons.VISIBILITY,
                                        tooltip="View",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.DELETE,
                                        tooltip="Delete",
                                        icon_color=ft.Colors.RED,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("EXP002")),
                        ft.DataCell(ft.Text("Bacterial Growth Study")),
                        ft.DataCell(ft.Text("Completed")),
                        ft.DataCell(ft.Text("2025-04-15")),
                        ft.DataCell(ft.Text("2025-05-15")),
                        ft.DataCell(ft.Text("Jane Smith")),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.Icons.VISIBILITY,
                                        tooltip="View",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.DELETE,
                                        tooltip="Delete",
                                        icon_color=ft.Colors.RED,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text("EXP003")),
                        ft.DataCell(ft.Text("Chemical Reaction Rates")),
                        ft.DataCell(ft.Text("Planned")),
                        ft.DataCell(ft.Text("2025-06-01")),
                        ft.DataCell(ft.Text("2025-07-01")),
                        ft.DataCell(ft.Text("Bob Johnson")),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.Icons.VISIBILITY,
                                        tooltip="View",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.EDIT,
                                        tooltip="Edit",
                                        icon_color=ft.Colors.BLUE,
                                    ),
                                    ft.IconButton(
                                        icon=ft.Icons.DELETE,
                                        tooltip="Delete",
                                        icon_color=ft.Colors.RED,
                                    ),
                                ]
                            )
                        ),
                    ],
                ),
            ],
        )
        
        # Create pagination controls
        pagination = ft.Row(
            [
                ft.Text("Page 1 of 3"),
                ft.IconButton(
                    icon=ft.Icons.ARROW_BACK,
                    tooltip="Previous Page",
                ),
                ft.IconButton(
                    icon=ft.Icons.ARROW_FORWARD,
                    tooltip="Next Page",
                ),
            ],
            alignment=ft.MainAxisAlignment.CENTER,
        )
        
        # Create experiments content
        experiments_content = ft.Column(
            [
                ft.Text("Experiment Management", size=24, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                ft.Row(
                    [
                        search_field,
                        filter_dropdown,
                        add_button,
                    ],
                    spacing=10,
                ),
                ft.Container(height=20),
                experiments_table,
                ft.Container(height=20),
                pagination,
            ],
            spacing=10,
            scroll=ft.ScrollMode.AUTO,
        )
        
        # Set content area
        self.content_area.content = experiments_content
        
        # Update navigation rail
        self.nav_rail.selected_index = 2
    
    def show_add_experiment_dialog(self):
        """Show the add experiment dialog."""
        # Create form fields
        title_field = ft.TextField(
            label="Experiment Title",
            autofocus=True,
            width=400,
        )
        
        status_dropdown = ft.Dropdown(
            label="Status",
            options=[
                ft.dropdown.Option("Planned"),
                ft.dropdown.Option("Active"),
                ft.dropdown.Option("Completed"),
            ],
            width=400,
        )
        
        start_date_field = ft.TextField(
            label="Start Date (YYYY-MM-DD)",
            width=400,
        )
        
        end_date_field = ft.TextField(
            label="End Date (YYYY-MM-DD)",
            width=400,
        )
        
        researcher_field = ft.TextField(
            label="Researcher",
            width=400,
        )
        
        description_field = ft.TextField(
            label="Description",
            multiline=True,
            min_lines=3,
            max_lines=5,
            width=400,
        )
        
        # Create dialog
        dialog = ft.AlertDialog(
            title=ft.Text("Add New Experiment"),
            content=ft.Column(
                [
                    title_field,
                    status_dropdown,
                    start_date_field,
                    end_date_field,
                    researcher_field,
                    description_field,
                ],
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                height=400,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(dialog, "open", False)),
                ft.ElevatedButton(
                    "Add",
                    on_click=lambda e: setattr(dialog, "open", False),
                ),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog using the new API
        if hasattr(self.page, 'overlay') and self.page.overlay is not None:
            self.page.overlay.append(dialog)
            dialog.open = True
            self.page.update()
        else:
            # Fallback to the old API
            self.page.dialog = dialog
            dialog.open = True
            self.page.update()
    
    def show_scheduling_page(self):
        """Show the scheduling page."""
        # Create scheduling content
        scheduling_content = ft.Column(
            [
                ft.Text("Scheduling", size=24, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                ft.Text("Calendar view will be implemented here."),
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Text("Upcoming Events", size=20, weight=ft.FontWeight.BOLD),
                            ft.DataTable(
                                columns=[
                                    ft.DataColumn(ft.Text("Date")),
                                    ft.DataColumn(ft.Text("Time")),
                                    ft.DataColumn(ft.Text("Event")),
                                    ft.DataColumn(ft.Text("Location")),
                                    ft.DataColumn(ft.Text("Participants")),
                                ],
                                rows=[
                                    ft.DataRow(
                                        cells=[
                                            ft.DataCell(ft.Text("2025-05-25")),
                                            ft.DataCell(ft.Text("10:00 AM")),
                                            ft.DataCell(ft.Text("Lab Meeting")),
                                            ft.DataCell(ft.Text("Conference Room")),
                                            ft.DataCell(ft.Text("All Staff")),
                                        ],
                                    ),
                                    ft.DataRow(
                                        cells=[
                                            ft.DataCell(ft.Text("2025-05-26")),
                                            ft.DataCell(ft.Text("2:00 PM")),
                                            ft.DataCell(ft.Text("Equipment Training")),
                                            ft.DataCell(ft.Text("Lab 2")),
                                            ft.DataCell(ft.Text("New Staff")),
                                        ],
                                    ),
                                    ft.DataRow(
                                        cells=[
                                            ft.DataCell(ft.Text("2025-05-28")),
                                            ft.DataCell(ft.Text("9:00 AM")),
                                            ft.DataCell(ft.Text("Safety Inspection")),
                                            ft.DataCell(ft.Text("All Labs")),
                                            ft.DataCell(ft.Text("Safety Officer")),
                                        ],
                                    ),
                                ],
                            ),
                        ],
                        spacing=10,
                    ),
                    padding=20,
                ),
            ],
            spacing=20,
            scroll=ft.ScrollMode.AUTO,
        )
        
        # Set content area
        self.content_area.content = scheduling_content
        
        # Update navigation rail
        self.nav_rail.selected_index = 3
    
    def show_reports_page(self):
        """Show the reports page."""
        # Create reports content
        reports_content = ft.Column(
            [
                ft.Text("Reports", size=24, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                ft.Row(
                    [
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column(
                                    [
                                        ft.Text("Inventory Report", size=18, weight=ft.FontWeight.BOLD),
                                        ft.Text("Summary of inventory items and status"),
                                        ft.ElevatedButton(
                                            "Generate",
                                            icon=ft.Icons.DOWNLOAD,
                                        ),
                                    ],
                                    spacing=10,
                                ),
                                padding=20,
                                width=250,
                            ),
                            elevation=5,
                        ),
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column(
                                    [
                                        ft.Text("Experiment Report", size=18, weight=ft.FontWeight.BOLD),
                                        ft.Text("Summary of experiments and results"),
                                        ft.ElevatedButton(
                                            "Generate",
                                            icon=ft.Icons.DOWNLOAD,
                                        ),
                                    ],
                                    spacing=10,
                                ),
                                padding=20,
                                width=250,
                            ),
                            elevation=5,
                        ),
                        ft.Card(
                            content=ft.Container(
                                content=ft.Column(
                                    [
                                        ft.Text("Usage Report", size=18, weight=ft.FontWeight.BOLD),
                                        ft.Text("Summary of equipment and material usage"),
                                        ft.ElevatedButton(
                                            "Generate",
                                            icon=ft.Icons.DOWNLOAD,
                                        ),
                                    ],
                                    spacing=10,
                                ),
                                padding=20,
                                width=250,
                            ),
                            elevation=5,
                        ),
                    ],
                    spacing=20,
                    alignment=ft.MainAxisAlignment.CENTER,
                ),
                ft.Container(height=20),
                ft.Text("Recent Reports", size=20, weight=ft.FontWeight.BOLD),
                ft.DataTable(
                    columns=[
                        ft.DataColumn(ft.Text("Date")),
                        ft.DataColumn(ft.Text("Report Type")),
                        ft.DataColumn(ft.Text("Generated By")),
                        ft.DataColumn(ft.Text("Actions")),
                    ],
                    rows=[
                        ft.DataRow(
                            cells=[
                                ft.DataCell(ft.Text("2025-05-20")),
                                ft.DataCell(ft.Text("Inventory Report")),
                                ft.DataCell(ft.Text("John Doe")),
                                ft.DataCell(
                                    ft.Row(
                                        [
                                            ft.IconButton(
                                                icon=ft.Icons.VISIBILITY,
                                                tooltip="View",
                                                icon_color=ft.Colors.BLUE,
                                            ),
                                            ft.IconButton(
                                                icon=ft.Icons.DOWNLOAD,
                                                tooltip="Download",
                                                icon_color=ft.Colors.GREEN,
                                            ),
                                        ]
                                    )
                                ),
                            ],
                        ),
                        ft.DataRow(
                            cells=[
                                ft.DataCell(ft.Text("2025-05-15")),
                                ft.DataCell(ft.Text("Experiment Report")),
                                ft.DataCell(ft.Text("Jane Smith")),
                                ft.DataCell(
                                    ft.Row(
                                        [
                                            ft.IconButton(
                                                icon=ft.Icons.VISIBILITY,
                                                tooltip="View",
                                                icon_color=ft.Colors.BLUE,
                                            ),
                                            ft.IconButton(
                                                icon=ft.Icons.DOWNLOAD,
                                                tooltip="Download",
                                                icon_color=ft.Colors.GREEN,
                                            ),
                                        ]
                                    )
                                ),
                            ],
                        ),
                        ft.DataRow(
                            cells=[
                                ft.DataCell(ft.Text("2025-05-10")),
                                ft.DataCell(ft.Text("Usage Report")),
                                ft.DataCell(ft.Text("Bob Johnson")),
                                ft.DataCell(
                                    ft.Row(
                                        [
                                            ft.IconButton(
                                                icon=ft.Icons.VISIBILITY,
                                                tooltip="View",
                                                icon_color=ft.Colors.BLUE,
                                            ),
                                            ft.IconButton(
                                                icon=ft.Icons.DOWNLOAD,
                                                tooltip="Download",
                                                icon_color=ft.Colors.GREEN,
                                            ),
                                        ]
                                    )
                                ),
                            ],
                        ),
                    ],
                ),
            ],
            spacing=20,
            scroll=ft.ScrollMode.AUTO,
        )
        
        # Set content area
        self.content_area.content = reports_content
        
        # Update navigation rail
        self.nav_rail.selected_index = 4
    
    def show_help(self, e):
        """
        Show the help dialog.
        
        Args:
            e: The event
        """
        help_dialog = ft.AlertDialog(
            title=ft.Text("Help"),
            content=ft.Column(
                [
                    ft.Text("Science Laboratory Management System", weight=ft.FontWeight.BOLD),
                    ft.Text("Version: 1.0.0"),
                    ft.Divider(),
                    ft.Text("This application helps manage laboratory resources, experiments, and scheduling."),
                    ft.Text("For more information, please contact the administrator."),
                    ft.Divider(),
                    ft.Text("Navigation:", weight=ft.FontWeight.BOLD),
                    ft.Text("• Dashboard: Overview of laboratory activities"),
                    ft.Text("• Inventory: Manage laboratory equipment and supplies"),
                    ft.Text("• Experiments: Track and manage experiments"),
                    ft.Text("• Scheduling: Schedule laboratory activities"),
                    ft.Text("• Reports: Generate and view reports"),
                ],
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                height=300,
            ),
            actions=[
                ft.TextButton("Close", on_click=lambda e: setattr(help_dialog, "open", False)),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog using the new API
        if hasattr(self.page, 'overlay') and self.page.overlay is not None:
            self.page.overlay.append(help_dialog)
            help_dialog.open = True
            self.page.update()
        else:
            # Fallback to the old API
            self.page.dialog = help_dialog
            help_dialog.open = True
            self.page.update()
    
    def show_error(self, message):
        """
        Show an error dialog.
        
        Args:
            message (str): The error message
        """
        error_dialog = ft.AlertDialog(
            title=ft.Text("Error"),
            content=ft.Text(message),
            actions=[
                ft.TextButton("OK", on_click=lambda e: setattr(error_dialog, "open", False)),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog using the new API
        if hasattr(self.page, 'overlay') and self.page.overlay is not None:
            self.page.overlay.append(error_dialog)
            error_dialog.open = True
            self.page.update()
        else:
            # Fallback to the old API
            self.page.dialog = error_dialog
            error_dialog.open = True
            self.page.update()
        
        # Log the error
        self.logger.error(f"Error: {message}")
    
    def handle_error(self, e):
        """
        Handle application errors.
        
        Args:
            e: The error event
        """
        # Log the error
        self.logger.error(f"Application error: {str(e)}")
        
        # Show error message
        self.show_error(f"An error occurred: {str(e)}")
    
    def handle_window_event(self, e):
        """
        Handle window events.
        
        Args:
            e: The window event
        """
        # Check if the event is a close event
        if hasattr(e, 'data') and e.data == "close":
            # Perform cleanup before closing
            self.cleanup()
        # For the new API, the event might be different
        elif hasattr(e, 'type') and e.type == "close":
            # Perform cleanup before closing
            self.cleanup()
    
    def cleanup(self):
        """Perform cleanup before application exit."""
        self.logger.info("Cleaning up before exit")

def main(page: ft.Page):
    """
    Main function for the application.
    
    Args:
        page (ft.Page): The Flet page
    """
    app = LabManagementApp(page)

if __name__ == "__main__":
    ft.app(target=main)