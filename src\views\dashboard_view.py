import flet as ft
from datetime import datetime

class DashboardView:
    """
    Dashboard view for the Science Laboratory Management System.
    Displays key metrics and summary information.
    """
    
    def __init__(self, controller):
        """
        Initialize the dashboard view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
    
    def build(self):
        """
        Build and return the dashboard view.
        
        Returns:
            ft.Container: The dashboard view container
        """
        # Get current user information
        user = self.controller.current_user
        
        # Get current date and time
        current_time = datetime.now().strftime("%A, %B %d, %Y %I:%M %p")
        
        # Create welcome message
        welcome_message = ft.Text(
            f"Welcome, {user['full_name']}",
            size=24,
            weight=ft.FontWeight.BOLD,
        )
        
        # Create date/time display
        date_display = ft.Text(
            current_time,
            size=14,
            color=ft.colors.GREY_700,
        )
        
        # Create logout button
        logout_button = ft.ElevatedButton(
            "Logout",
            icon=ft.icons.LOGOUT,
            on_click=lambda _: self.controller.logout(),
        )
        
        # Create header row
        header_row = ft.Row(
            [
                ft.Column(
                    [welcome_message, date_display],
                    expand=True,
                ),
                logout_button,
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        )
        
        # Create dashboard cards
        inventory_card = self._create_dashboard_card(
            "Inventory Items",
            self._get_inventory_count(),
            ft.icons.INVENTORY,
            ft.colors.BLUE,
            "View Inventory",
            lambda _: self._navigate_to_inventory(),
        )
        
        experiments_card = self._create_dashboard_card(
            "Active Experiments",
            self._get_active_experiments_count(),
            ft.icons.SCIENCE,
            ft.colors.GREEN,
            "View Experiments",
            lambda _: self._navigate_to_experiments(),
        )
        
        schedules_card = self._create_dashboard_card(
            "Upcoming Schedules",
            self._get_upcoming_schedules_count(),
            ft.icons.CALENDAR_TODAY,
            ft.colors.ORANGE,
            "View Schedule",
            lambda _: self._navigate_to_scheduling(),
        )
        
        safety_card = self._create_dashboard_card(
            "Safety Incidents",
            self._get_safety_incidents_count(),
            ft.icons.HEALTH_AND_SAFETY,
            ft.colors.RED,
            "View Safety",
            lambda _: self._navigate_to_safety(),
        )
        
        # Create cards row
        cards_row = ft.Row(
            [inventory_card, experiments_card, schedules_card, safety_card],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            wrap=True,
        )
        
        # Create recent activity list
        activity_list = self._create_activity_list()
        
        # Create alerts section
        alerts_section = self._create_alerts_section()
        
        # Return the complete dashboard
        return ft.Container(
            content=ft.Column(
                [
                    header_row,
                    ft.Divider(),
                    ft.Container(height=10),
                    cards_row,
                    ft.Container(height=20),
                    ft.Text("Recent Activity", size=18, weight=ft.FontWeight.BOLD),
                    activity_list,
                    ft.Container(height=20),
                    ft.Text("Alerts & Notifications", size=18, weight=ft.FontWeight.BOLD),
                    alerts_section,
                ],
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
            ),
            padding=20,
            expand=True,
        )
    
    def _create_dashboard_card(self, title, count, icon, color, button_text, button_action):
        """
        Create a dashboard metric card.
        
        Args:
            title (str): Card title
            count (int): Metric count
            icon (str): Icon to display
            color (str): Card accent color
            button_text (str): Button text
            button_action (function): Button click handler
            
        Returns:
            ft.Card: The dashboard card
        """
        return ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Row(
                            [
                                ft.Icon(icon, color=color, size=24),
                                ft.Text(title, weight=ft.FontWeight.BOLD),
                            ],
                            alignment=ft.MainAxisAlignment.START,
                        ),
                        ft.Container(height=10),
                        ft.Text(str(count), size=36, weight=ft.FontWeight.BOLD),
                        ft.Container(height=10),
                        ft.ElevatedButton(
                            text=button_text,
                            on_click=button_action,
                        ),
                    ],
                    spacing=5,
                ),
                padding=15,
                width=250,
            ),
            elevation=2,
        )
    
    def _create_activity_list(self):
        """
        Create the recent activity list.
        
        Returns:
            ft.ListView: The activity list
        """
        # Get recent audit logs
        logs = self._get_recent_audit_logs()
        
        # Create list items
        items = []
        for log in logs:
            items.append(
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Row(
                                [
                                    ft.Text(
                                        log["action"].capitalize(),
                                        weight=ft.FontWeight.BOLD,
                                    ),
                                    ft.Text(
                                        self._format_timestamp(log["timestamp"]),
                                        color=ft.colors.GREY_700,
                                        size=12,
                                    ),
                                ],
                                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                            ),
                            ft.Text(
                                f"{log['entity_type'].capitalize()}: {log['details']}",
                                size=14,
                            ),
                        ],
                    ),
                    padding=10,
                    border=ft.border.only(bottom=ft.BorderSide(1, ft.colors.GREY_300)),
                )
            )
        
        # Return the list view
        return ft.Column(
            items,
            spacing=0,
            height=200,
            scroll=ft.ScrollMode.AUTO,
        )
    
    def _create_alerts_section(self):
        """
        Create the alerts and notifications section.
        
        Returns:
            ft.Column: The alerts section
        """
        # Get alerts
        alerts = self._get_alerts()
        
        # Create alert items
        items = []
        for alert in alerts:
            items.append(
                ft.Container(
                    content=ft.Row(
                        [
                            ft.Icon(
                                alert["icon"],
                                color=alert["color"],
                                size=20,
                            ),
                            ft.Text(
                                alert["message"],
                                size=14,
                            ),
                        ],
                        spacing=10,
                    ),
                    padding=10,
                    border=ft.border.only(bottom=ft.BorderSide(1, ft.colors.GREY_300)),
                )
            )
        
        # Return the alerts column
        return ft.Column(
            items,
            spacing=0,
            height=150,
            scroll=ft.ScrollMode.AUTO,
        )
    
    def _get_inventory_count(self):
        """
        Get the count of inventory items.
        
        Returns:
            int: Count of inventory items
        """
        # Query the database
        result = self.controller.db.execute_query("SELECT COUNT(*) as count FROM inventory_items")
        return result[0]["count"] if result else 0
    
    def _get_active_experiments_count(self):
        """
        Get the count of active experiments.
        
        Returns:
            int: Count of active experiments
        """
        # Query the database
        result = self.controller.db.execute_query(
            "SELECT COUNT(*) as count FROM experiments WHERE status = 'active'"
        )
        return result[0]["count"] if result else 0
    
    def _get_upcoming_schedules_count(self):
        """
        Get the count of upcoming schedules.
        
        Returns:
            int: Count of upcoming schedules
        """
        # Query the database
        current_time = datetime.now().isoformat()
        result = self.controller.db.execute_query(
            "SELECT COUNT(*) as count FROM lab_schedules WHERE start_time > ?",
            (current_time,)
        )
        return result[0]["count"] if result else 0
    
    def _get_safety_incidents_count(self):
        """
        Get the count of open safety incidents.
        
        Returns:
            int: Count of open safety incidents
        """
        # Query the database
        result = self.controller.db.execute_query(
            "SELECT COUNT(*) as count FROM safety_incidents WHERE status != 'resolved'"
        )
        return result[0]["count"] if result else 0
    
    def _get_recent_audit_logs(self):
        """
        Get recent audit logs.
        
        Returns:
            list: List of recent audit logs
        """
        # Query the database
        logs = self.controller.db.execute_query(
            "SELECT * FROM audit_logs ORDER BY timestamp DESC LIMIT 10"
        )
        return logs
    
    def _get_alerts(self):
        """
        Get alerts and notifications.
        
        Returns:
            list: List of alerts
        """
        alerts = []
        
        # Check for low inventory items
        low_inventory = self.controller.db.execute_query(
            "SELECT COUNT(*) as count FROM inventory_items WHERE quantity <= min_quantity"
        )
        if low_inventory and low_inventory[0]["count"] > 0:
            alerts.append({
                "icon": ft.icons.WARNING_AMBER_ROUNDED,
                "color": ft.colors.AMBER,
                "message": f"{low_inventory[0]['count']} items are low in stock"
            })
        
        # Check for expiring items
        current_date = datetime.now().strftime("%Y-%m-%d")
        expiring_soon = self.controller.db.execute_query(
            "SELECT COUNT(*) as count FROM inventory_items WHERE expiry_date IS NOT NULL AND expiry_date <= date(?, '+30 days') AND expiry_date >= ?",
            (current_date, current_date)
        )
        if expiring_soon and expiring_soon[0]["count"] > 0:
            alerts.append({
                "icon": ft.icons.EVENT_BUSY,
                "color": ft.colors.ORANGE,
                "message": f"{expiring_soon[0]['count']} items are expiring within 30 days"
            })
        
        # Check for unresolved safety incidents
        safety_incidents = self.controller.db.execute_query(
            "SELECT COUNT(*) as count FROM safety_incidents WHERE status = 'open'"
        )
        if safety_incidents and safety_incidents[0]["count"] > 0:
            alerts.append({
                "icon": ft.icons.HEALTH_AND_SAFETY,
                "color": ft.colors.RED,
                "message": f"{safety_incidents[0]['count']} safety incidents require attention"
            })
        
        # Check for upcoming schedules today
        today_start = datetime.now().strftime("%Y-%m-%d 00:00:00")
        today_end = datetime.now().strftime("%Y-%m-%d 23:59:59")
        today_schedules = self.controller.db.execute_query(
            "SELECT COUNT(*) as count FROM lab_schedules WHERE start_time BETWEEN ? AND ?",
            (today_start, today_end)
        )
        if today_schedules and today_schedules[0]["count"] > 0:
            alerts.append({
                "icon": ft.icons.CALENDAR_TODAY,
                "color": ft.colors.BLUE,
                "message": f"{today_schedules[0]['count']} lab sessions scheduled for today"
            })
        
        return alerts
    
    def _format_timestamp(self, timestamp):
        """
        Format a timestamp for display.
        
        Args:
            timestamp (str): The timestamp to format
            
        Returns:
            str: Formatted timestamp
        """
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime("%m/%d/%Y %I:%M %p")
        except:
            return timestamp
    
    def _navigate_to_inventory(self):
        """Navigate to the inventory view."""
        self.controller.navigation_rail.selected_index = 1
        self.controller.navigation_change(ft.ControlEvent(
            self.controller.navigation_rail,
            data=None,
        ))
    
    def _navigate_to_experiments(self):
        """Navigate to the experiments view."""
        self.controller.navigation_rail.selected_index = 2
        self.controller.navigation_change(ft.ControlEvent(
            self.controller.navigation_rail,
            data=None,
        ))
    
    def _navigate_to_scheduling(self):
        """Navigate to the scheduling view."""
        self.controller.navigation_rail.selected_index = 3
        self.controller.navigation_change(ft.ControlEvent(
            self.controller.navigation_rail,
            data=None,
        ))
    
    def _navigate_to_safety(self):
        """Navigate to the safety view."""
        self.controller.navigation_rail.selected_index = 4
        self.controller.navigation_change(ft.ControlEvent(
            self.controller.navigation_rail,
            data=None,
        ))