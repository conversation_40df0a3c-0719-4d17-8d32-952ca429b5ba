import icalendar
import pytz
import requests
from datetime import datetime, timedelta

# Create a simple calendar event
cal = icalendar.Calendar()
cal.add('prodid', '-//Test Calendar//EN')
cal.add('version', '2.0')

event = icalendar.Event()
event.add('summary', 'Test Event')
event.add('dtstart', datetime.now())
event.add('dtend', datetime.now() + timedelta(hours=1))
event.add('dtstamp', datetime.now())
event.add('uid', '<EMAIL>')

cal.add_component(event)

print("Calendar created successfully!")
print(cal.to_ical().decode('utf-8')[:100] + "...")