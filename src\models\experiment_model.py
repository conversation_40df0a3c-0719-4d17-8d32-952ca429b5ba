import json
from datetime import datetime

class ExperimentModel:
    """
    Model for experiment-related database operations.
    Handles experiment data, measurements, and results tracking.
    """
    
    def __init__(self, db):
        """
        Initialize the experiment model.
        
        Args:
            db: Database instance
        """
        self.db = db
        self._ensure_tables_exist()
    
    def _ensure_tables_exist(self):
        """Ensure all required tables exist in the database."""
        # Create experiment_collaborators table if it doesn't exist
        self.db.execute_query('''
        CREATE TABLE IF NOT EXISTS experiment_collaborators (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            experiment_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            added_by INTEGER NOT NULL,
            added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (experiment_id) REFERENCES experiments (id),
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (added_by) REFERENCES users (id)
        )
        ''')
        
        # Create experiment_measurements table if it doesn't exist
        self.db.execute_query('''
        CREATE TABLE IF NOT EXISTS experiment_measurements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            experiment_id INTEGER NOT NULL,
            name TEXT NOT NULL,
            value REAL NOT NULL,
            unit TEXT,
            timestamp TIMESTAMP NOT NULL,
            notes TEXT,
            created_by INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (experiment_id) REFERENCES experiments (id),
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
        ''')
        
        # Create experiment_files table if it doesn't exist
        self.db.execute_query('''
        CREATE TABLE IF NOT EXISTS experiment_files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            experiment_id INTEGER NOT NULL,
            filename TEXT NOT NULL,
            file_path TEXT NOT NULL,
            file_type TEXT,
            file_size INTEGER,
            description TEXT,
            uploaded_by INTEGER NOT NULL,
            uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (experiment_id) REFERENCES experiments (id),
            FOREIGN KEY (uploaded_by) REFERENCES users (id)
        )
        ''')
        
        # Create experiment_comments table if it doesn't exist
        self.db.execute_query('''
        CREATE TABLE IF NOT EXISTS experiment_comments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            experiment_id INTEGER NOT NULL,
            comment_text TEXT NOT NULL,
            created_by INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (experiment_id) REFERENCES experiments (id),
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
        ''')
    
    def get_all_experiments(self, include_data=False):
        """
        Get all experiments.
        
        Args:
            include_data (bool): Whether to include experiment data
            
        Returns:
            list: List of all experiments
        """
        query = """
            SELECT e.*, u.full_name as creator_name
            FROM experiments e
            JOIN users u ON e.created_by = u.id
            ORDER BY e.last_updated DESC
        """
        
        experiments = self.db.execute_query(query)
        
        if not include_data:
            return experiments
        
        # Load experiment data for each experiment
        for experiment in experiments:
            experiment_data = self.get_experiment_data(experiment["id"])
            experiment["measurements"] = experiment_data.get("measurements", [])
            experiment["files"] = experiment_data.get("files", [])
            experiment["collaborators"] = self.get_experiment_collaborators(experiment["id"])
        
        return experiments
    
    def get_experiment_by_id(self, experiment_id, include_data=True):
        """
        Get an experiment by ID.
        
        Args:
            experiment_id (int): The experiment ID
            include_data (bool): Whether to include experiment data
            
        Returns:
            dict: Experiment data if found, None otherwise
        """
        query = """
            SELECT e.*, u.full_name as creator_name
            FROM experiments e
            JOIN users u ON e.created_by = u.id
            WHERE e.id = ?
        """
        
        experiments = self.db.execute_query(query, (experiment_id,))
        
        if not experiments:
            return None
        
        experiment = experiments[0]
        
        if include_data:
            # Load experiment data
            experiment_data = self.get_experiment_data(experiment_id)
            experiment["measurements"] = experiment_data.get("measurements", [])
            experiment["files"] = experiment_data.get("files", [])
            experiment["collaborators"] = self.get_experiment_collaborators(experiment_id)
        
        return experiment
    
    def get_experiments_by_user(self, user_id, include_collaborations=True):
        """
        Get experiments created by or collaborated on by a user.
        
        Args:
            user_id (int): The user ID
            include_collaborations (bool): Whether to include experiments the user collaborates on
            
        Returns:
            list: List of experiments
        """
        if include_collaborations:
            query = """
                SELECT DISTINCT e.*, u.full_name as creator_name
                FROM experiments e
                JOIN users u ON e.created_by = u.id
                LEFT JOIN experiment_collaborators ec ON e.id = ec.experiment_id
                WHERE e.created_by = ? OR ec.user_id = ?
                ORDER BY e.last_updated DESC
            """
            return self.db.execute_query(query, (user_id, user_id))
        else:
            query = """
                SELECT e.*, u.full_name as creator_name
                FROM experiments e
                JOIN users u ON e.created_by = u.id
                WHERE e.created_by = ?
                ORDER BY e.last_updated DESC
            """
            return self.db.execute_query(query, (user_id,))
    
    def create_experiment(self, experiment_data, user_id):
        """
        Create a new experiment.
        
        Args:
            experiment_data (dict): The experiment data
            user_id (int): ID of the user creating the experiment
            
        Returns:
            int: ID of the created experiment
        """
        # Extract basic experiment info
        title = experiment_data.get("title")
        description = experiment_data.get("description")
        status = experiment_data.get("status", "Draft")
        
        # Extract detailed experiment data
        details = {
            "objective": experiment_data.get("objective", ""),
            "materials": experiment_data.get("materials", ""),
            "procedure": experiment_data.get("procedure", ""),
            "observations": experiment_data.get("observations", ""),
            "results": experiment_data.get("results", ""),
            "conclusion": experiment_data.get("conclusion", ""),
            "measurements": experiment_data.get("measurements", []),
            "variables": experiment_data.get("variables", []),
            "metadata": experiment_data.get("metadata", {})
        }
        
        # Insert the experiment
        experiment_id = self.db.execute_insert(
            """
            INSERT INTO experiments (
                title, description, status, created_by, last_updated, data
            ) VALUES (?, ?, ?, ?, ?, ?)
            """,
            (
                title,
                description,
                status,
                user_id,
                datetime.now().isoformat(),
                json.dumps(details)
            )
        )
        
        # Add collaborators if provided
        collaborators = experiment_data.get("collaborators", [])
        for collaborator_id in collaborators:
            self.add_collaborator(experiment_id, collaborator_id, user_id)
        
        # Log the creation
        self.db.log_audit(
            user_id,
            "create",
            "experiment",
            experiment_id,
            f"Created experiment: {title}"
        )
        
        return experiment_id
    
    def update_experiment(self, experiment_id, experiment_data, user_id):
        """
        Update an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            experiment_data (dict): The updated experiment data
            user_id (int): ID of the user updating the experiment
            
        Returns:
            bool: True if update successful, False otherwise
        """
        # Check if experiment exists and user has permission
        experiment = self.get_experiment_by_id(experiment_id, include_data=False)
        if not experiment:
            return False
        
        if experiment["created_by"] != user_id and user_id not in self.get_experiment_collaborators(experiment_id):
            return False
        
        # Extract basic experiment info
        title = experiment_data.get("title")
        description = experiment_data.get("description")
        status = experiment_data.get("status")
        
        # Extract detailed experiment data
        current_data = {}
        if experiment["data"]:
            try:
                current_data = json.loads(experiment["data"])
            except:
                current_data = {}
        
        # Update the data with new values
        details = {
            "objective": experiment_data.get("objective", current_data.get("objective", "")),
            "materials": experiment_data.get("materials", current_data.get("materials", "")),
            "procedure": experiment_data.get("procedure", current_data.get("procedure", "")),
            "observations": experiment_data.get("observations", current_data.get("observations", "")),
            "results": experiment_data.get("results", current_data.get("results", "")),
            "conclusion": experiment_data.get("conclusion", current_data.get("conclusion", "")),
            "measurements": experiment_data.get("measurements", current_data.get("measurements", [])),
            "variables": experiment_data.get("variables", current_data.get("variables", [])),
            "metadata": experiment_data.get("metadata", current_data.get("metadata", {}))
        }
        
        # Update the experiment
        self.db.execute_query(
            """
            UPDATE experiments
            SET title = ?, description = ?, status = ?, last_updated = ?, data = ?
            WHERE id = ?
            """,
            (
                title,
                description,
                status,
                datetime.now().isoformat(),
                json.dumps(details),
                experiment_id
            )
        )
        
        # Log the update
        self.db.log_audit(
            user_id,
            "update",
            "experiment",
            experiment_id,
            f"Updated experiment: {title}"
        )
        
        return True
    
    def delete_experiment(self, experiment_id, user_id):
        """
        Delete an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            user_id (int): ID of the user deleting the experiment
            
        Returns:
            bool: True if deletion successful, False otherwise
        """
        # Check if experiment exists and user has permission
        experiment = self.get_experiment_by_id(experiment_id, include_data=False)
        if not experiment:
            return False
        
        if experiment["created_by"] != user_id:
            return False
        
        # Delete the experiment
        self.db.execute_query(
            "DELETE FROM experiments WHERE id = ?",
            (experiment_id,)
        )
        
        # Delete collaborators
        self.db.execute_query(
            "DELETE FROM experiment_collaborators WHERE experiment_id = ?",
            (experiment_id,)
        )
        
        # Delete measurements
        self.db.execute_query(
            "DELETE FROM experiment_measurements WHERE experiment_id = ?",
            (experiment_id,)
        )
        
        # Delete files
        self.db.execute_query(
            "DELETE FROM experiment_files WHERE experiment_id = ?",
            (experiment_id,)
        )
        
        # Delete comments
        self.db.execute_query(
            "DELETE FROM experiment_comments WHERE experiment_id = ?",
            (experiment_id,)
        )
        
        # Log the deletion
        self.db.log_audit(
            user_id,
            "delete",
            "experiment",
            experiment_id,
            f"Deleted experiment: {experiment['title']}"
        )
        
        return True
    
    def get_experiment_data(self, experiment_id):
        """
        Get detailed data for an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            
        Returns:
            dict: Experiment data
        """
        # Get the experiment
        experiment = self.db.execute_query(
            "SELECT data FROM experiments WHERE id = ?",
            (experiment_id,)
        )
        
        if not experiment or not experiment[0]["data"]:
            return {}
        
        try:
            return json.loads(experiment[0]["data"])
        except:
            return {}
    
    def add_measurement(self, experiment_id, measurement_data, user_id):
        """
        Add a measurement to an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            measurement_data (dict): The measurement data
            user_id (int): ID of the user adding the measurement
            
        Returns:
            int: ID of the created measurement
        """
        # Check if experiment exists and user has permission
        experiment = self.get_experiment_by_id(experiment_id, include_data=False)
        if not experiment:
            return None
        
        if experiment["created_by"] != user_id and user_id not in self.get_experiment_collaborators(experiment_id):
            return None
        
        # Insert the measurement
        measurement_id = self.db.execute_insert(
            """
            INSERT INTO experiment_measurements (
                experiment_id, name, value, unit, timestamp, notes, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """,
            (
                experiment_id,
                measurement_data.get("name"),
                measurement_data.get("value"),
                measurement_data.get("unit"),
                measurement_data.get("timestamp") or datetime.now().isoformat(),
                measurement_data.get("notes"),
                user_id
            )
        )
        
        # Log the addition
        self.db.log_audit(
            user_id,
            "create",
            "measurement",
            measurement_id,
            f"Added measurement to experiment {experiment_id}: {measurement_data.get('name')}"
        )
        
        return measurement_id
    
    def get_measurements(self, experiment_id):
        """
        Get all measurements for an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            
        Returns:
            list: List of measurements
        """
        return self.db.execute_query(
            """
            SELECT m.*, u.full_name as creator_name
            FROM experiment_measurements m
            JOIN users u ON m.created_by = u.id
            WHERE m.experiment_id = ?
            ORDER BY m.timestamp
            """,
            (experiment_id,)
        )
    
    def add_file(self, experiment_id, file_data, user_id):
        """
        Add a file to an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            file_data (dict): The file data
            user_id (int): ID of the user adding the file
            
        Returns:
            int: ID of the created file record
        """
        # Check if experiment exists and user has permission
        experiment = self.get_experiment_by_id(experiment_id, include_data=False)
        if not experiment:
            return None
        
        if experiment["created_by"] != user_id and user_id not in self.get_experiment_collaborators(experiment_id):
            return None
        
        # Insert the file record
        file_id = self.db.execute_insert(
            """
            INSERT INTO experiment_files (
                experiment_id, filename, file_path, file_type, file_size, description, uploaded_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """,
            (
                experiment_id,
                file_data.get("filename"),
                file_data.get("file_path"),
                file_data.get("file_type"),
                file_data.get("file_size"),
                file_data.get("description"),
                user_id
            )
        )
        
        # Log the addition
        self.db.log_audit(
            user_id,
            "create",
            "file",
            file_id,
            f"Added file to experiment {experiment_id}: {file_data.get('filename')}"
        )
        
        return file_id
    
    def get_files(self, experiment_id):
        """
        Get all files for an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            
        Returns:
            list: List of files
        """
        return self.db.execute_query(
            """
            SELECT f.*, u.full_name as uploader_name
            FROM experiment_files f
            JOIN users u ON f.uploaded_by = u.id
            WHERE f.experiment_id = ?
            ORDER BY f.uploaded_at DESC
            """,
            (experiment_id,)
        )
    
    def add_collaborator(self, experiment_id, collaborator_id, added_by):
        """
        Add a collaborator to an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            collaborator_id (int): ID of the user to add as collaborator
            added_by (int): ID of the user adding the collaborator
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Check if experiment exists and user has permission
        experiment = self.get_experiment_by_id(experiment_id, include_data=False)
        if not experiment:
            return False
        
        if experiment["created_by"] != added_by:
            return False
        
        # Check if already a collaborator
        existing = self.db.execute_query(
            "SELECT * FROM experiment_collaborators WHERE experiment_id = ? AND user_id = ?",
            (experiment_id, collaborator_id)
        )
        
        if existing:
            return True
        
        # Add the collaborator
        self.db.execute_insert(
            "INSERT INTO experiment_collaborators (experiment_id, user_id, added_by) VALUES (?, ?, ?)",
            (experiment_id, collaborator_id, added_by)
        )
        
        # Get collaborator name
        users = self.db.execute_query(
            "SELECT full_name FROM users WHERE id = ?",
            (collaborator_id,)
        )
        collaborator_name = users[0]["full_name"] if users else f"User {collaborator_id}"
        
        # Log the addition
        self.db.log_audit(
            added_by,
            "add",
            "collaborator",
            experiment_id,
            f"Added collaborator to experiment {experiment_id}: {collaborator_name}"
        )
        
        return True
    
    def remove_collaborator(self, experiment_id, collaborator_id, removed_by):
        """
        Remove a collaborator from an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            collaborator_id (int): ID of the user to remove as collaborator
            removed_by (int): ID of the user removing the collaborator
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Check if experiment exists and user has permission
        experiment = self.get_experiment_by_id(experiment_id, include_data=False)
        if not experiment:
            return False
        
        if experiment["created_by"] != removed_by:
            return False
        
        # Remove the collaborator
        self.db.execute_query(
            "DELETE FROM experiment_collaborators WHERE experiment_id = ? AND user_id = ?",
            (experiment_id, collaborator_id)
        )
        
        # Get collaborator name
        users = self.db.execute_query(
            "SELECT full_name FROM users WHERE id = ?",
            (collaborator_id,)
        )
        collaborator_name = users[0]["full_name"] if users else f"User {collaborator_id}"
        
        # Log the removal
        self.db.log_audit(
            removed_by,
            "remove",
            "collaborator",
            experiment_id,
            f"Removed collaborator from experiment {experiment_id}: {collaborator_name}"
        )
        
        return True
    
    def get_experiment_collaborators(self, experiment_id):
        """
        Get all collaborators for an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            
        Returns:
            list: List of collaborator user IDs
        """
        collaborators = self.db.execute_query(
            """
            SELECT c.user_id, u.full_name, u.username, u.email
            FROM experiment_collaborators c
            JOIN users u ON c.user_id = u.id
            WHERE c.experiment_id = ?
            """,
            (experiment_id,)
        )
        
        return collaborators
    
    def add_comment(self, experiment_id, comment_text, user_id):
        """
        Add a comment to an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            comment_text (str): The comment text
            user_id (int): ID of the user adding the comment
            
        Returns:
            int: ID of the created comment
        """
        # Check if experiment exists
        experiment = self.get_experiment_by_id(experiment_id, include_data=False)
        if not experiment:
            return None
        
        # Insert the comment
        comment_id = self.db.execute_insert(
            """
            INSERT INTO experiment_comments (
                experiment_id, comment_text, created_by
            ) VALUES (?, ?, ?)
            """,
            (
                experiment_id,
                comment_text,
                user_id
            )
        )
        
        # Log the addition
        self.db.log_audit(
            user_id,
            "create",
            "comment",
            comment_id,
            f"Added comment to experiment {experiment_id}"
        )
        
        return comment_id
    
    def get_comments(self, experiment_id):
        """
        Get all comments for an experiment.
        
        Args:
            experiment_id (int): The experiment ID
            
        Returns:
            list: List of comments
        """
        return self.db.execute_query(
            """
            SELECT c.*, u.full_name as creator_name
            FROM experiment_comments c
            JOIN users u ON c.created_by = u.id
            WHERE c.experiment_id = ?
            ORDER BY c.created_at
            """,
            (experiment_id,)
        )
    
    def export_experiment_data(self, experiment_id, format="json"):
        """
        Export experiment data in various formats.
        
        Args:
            experiment_id (int): The experiment ID
            format (str): The export format (json, csv, etc.)
            
        Returns:
            dict: The exported data
        """
        experiment = self.get_experiment_by_id(experiment_id, include_data=True)
        if not experiment:
            return None
        
        # For now, just return the experiment data in a structured format
        export_data = {
            "experiment": {
                "id": experiment["id"],
                "title": experiment["title"],
                "description": experiment["description"],
                "status": experiment["status"],
                "created_by": experiment["creator_name"],
                "created_at": experiment["created_at"],
                "last_updated": experiment["last_updated"],
            },
            "details": {}
        }
        
        # Add experiment details
        if experiment["data"]:
            try:
                data = json.loads(experiment["data"])
                export_data["details"] = {
                    "objective": data.get("objective", ""),
                    "materials": data.get("materials", ""),
                    "procedure": data.get("procedure", ""),
                    "observations": data.get("observations", ""),
                    "results": data.get("results", ""),
                    "conclusion": data.get("conclusion", ""),
                    "variables": data.get("variables", []),
                    "metadata": data.get("metadata", {})
                }
            except:
                pass
        
        # Add measurements
        export_data["measurements"] = self.get_measurements(experiment_id)
        
        # Add files
        export_data["files"] = self.get_files(experiment_id)
        
        # Add collaborators
        export_data["collaborators"] = self.get_experiment_collaborators(experiment_id)
        
        # Add comments
        export_data["comments"] = self.get_comments(experiment_id)
        
        return export_data