#!/usr/bin/env python
"""
Runner script for the Science Laboratory Management System.
"""
import os
import sys
import traceback
import logging

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("runner")

def check_environment():
    """Check the Python environment."""
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Python executable: {sys.executable}")
    logger.info(f"Current directory: {os.getcwd()}")
    
    # Check if required directories exist
    dirs_to_check = ["src", "config", "data", "src/auth", "src/database", "src/utils"]
    for dir_path in dirs_to_check:
        full_path = os.path.join(os.getcwd(), dir_path)
        exists = os.path.exists(full_path)
        logger.info(f"Directory {dir_path}: {'EXISTS' if exists else 'MISSING'}")
    
    # Check if required files exist
    files_to_check = [
        "lab_management_app_with_auth.py",
        "src/auth/auth_manager.py",
        "src/database/db_manager.py",
        "src/utils/logger.py",
        "config/app_config.json"
    ]
    for file_path in files_to_check:
        full_path = os.path.join(os.getcwd(), file_path)
        exists = os.path.exists(full_path)
        logger.info(f"File {file_path}: {'EXISTS' if exists else 'MISSING'}")
    
    # Check installed packages
    try:
        import flet
        import pkg_resources
        flet_version = pkg_resources.get_distribution("flet").version
        logger.info(f"Flet version: {flet_version}")
    except ImportError:
        logger.error("Flet package is not installed")
    except Exception as e:
        logger.error(f"Error getting Flet version: {str(e)}")
    
    try:
        import sqlite3
        logger.info(f"SQLite version: {sqlite3.sqlite_version}")
    except ImportError:
        logger.error("SQLite3 package is not installed")

def main():
    """Main function to run the application."""
    try:
        print("Starting Science Laboratory Management System...")
        
        # Check environment
        print("Checking environment...")
        check_environment()
        
        # Import the application
        print("Importing lab_management_app_with_auth...")
        import lab_management_app_with_auth
        
        # Run the application
        print("Running the application...")
        # Use the main function that doesn't require a page parameter
        lab_management_app_with_auth.main()
        
        print("Application exited successfully.")
    except Exception as e:
        print(f"Error running application: {str(e)}")
        print("Traceback:")
        traceback.print_exc()

if __name__ == "__main__":
    main()