class PermissionModel:
    """
    Model for permission-related database operations.
    """
    
    def __init__(self, db):
        """
        Initialize the permission model.
        
        Args:
            db: Database instance
        """
        self.db = db
    
    def get_all_permissions(self):
        """
        Get all permissions.
        
        Returns:
            list: List of all permissions
        """
        return self.db.execute_query("SELECT * FROM permissions")
    
    def get_permission_by_id(self, permission_id):
        """
        Get a permission by ID.
        
        Args:
            permission_id (int): The permission ID
            
        Returns:
            dict: Permission data if found, None otherwise
        """
        permissions = self.db.execute_query(
            "SELECT * FROM permissions WHERE id = ?",
            (permission_id,)
        )
        
        return permissions[0] if permissions else None
    
    def get_permission_by_name(self, name):
        """
        Get a permission by name.
        
        Args:
            name (str): The permission name
            
        Returns:
            dict: Permission data if found, None otherwise
        """
        permissions = self.db.execute_query(
            "SELECT * FROM permissions WHERE name = ?",
            (name,)
        )
        
        return permissions[0] if permissions else None
    
    def create_permission(self, name, description=None):
        """
        Create a new permission.
        
        Args:
            name (str): The permission name
            description (str, optional): The permission description
            
        Returns:
            int: ID of the created permission
        """
        return self.db.execute_insert(
            "INSERT INTO permissions (name, description) VALUES (?, ?)",
            (name, description)
        )
    
    def update_permission(self, permission_id, name=None, description=None):
        """
        Update a permission.
        
        Args:
            permission_id (int): The permission ID
            name (str, optional): The permission name
            description (str, optional): The permission description
            
        Returns:
            bool: True if update successful, False otherwise
        """
        # Build the update query
        query_parts = []
        params = []
        
        if name is not None:
            query_parts.append("name = ?")
            params.append(name)
        
        if description is not None:
            query_parts.append("description = ?")
            params.append(description)
        
        if not query_parts:
            return False
        
        query = f"UPDATE permissions SET {', '.join(query_parts)} WHERE id = ?"
        params.append(permission_id)
        
        # Execute the update
        self.db.execute_query(query, tuple(params))
        
        return True
    
    def delete_permission(self, permission_id):
        """
        Delete a permission.
        
        Args:
            permission_id (int): The permission ID
            
        Returns:
            bool: True if deletion successful, False otherwise
        """
        # Delete the permission
        self.db.execute_query(
            "DELETE FROM permissions WHERE id = ?",
            (permission_id,)
        )
        
        # Delete role-permission associations
        self.db.execute_query(
            "DELETE FROM role_permissions WHERE permission_id = ?",
            (permission_id,)
        )
        
        return True
    
    def get_role_permissions(self, role):
        """
        Get all permissions for a role.
        
        Args:
            role (str): The role
            
        Returns:
            list: List of permissions for the role
        """
        return self.db.execute_query(
            """
            SELECT p.* FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            WHERE rp.role = ?
            """,
            (role,)
        )
    
    def add_permission_to_role(self, role, permission_id):
        """
        Add a permission to a role.
        
        Args:
            role (str): The role
            permission_id (int): The permission ID
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Check if the association already exists
        existing = self.db.execute_query(
            "SELECT * FROM role_permissions WHERE role = ? AND permission_id = ?",
            (role, permission_id)
        )
        
        if existing:
            return True
        
        # Add the association
        self.db.execute_insert(
            "INSERT INTO role_permissions (role, permission_id) VALUES (?, ?)",
            (role, permission_id)
        )
        
        return True
    
    def remove_permission_from_role(self, role, permission_id):
        """
        Remove a permission from a role.
        
        Args:
            role (str): The role
            permission_id (int): The permission ID
            
        Returns:
            bool: True if successful, False otherwise
        """
        self.db.execute_query(
            "DELETE FROM role_permissions WHERE role = ? AND permission_id = ?",
            (role, permission_id)
        )
        
        return True
    
    def has_permission(self, user_id, permission_name, user_role=None):
        """
        Check if a user has a specific permission.
        
        Args:
            user_id (int): The user ID
            permission_name (str): The permission name
            user_role (str, optional): The user's role (if already known)
            
        Returns:
            bool: True if the user has the permission, False otherwise
        """
        # If role is not provided, get the user's role
        if not user_role:
            users = self.db.execute_query(
                "SELECT role FROM users WHERE id = ?",
                (user_id,)
            )
            
            if not users:
                return False
            
            user_role = users[0]['role']
        
        # Admin role has all permissions
        if user_role == 'admin':
            return True
        
        # Check if the permission exists
        permission = self.get_permission_by_name(permission_name)
        if not permission:
            return False
        
        # Check if the user's role has the permission
        role_permissions = self.get_role_permissions(user_role)
        return any(p['id'] == permission['id'] for p in role_permissions)
    
    def initialize_default_permissions(self):
        """
        Initialize default permissions and role-permission associations.
        
        Returns:
            bool: True if successful
        """
        # Define default permissions
        default_permissions = [
            ("view_dashboard", "View dashboard"),
            ("view_inventory", "View inventory items"),
            ("manage_inventory", "Manage inventory items"),
            ("view_experiments", "View experiments"),
            ("manage_experiments", "Manage experiments"),
            ("view_schedules", "View lab schedules"),
            ("manage_schedules", "Manage lab schedules"),
            ("view_safety", "View safety information"),
            ("manage_safety", "Manage safety information"),
            ("view_reports", "View reports"),
            ("export_data", "Export data"),
            ("view_users", "View user information"),
            ("manage_users", "Manage users"),
            ("view_audit_trail", "View audit trail"),
            ("manage_settings", "Manage system settings")
        ]
        
        # Create permissions if they don't exist
        for name, description in default_permissions:
            existing = self.get_permission_by_name(name)
            if not existing:
                self.create_permission(name, description)
        
        # Define role-permission mappings
        role_permissions = {
            "admin": [p[0] for p in default_permissions],
            "manager": [
                "view_dashboard", "view_inventory", "manage_inventory", 
                "view_experiments", "manage_experiments", "view_schedules", 
                "manage_schedules", "view_safety", "manage_safety", 
                "view_reports", "export_data", "view_users", "manage_users", 
                "view_audit_trail"
            ],
            "lab_technician": [
                "view_dashboard", "view_inventory", "manage_inventory", 
                "view_experiments", "manage_experiments", "view_schedules", 
                "view_safety", "manage_safety", "view_reports"
            ],
            "researcher": [
                "view_dashboard", "view_inventory", "view_experiments", 
                "manage_experiments", "view_schedules", "view_safety", 
                "view_reports"
            ],
            "student": [
                "view_dashboard", "view_inventory", "view_experiments", 
                "view_schedules", "view_safety"
            ],
            "guest": [
                "view_dashboard", "view_schedules"
            ]
        }
        
        # Assign permissions to roles
        for role, permissions in role_permissions.items():
            for permission_name in permissions:
                permission = self.get_permission_by_name(permission_name)
                if permission:
                    self.add_permission_to_role(role, permission['id'])
        
        return True