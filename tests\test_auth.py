"""
Unit tests for authentication functionality.
"""
import pytest
from datetime import datetime, timedelta
from src.auth.auth_manager import AuthManager


class TestAuthManager:
    """Test cases for AuthManager class."""
    
    def test_password_hashing(self, auth_manager):
        """Test password hashing and verification."""
        password = "TestPassword123!"
        
        # Test password hashing
        hashed = auth_manager.hash_password(password)
        assert hashed != password
        assert len(hashed) > 0
        
        # Test password verification
        assert auth_manager.verify_password(password, hashed) is True
        assert auth_manager.verify_password("wrongpassword", hashed) is False
    
    def test_user_login(self, auth_manager, sample_user_data):
        """Test user login functionality."""
        # Create a user first
        user_id, error = auth_manager.create_user(**sample_user_data)
        assert error is None
        assert user_id is not None
        
        # Test successful login
        success, user = auth_manager.login(
            sample_user_data["username"], 
            sample_user_data["password"]
        )
        assert success is True
        assert user is not None
        assert user["username"] == sample_user_data["username"]
        
        # Test failed login
        success, user = auth_manager.login(
            sample_user_data["username"], 
            "wrongpassword"
        )
        assert success is False
        assert user is None
    
    def test_user_logout(self, auth_manager, sample_user_data):
        """Test user logout functionality."""
        # Create and login user
        user_id, error = auth_manager.create_user(**sample_user_data)
        assert error is None
        
        success, user = auth_manager.login(
            sample_user_data["username"], 
            sample_user_data["password"]
        )
        assert success is True
        
        # Test logout
        auth_manager.logout()
        assert auth_manager.current_user is None
        assert auth_manager.is_authenticated() is False
    
    def test_session_management(self, auth_manager, sample_user_data):
        """Test session management."""
        # Create and login user
        user_id, error = auth_manager.create_user(**sample_user_data)
        assert error is None
        
        success, user = auth_manager.login(
            sample_user_data["username"], 
            sample_user_data["password"]
        )
        assert success is True
        
        # Check session is active
        assert auth_manager.is_authenticated() is True
        assert auth_manager.get_current_user() is not None
        
        # Test session timeout (simulate)
        if hasattr(auth_manager, 'session_start_time'):
            auth_manager.session_start_time = datetime.now() - timedelta(hours=2)
            assert auth_manager.is_session_valid() is False
    
    def test_password_complexity(self, auth_manager):
        """Test password complexity validation."""
        # Test weak passwords
        weak_passwords = [
            "123456",
            "password",
            "abc",
            "PASSWORD",
            "12345678"
        ]
        
        for password in weak_passwords:
            is_valid, message = auth_manager.check_password_complexity(password)
            assert is_valid is False
            assert len(message) > 0
        
        # Test strong passwords
        strong_passwords = [
            "StrongPassword123!",
            "MySecure@Pass1",
            "Complex#Password9"
        ]
        
        for password in strong_passwords:
            is_valid, message = auth_manager.check_password_complexity(password)
            assert is_valid is True
    
    def test_account_lockout(self, auth_manager, sample_user_data):
        """Test account lockout after failed attempts."""
        # Create user
        user_id, error = auth_manager.create_user(**sample_user_data)
        assert error is None
        
        # Simulate multiple failed login attempts
        for i in range(6):  # Assuming max attempts is 5
            success, user = auth_manager.login(
                sample_user_data["username"], 
                "wrongpassword"
            )
            assert success is False
        
        # Account should be locked now
        success, user = auth_manager.login(
            sample_user_data["username"], 
            sample_user_data["password"]  # Even correct password should fail
        )
        assert success is False
    
    def test_role_based_access(self, auth_manager):
        """Test role-based access control."""
        # Create users with different roles
        admin_data = {
            "username": "admin_test",
            "password": "AdminPassword123!",
            "email": "<EMAIL>",
            "full_name": "Admin Test",
            "role": "admin"
        }
        
        researcher_data = {
            "username": "researcher_test",
            "password": "ResearcherPassword123!",
            "email": "<EMAIL>",
            "full_name": "Researcher Test",
            "role": "researcher"
        }
        
        # Create users
        admin_id, error = auth_manager.create_user(**admin_data)
        assert error is None
        
        researcher_id, error = auth_manager.create_user(**researcher_data)
        assert error is None
        
        # Test admin permissions
        success, admin_user = auth_manager.login(admin_data["username"], admin_data["password"])
        assert success is True
        assert auth_manager.has_permission("admin") is True
        assert auth_manager.has_permission("researcher") is True
        
        # Test researcher permissions
        auth_manager.logout()
        success, researcher_user = auth_manager.login(researcher_data["username"], researcher_data["password"])
        assert success is True
        assert auth_manager.has_permission("researcher") is True
        assert auth_manager.has_permission("admin") is False
    
    def test_password_reset(self, auth_manager, sample_user_data):
        """Test password reset functionality."""
        # Create user
        user_id, error = auth_manager.create_user(**sample_user_data)
        assert error is None
        
        # Generate password reset token
        token = auth_manager.generate_password_reset_token(sample_user_data["email"])
        assert token is not None
        assert len(token) > 0
        
        # Reset password using token
        new_password = "NewPassword123!"
        success = auth_manager.reset_password_with_token(token, new_password)
        assert success is True
        
        # Test login with new password
        success, user = auth_manager.login(sample_user_data["username"], new_password)
        assert success is True
        
        # Test old password no longer works
        success, user = auth_manager.login(sample_user_data["username"], sample_user_data["password"])
        assert success is False
