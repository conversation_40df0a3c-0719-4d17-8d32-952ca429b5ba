#!/usr/bin/env python
"""
Script to verify flet installation and imports.
"""
import sys
import os

# Add the project root directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import flet as ft
    print(f"Flet imported successfully!")
    print(f"Flet module location: {ft.__file__}")
    # Create a simple Flet control to verify it works
    text = ft.Text("Hello, Flet!")
    print(f"Created a Flet Text control: {text}")
except ImportError as e:
    print(f"Error importing flet: {str(e)}")

# Try to import from the app controller
try:
    from src.controllers.app_controller import AppController
    print("AppController imported successfully!")
except ImportError as e:
    print(f"Error importing AppController: {str(e)}")