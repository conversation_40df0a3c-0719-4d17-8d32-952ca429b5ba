#!/usr/bin/env python
"""
Test script for the Integration Manager.
Run this from the integrations directory.
"""
import os
import sys

# Add the project root directory to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the IntegrationManager
from src.integrations.integration_manager import IntegrationManager

# Create a simple configuration
config = {
    "email": {
        "enabled": False
    }
}

try:
    # Initialize the manager
    print("Initializing IntegrationManager...")
    manager = IntegrationManager(config)
    
    # Get the status
    print("Getting component status...")
    status = manager.get_component_status()
    print("Integration Components Status:")
    print(status)
    
    # Shutdown the manager
    print("Shutting down IntegrationManager...")
    manager.shutdown()
    
    print("Test completed successfully!")
except Exception as e:
    print(f"Error during test: {str(e)}")
    import traceback
    traceback.print_exc()