import flet as ft
import json
from datetime import datetime

class ExperimentView:
    """
    Experiment documentation view for the Science Laboratory Management System.
    Allows users to create, view, and manage experiment documentation.
    """
    
    def __init__(self, controller):
        """
        Initialize the experiment view.
        
        Args:
            controller: The application controller
        """
        self.controller = controller
        self.experiments = []
        self.filtered_experiments = []
        
        # Create filter controls
        self.search_field = ft.TextField(
            label="Search",
            prefix_icon=ft.icons.SEARCH,
            on_change=self.filter_experiments,
            expand=True,
        )
        
        self.status_dropdown = ft.Dropdown(
            label="Status",
            on_change=self.filter_experiments,
            options=[
                ft.dropdown.Option("All"),
                ft.dropdown.Option("Draft"),
                ft.dropdown.Option("Active"),
                ft.dropdown.Option("Completed"),
                ft.dropdown.Option("Archived"),
            ],
            value="All",
            width=200,
        )
        
        # Create data table
        self.data_table = ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("Title")),
                ft.DataColumn(ft.Text("Status")),
                ft.DataColumn(ft.Text("Created By")),
                ft.DataColumn(ft.Text("Created At")),
                ft.DataColumn(ft.Text("Last Updated")),
                ft.DataColumn(ft.Text("Actions")),
            ],
            rows=[],
        )
        
        # Create form fields for adding/editing experiments
        self.experiment_id_field = ft.TextField(visible=False)
        self.title_field = ft.TextField(label="Title", required=True)
        self.description_field = ft.TextField(
            label="Description",
            multiline=True,
            min_lines=3,
            max_lines=5,
        )
        self.status_field = ft.Dropdown(
            label="Status",
            required=True,
            options=[
                ft.dropdown.Option("Draft"),
                ft.dropdown.Option("Active"),
                ft.dropdown.Option("Completed"),
                ft.dropdown.Option("Archived"),
            ],
        )
        
        # Create experiment data fields
        self.objective_field = ft.TextField(
            label="Objective",
            multiline=True,
            min_lines=2,
            max_lines=4,
        )
        
        self.materials_field = ft.TextField(
            label="Materials",
            multiline=True,
            min_lines=2,
            max_lines=4,
            hint_text="List materials used in the experiment",
        )
        
        self.procedure_field = ft.TextField(
            label="Procedure",
            multiline=True,
            min_lines=4,
            max_lines=8,
            hint_text="Describe the experimental procedure step by step",
        )
        
        self.observations_field = ft.TextField(
            label="Observations",
            multiline=True,
            min_lines=3,
            max_lines=6,
            hint_text="Record observations during the experiment",
        )
        
        self.results_field = ft.TextField(
            label="Results",
            multiline=True,
            min_lines=3,
            max_lines=6,
            hint_text="Document the results of the experiment",
        )
        
        self.conclusion_field = ft.TextField(
            label="Conclusion",
            multiline=True,
            min_lines=2,
            max_lines=4,
            hint_text="Summarize the findings and conclusions",
        )
        
        # Create dialog for adding/editing experiments
        self.experiment_dialog = ft.AlertDialog(
            title=ft.Text("Add Experiment"),
            content=ft.Column(
                [
                    self.experiment_id_field,
                    self.title_field,
                    self.description_field,
                    self.status_field,
                    ft.Divider(),
                    ft.Text("Experiment Details", weight=ft.FontWeight.BOLD),
                    self.objective_field,
                    self.materials_field,
                    self.procedure_field,
                    self.observations_field,
                    self.results_field,
                    self.conclusion_field,
                ],
                tight=True,
                spacing=10,
                scroll=ft.ScrollMode.AUTO,
                width=600,
                height=500,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=self.close_dialog),
                ft.TextButton("Save", on_click=self.save_experiment),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Create dialog for viewing experiment details
        self.view_dialog = ft.AlertDialog(
            title=ft.Text("Experiment Details"),
            content=ft.Column(
                [
                    ft.Text("Loading..."),
                ],
                scroll=ft.ScrollMode.AUTO,
                width=600,
                height=500,
            ),
            actions=[
                ft.TextButton("Close", on_click=self.close_view_dialog),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
    
    def build(self):
        """
        Build and return the experiment view.
        
        Returns:
            ft.Container: The experiment view container
        """
        # Load experiment data
        self.load_experiment_data()
        
        # Create add button
        add_button = ft.ElevatedButton(
            "New Experiment",
            icon=ft.icons.ADD,
            on_click=self.show_add_dialog,
        )
        
        # Create export button
        export_button = ft.ElevatedButton(
            "Export",
            icon=ft.icons.DOWNLOAD,
            on_click=self.export_experiments,
        )
        
        # Create header row
        header_row = ft.Row(
            [
                ft.Text("Experiment Documentation", size=24, weight=ft.FontWeight.BOLD),
                ft.Row([add_button, export_button]),
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        )
        
        # Create filters row
        filters_row = ft.Row(
            [
                self.search_field,
                self.status_dropdown,
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        )
        
        # Create data table container
        table_container = ft.Container(
            content=self.data_table,
            border=ft.border.all(1, ft.colors.GREY_300),
            border_radius=5,
            padding=10,
            expand=True,
        )
        
        # Return the complete view
        return ft.Container(
            content=ft.Column(
                [
                    header_row,
                    ft.Divider(),
                    filters_row,
                    ft.Container(height=10),
                    table_container,
                ],
                spacing=10,
                expand=True,
            ),
            padding=20,
            expand=True,
        )
    
    def load_experiment_data(self):
        """Load experiment data from the database."""
        # Query the database
        self.experiments = self.controller.db.execute_query("""
            SELECT e.*, u.full_name as creator_name
            FROM experiments e
            JOIN users u ON e.created_by = u.id
            ORDER BY e.last_updated DESC
        """)
        self.filtered_experiments = self.experiments.copy()
        
        # Update the data table
        self.update_data_table()
    
    def update_data_table(self):
        """Update the data table with filtered experiments."""
        # Clear existing rows
        self.data_table.rows.clear()
        
        # Add rows for filtered experiments
        for experiment in self.filtered_experiments:
            # Format dates
            created_at = self._format_timestamp(experiment["created_at"])
            last_updated = self._format_timestamp(experiment["last_updated"])
            
            # Get status color
            status_color = self._get_status_color(experiment["status"])
            
            # Create row
            self.data_table.rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(experiment["title"])),
                        ft.DataCell(ft.Text(experiment["status"], color=status_color)),
                        ft.DataCell(ft.Text(experiment["creator_name"])),
                        ft.DataCell(ft.Text(created_at)),
                        ft.DataCell(ft.Text(last_updated)),
                        ft.DataCell(
                            ft.Row(
                                [
                                    ft.IconButton(
                                        icon=ft.icons.VISIBILITY,
                                        tooltip="View",
                                        on_click=lambda e, exp_id=experiment["id"]: self.show_view_dialog(e, exp_id),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.EDIT,
                                        tooltip="Edit",
                                        on_click=lambda e, exp_id=experiment["id"]: self.show_edit_dialog(e, exp_id),
                                    ),
                                    ft.IconButton(
                                        icon=ft.icons.DELETE,
                                        tooltip="Delete",
                                        on_click=lambda e, exp_id=experiment["id"]: self.delete_experiment(e, exp_id),
                                    ),
                                ]
                            )
                        ),
                    ]
                )
            )
        
        # Update the page
        self.controller.page.update()
    
    def filter_experiments(self, e=None):
        """
        Filter experiments based on search and dropdown values.
        
        Args:
            e: The change event (optional)
        """
        search_text = self.search_field.value.lower() if self.search_field.value else ""
        status = self.status_dropdown.value
        
        # Filter experiments
        self.filtered_experiments = []
        for experiment in self.experiments:
            # Apply search filter
            if search_text and search_text not in experiment["title"].lower() and search_text not in (experiment["description"] or "").lower():
                continue
            
            # Apply status filter
            if status != "All" and experiment["status"] != status:
                continue
            
            # Add experiment to filtered list
            self.filtered_experiments.append(experiment)
        
        # Update the data table
        self.update_data_table()
    
    def show_add_dialog(self, e):
        """
        Show dialog for adding a new experiment.
        
        Args:
            e: The click event
        """
        # Clear form fields
        self.experiment_id_field.value = ""
        self.title_field.value = ""
        self.description_field.value = ""
        self.status_field.value = "Draft"
        self.objective_field.value = ""
        self.materials_field.value = ""
        self.procedure_field.value = ""
        self.observations_field.value = ""
        self.results_field.value = ""
        self.conclusion_field.value = ""
        
        # Set dialog title
        self.experiment_dialog.title = ft.Text("Add Experiment")
        
        # Show the dialog
        self.controller.page.dialog = self.experiment_dialog
        self.experiment_dialog.open = True
        self.controller.page.update()
    
    def show_edit_dialog(self, e, experiment_id):
        """
        Show dialog for editing an experiment.
        
        Args:
            e: The click event
            experiment_id: ID of the experiment to edit
        """
        # Find the experiment
        experiment = next((exp for exp in self.experiments if exp["id"] == experiment_id), None)
        
        if not experiment:
            return
        
        # Set form fields
        self.experiment_id_field.value = str(experiment["id"])
        self.title_field.value = experiment["title"]
        self.description_field.value = experiment["description"] or ""
        self.status_field.value = experiment["status"]
        
        # Parse experiment data JSON
        data = {}
        if experiment["data"]:
            try:
                data = json.loads(experiment["data"])
            except:
                data = {}
        
        # Set experiment data fields
        self.objective_field.value = data.get("objective", "")
        self.materials_field.value = data.get("materials", "")
        self.procedure_field.value = data.get("procedure", "")
        self.observations_field.value = data.get("observations", "")
        self.results_field.value = data.get("results", "")
        self.conclusion_field.value = data.get("conclusion", "")
        
        # Set dialog title
        self.experiment_dialog.title = ft.Text("Edit Experiment")
        
        # Show the dialog
        self.controller.page.dialog = self.experiment_dialog
        self.experiment_dialog.open = True
        self.controller.page.update()
    
    def show_view_dialog(self, e, experiment_id):
        """
        Show dialog for viewing experiment details.
        
        Args:
            e: The click event
            experiment_id: ID of the experiment to view
        """
        # Find the experiment
        experiment = next((exp for exp in self.experiments if exp["id"] == experiment_id), None)
        
        if not experiment:
            return
        
        # Parse experiment data JSON
        data = {}
        if experiment["data"]:
            try:
                data = json.loads(experiment["data"])
            except:
                data = {}
        
        # Format dates
        created_at = self._format_timestamp(experiment["created_at"])
        last_updated = self._format_timestamp(experiment["last_updated"])
        
        # Create view content
        content = ft.Column(
            [
                ft.Text(experiment["title"], size=20, weight=ft.FontWeight.BOLD),
                ft.Text(f"Status: {experiment['status']}", color=self._get_status_color(experiment["status"])),
                ft.Text(f"Created by: {experiment['creator_name']}"),
                ft.Text(f"Created: {created_at}"),
                ft.Text(f"Last updated: {last_updated}"),
                ft.Divider(),
                
                ft.Text("Description", weight=ft.FontWeight.BOLD),
                ft.Text(experiment["description"] or "No description provided"),
                ft.Container(height=10),
                
                ft.Text("Objective", weight=ft.FontWeight.BOLD),
                ft.Text(data.get("objective", "Not specified")),
                ft.Container(height=10),
                
                ft.Text("Materials", weight=ft.FontWeight.BOLD),
                ft.Text(data.get("materials", "Not specified")),
                ft.Container(height=10),
                
                ft.Text("Procedure", weight=ft.FontWeight.BOLD),
                ft.Text(data.get("procedure", "Not specified")),
                ft.Container(height=10),
                
                ft.Text("Observations", weight=ft.FontWeight.BOLD),
                ft.Text(data.get("observations", "Not specified")),
                ft.Container(height=10),
                
                ft.Text("Results", weight=ft.FontWeight.BOLD),
                ft.Text(data.get("results", "Not specified")),
                ft.Container(height=10),
                
                ft.Text("Conclusion", weight=ft.FontWeight.BOLD),
                ft.Text(data.get("conclusion", "Not specified")),
            ],
            scroll=ft.ScrollMode.AUTO,
            spacing=5,
        )
        
        # Update dialog content
        self.view_dialog.title = ft.Text("Experiment Details")
        self.view_dialog.content = content
        
        # Show the dialog
        self.controller.page.dialog = self.view_dialog
        self.view_dialog.open = True
        self.controller.page.update()
    
    def close_dialog(self, e):
        """
        Close the experiment dialog.
        
        Args:
            e: The click event
        """
        self.experiment_dialog.open = False
        self.controller.page.update()
    
    def close_view_dialog(self, e):
        """
        Close the view dialog.
        
        Args:
            e: The click event
        """
        self.view_dialog.open = False
        self.controller.page.update()
    
    def save_experiment(self, e):
        """
        Save the experiment.
        
        Args:
            e: The click event
        """
        # Validate required fields
        if not self.title_field.value or not self.status_field.value:
            # Show error message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Please fill in all required fields"),
                bgcolor=ft.colors.RED_400,
            )
            self.controller.page.snack_bar.open = True
            self.controller.page.update()
            return
        
        # Prepare experiment data
        experiment_data = {
            "title": self.title_field.value,
            "description": self.description_field.value if self.description_field.value else None,
            "status": self.status_field.value,
        }
        
        # Prepare experiment details JSON
        details = {
            "objective": self.objective_field.value,
            "materials": self.materials_field.value,
            "procedure": self.procedure_field.value,
            "observations": self.observations_field.value,
            "results": self.results_field.value,
            "conclusion": self.conclusion_field.value,
        }
        
        experiment_data["data"] = json.dumps(details)
        
        # Add or update experiment
        if self.experiment_id_field.value:
            # Update existing experiment
            experiment_id = int(self.experiment_id_field.value)
            
            # Build the update query
            query_parts = []
            params = []
            
            for key, value in experiment_data.items():
                query_parts.append(f"{key} = ?")
                params.append(value)
            
            query = f"UPDATE experiments SET {', '.join(query_parts)}, last_updated = ? WHERE id = ?"
            params.append(datetime.now().isoformat())
            params.append(experiment_id)
            
            # Execute the update
            self.controller.db.execute_query(query, tuple(params))
            
            # Log the update
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "update",
                "experiment",
                experiment_id,
                f"Updated experiment: {experiment_data['title']}"
            )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Experiment updated successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
        else:
            # Add new experiment
            experiment_data["created_by"] = self.controller.current_user["id"]
            experiment_data["last_updated"] = datetime.now().isoformat()
            
            # Build the insert query
            keys = list(experiment_data.keys())
            placeholders = ["?"] * len(keys)
            
            query = f"INSERT INTO experiments ({', '.join(keys)}) VALUES ({', '.join(placeholders)})"
            params = tuple(experiment_data.values())
            
            # Execute the insert
            experiment_id = self.controller.db.execute_insert(query, params)
            
            # Log the insert
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "create",
                "experiment",
                experiment_id,
                f"Added new experiment: {experiment_data['title']}"
            )
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Experiment added successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
        
        # Close the dialog
        self.experiment_dialog.open = False
        
        # Reload experiment data
        self.load_experiment_data()
        
        # Update the page
        self.controller.page.snack_bar.open = True
        self.controller.page.update()
    
    def delete_experiment(self, e, experiment_id):
        """
        Delete an experiment.
        
        Args:
            e: The click event
            experiment_id: ID of the experiment to delete
        """
        # Find the experiment
        experiment = next((exp for exp in self.experiments if exp["id"] == experiment_id), None)
        
        if not experiment:
            return
        
        # Show confirmation dialog
        def confirm_delete(e):
            # Delete the experiment
            self.controller.db.execute_query(
                "DELETE FROM experiments WHERE id = ?",
                (experiment_id,)
            )
            
            # Log the delete
            self.controller.db.log_audit(
                self.controller.current_user["id"],
                "delete",
                "experiment",
                experiment_id,
                f"Deleted experiment: {experiment['title']}"
            )
            
            # Close the dialog
            confirm_dialog.open = False
            
            # Show success message
            self.controller.page.snack_bar = ft.SnackBar(
                content=ft.Text("Experiment deleted successfully"),
                bgcolor=ft.colors.GREEN_400,
            )
            self.controller.page.snack_bar.open = True
            
            # Reload experiment data
            self.load_experiment_data()
            
            # Update the page
            self.controller.page.update()
        
        # Create confirmation dialog
        confirm_dialog = ft.AlertDialog(
            title=ft.Text("Confirm Delete"),
            content=ft.Text(f"Are you sure you want to delete '{experiment['title']}'?"),
            actions=[
                ft.TextButton("Cancel", on_click=lambda e: setattr(confirm_dialog, "open", False)),
                ft.TextButton("Delete", on_click=confirm_delete),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = confirm_dialog
        confirm_dialog.open = True
        self.controller.page.update()
    
    def export_experiments(self, e):
        """
        Export experiment data.
        
        Args:
            e: The click event
        """
        # Create a download link
        download_link = ft.Text(
            "Click here to download",
            color=ft.colors.BLUE,
            style=ft.TextStyle(decoration=ft.TextDecoration.UNDERLINE),
        )
        
        # Create dialog
        export_dialog = ft.AlertDialog(
            title=ft.Text("Export Experiments"),
            content=ft.Column(
                [
                    ft.Text("Your experiment data is ready to export."),
                    download_link,
                ],
                tight=True,
                spacing=10,
            ),
            actions=[
                ft.TextButton("Close", on_click=lambda e: setattr(export_dialog, "open", False)),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        # Show the dialog
        self.controller.page.dialog = export_dialog
        export_dialog.open = True
        self.controller.page.update()
        
        # In a real application, we would save the data to a file and provide a download link
        # For this example, we'll just show a dialog with instructions
    
    def _format_timestamp(self, timestamp):
        """
        Format a timestamp for display.
        
        Args:
            timestamp (str): The timestamp to format
            
        Returns:
            str: Formatted timestamp
        """
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime("%m/%d/%Y %I:%M %p")
        except:
            return timestamp
    
    def _get_status_color(self, status):
        """
        Get the color for a status.
        
        Args:
            status (str): The status
            
        Returns:
            str: The color for the status
        """
        if status == "Draft":
            return ft.colors.GREY
        elif status == "Active":
            return ft.colors.GREEN
        elif status == "Completed":
            return ft.colors.BLUE
        elif status == "Archived":
            return ft.colors.BROWN
        else:
            return ft.colors.BLACK